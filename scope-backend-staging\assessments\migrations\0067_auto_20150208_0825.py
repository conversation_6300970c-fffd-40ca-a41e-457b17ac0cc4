from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0066_auto_20150205_2006")]

    operations = [
        migrations.AddField(
            model_name="landuse",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="totallanduse",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="land_used_by_members_outgrowers",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="producingorganizationdetails2",
                null=True,
                blank=True,
                to="assessments.TotalLandUse",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="land_used_by_producing_organization",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="producingorganizationdetails1",
                null=True,
                blank=True,
                to="assessments.TotalLandUse",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="totallanduse",
            name="leased_rented",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="totallanduse2",
                null=True,
                blank=True,
                to="assessments.LandUse",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="totallanduse",
            name="owned",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="totallanduse1",
                null=True,
                blank=True,
                to="assessments.LandUse",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="totallanduse",
            name="used_otherwise",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="totallanduse3",
                null=True,
                blank=True,
                to="assessments.LandUse",
            ),
            preserve_default=True,
        ),
    ]
