from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0065_auto_20150205_1023")]

    operations = [
        migrations.CreateModel(
            name="LandUse",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("area", models.DecimalField(max_digits=10, decimal_places=2)),
                ("unit", models.TextField()),
                (
                    "percent_irrigated",
                    models.DecimalField(
                        null=True, max_digits=5, decimal_places=2, blank=True
                    ),
                ),
                (
                    "percent_productive_use",
                    models.DecimalField(
                        null=True, max_digits=5, decimal_places=2, blank=True
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="TotalLandUse",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "total_area",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=10,
                        decimal_places=2,
                        blank=True,
                    ),
                ),
                ("unit", models.TextField(editable=False, blank=True)),
                (
                    "leased_rented",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.LandUse",
                        null=True,
                    ),
                ),
                (
                    "owned",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.LandUse",
                        null=True,
                    ),
                ),
                (
                    "used_otherwise",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.LandUse",
                        null=True,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="land_used_by_members_outgrowers",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                to="assessments.TotalLandUse",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="land_used_by_producing_organization",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                to="assessments.TotalLandUse",
                null=True,
            ),
            preserve_default=True,
        ),
    ]
