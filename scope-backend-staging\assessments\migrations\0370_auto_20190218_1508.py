# Generated by Django 2.1.7 on 2019-02-18 15:08

import djmoney.models.fields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [("assessments", "0369_auto_20190215_1426")]

    operations = [
        migrations.AlterField(
            model_name="agentincome",
            name="income",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, default=None, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="capitalrequirement",
            name="amount",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, default=None, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="granthistory",
            name="amount",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, default=None, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="prefinancehistory",
            name="amount",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, default=None, max_digits=20, null=True
            ),
        ),
    ]
