from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0039_auto_20150206_1356"),
        ("assessments", "0078_auto_20150209_1403"),
    ]

    operations = [
        migrations.AddField(
            model_name="response",
            name="question_description",
            field=models.TextField(editable=False, blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="response",
            name="question_explanation",
            field=models.TextField(editable=False, blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="response",
            name="question_scale_type",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                blank=True,
                editable=False,
                to="products.ScaleType",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="response",
            name="question_title",
            field=models.TextField(editable=False, blank=True),
            preserve_default=True,
        ),
    ]
