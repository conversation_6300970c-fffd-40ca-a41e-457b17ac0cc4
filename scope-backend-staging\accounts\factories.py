import factory
import factory.fuzzy
from django.conf import settings
from django.contrib.auth.models import Group
from factory.django import DjangoModelFactory

from .models import PasswordResetRequest, User


class UserFactory(DjangoModelFactory):
    email = factory.Faker("email")
    username = factory.SelfAttribute("email")
    organization = factory.SubFactory("customers.factories.CustomerFactory")
    country = factory.Faker("country")
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")
    language = factory.Faker(
        "word", ext_word_list=[item[0] for item in settings.LANGUAGES]
    )
    city = factory.Faker("city")
    global_region = factory.Faker("city_prefix")
    region = factory.Faker("city_prefix")
    region_iso = factory.Faker("city_prefix")
    languages_spoken = factory.Faker(
        "random_sample", elements=[item[0] for item in settings.LANGUAGES]
    )
    allowed_frontends = "blub"
    email_notifications_on = factory.Faker("boolean")
    phone_number = factory.Faker("phone_number")
    second_region = factory.Faker("city_prefix")
    street = factory.Faker("street_name")
    street_number = factory.Faker("numerify")
    zipcode = factory.Faker("zipcode")
    display_in_list = True

    @factory.post_generation
    def groups(self, created, extracted, **kwargs):
        if created and extracted:
            self.groups.set(extracted)

    @factory.post_generation
    def password(self, created, extracted, **kwargs):
        if created:
            if extracted:
                password = extracted
            else:
                password = factory.Faker("word").generate({})
            self.set_password(password)

    class Meta:
        model = User


class PasswordResetRequestFactory(DjangoModelFactory):
    user = factory.SubFactory(UserFactory)

    class Meta:
        model = PasswordResetRequest


class GroupFactory(DjangoModelFactory):
    name = factory.fuzzy.FuzzyText()

    class Meta:
        model = Group
