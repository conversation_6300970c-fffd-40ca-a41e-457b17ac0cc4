import base64
import datetime
import json
from shutil import rmtree
from tempfile import mkdtemp

import pytz
from django.conf import settings
from freezegun import freeze_time
from rest_framework import status
from rest_framework.reverse import reverse

from libs.test_helpers import (
    AssessorJWTTestCase,
    ContactJWTTestCase,
    CustomerAdminJWTTestCase,
    CustomerUserJWTTestCase,
    DenormMixin,
    EmployeeJWTTestCase,
    QualityReviewerJWTTestCase,
)

from .factories import BlogFactory, BlogSetFactory
from .models import Blog, BlogSet


class EmployeeTestCase(DenormMixin, EmployeeJWTTestCase):
    def tearDown(self):
        dirs_to_delete = getattr(self, "dirs_to_delete", [])
        for dirname in set(dirs_to_delete):
            rmtree(dirname)

    def test_can_create_blogset(self):
        """
        Employee can create blogset
        """
        url = reverse("blogs:blogset-list")
        data = {}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_201_CREATED, response.status_code)
        self.assertEqual(1, BlogSet.objects.count())

    def test_can_get_blogset(self):
        """
        Employee can get blogset
        """
        BlogSetFactory.create_batch(5)
        url = reverse("blogs:blogset-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertIn("count", response_dict)
        self.assertEqual(5, response_dict["count"])

    def test_can_create_blogs(self):
        """
        Employee can create blogs
        """
        url = reverse("blogs:blog-list")
        blog_set = BlogSetFactory.create()

        data = {
            "blog_set": reverse("blogs:blogset-detail", [blog_set.pk]),
            "language": "en",
            "subject": "Proximity is power",
            "body": "Proximity is power. Proximity is power. Proximity is power.",
        }
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        response_dict = json.loads(response.content)
        self.assertEqual(status.HTTP_201_CREATED, response.status_code)
        self.assertEqual("Proximity is power", response_dict["subject"])
        self.assertEqual(
            "Proximity is power. Proximity is power. Proximity is power.",
            response_dict["body"],
        )
        self.assertEqual("en", response_dict["language"])
        self.assertEqual(
            "http://testserver" + reverse("blogs:blog-detail", [response_dict["id"]]),
            response_dict["url"],
        )
        self.assertEqual(
            "http://testserver" + reverse("blogs:blogset-detail", [blog_set.pk]),
            response_dict["blog_set"],
        )

    def test_images_are_extracted(self):
        """
        When creating a blog, images need to be extracted and replaced with urls
        """
        temp_media_dir = mkdtemp(dir=settings.BASE_DIR)
        self.dirs_to_delete = [temp_media_dir]
        with self.settings(MEDIA_ROOT=temp_media_dir):
            url = reverse("blogs:blog-list")
            blog_set = BlogSetFactory.create()

            base64image = "/9j/4AAQSkZJRgABAQEASABIAAD/2wBDAP//////////////////////////////////////////////////////////////////////////////////////wgALCAABAAEBAREA/8QAFBABAAAAAAAAAAAAAAAAAAAAAP/aAAgBAQABPxA="
            data = {
                "blog_set": reverse("blogs:blogset-detail", [blog_set.pk]),
                "language": "en",
                "subject": "With image",
                "body": '<p><img src="data:image/jpeg;base64,{}"></p>'.format(
                    base64image
                ),
            }
            response = self.client.post(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(status.HTTP_201_CREATED, response.status_code)
            blog = Blog.objects.get()
            self.assertEqual(1, blog.images.count())
            blogimage = blog.images.get()
            self.assertEqual(
                base64image, base64.b64encode(blogimage.file.read()).decode()
            )
            self.assertNotIn(base64image, blog.body)
            self.assertIn(
                'img src="http://testserver{}"'.format(blogimage.file.url), blog.body
            )
            self.assertTrue(blog.body.startswith("<p>"))

    def test_can_patch_blogs(self):
        """
        Employee can patch blogs
        """
        blog_set = BlogSetFactory.create()

        blog = BlogFactory.create(
            blog_set=blog_set,
            language="en",
            subject="Proximity is power",
            body="Proximity is power. Proximity is power. Proximity is power.",
        )

        url = reverse("blogs:blog-detail", [blog.pk])
        data = {"subject": "How to Win Friends and Influence People"}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        response_dict = json.loads(response.content)
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual(
            "How to Win Friends and Influence People", response_dict["subject"]
        )

    def test_can_delete_blogs(self):
        """
        Employee can delete blogs
        """
        blog_set = BlogSetFactory.create()

        blog = BlogFactory.create(
            blog_set=blog_set,
            language="en",
            subject="Proximity is power",
            body="Proximity is power. Proximity is power. Proximity is power.",
        )

        url = reverse("blogs:blog-detail", [blog.pk])
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(status.HTTP_204_NO_CONTENT, response.status_code)
        self.assertEqual(0, Blog.objects.count())

    def test_can_delete_blogsets(self):
        """
        Employee can delete blogs
        """
        blog_set = BlogSetFactory.create()

        url = reverse("blogs:blogset-detail", [blog_set.pk])
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(status.HTTP_204_NO_CONTENT, response.status_code)
        self.assertEqual(0, BlogSet.objects.count())

    def test_can_get_blog_detail(self):
        """
        Employee can get blog
        """
        blog_set = BlogSetFactory.create()
        blog = BlogFactory.create(
            blog_set=blog_set,
            language="en",
            subject="Proximity is power",
            body="Proximity is power. Proximity is power. Proximity is power.",
        )
        url = reverse("blogs:blog-detail", [blog.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual("Proximity is power", response_dict["subject"])
        self.assertEqual(
            "Proximity is power. Proximity is power. Proximity is power.",
            response_dict["body"],
        )
        self.assertEqual("en", response_dict["language"])

    def test_can_mark_blog_as_read(self):
        """
        Employee can mark blogs as read
        """
        me = self.jwt_user
        blog_set = BlogSetFactory.create()
        url = reverse("blogs:blogset-mark-as-read", [blog_set.pk])
        data = {}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        blog_set.refresh_from_db()
        self.assertIn(me, blog_set.read_by.all())

    def test_mark_as_read_triggers_200(self):
        """
        Marking as read should trigger 200 on subsequent list get
        """
        create_time = datetime.datetime(2018, 4, 6, 12, 0, 0, 0, pytz.utc)
        get_time = create_time + datetime.timedelta(seconds=1)
        mark_time = get_time + datetime.timedelta(seconds=1)
        reget_time = mark_time + datetime.timedelta(seconds=1)
        with freeze_time(create_time):
            blog_set = BlogSetFactory.create()
        url = reverse("blogs:blogset-mark-as-read", [blog_set.pk])
        data = {}
        with freeze_time(mark_time):
            self.client.post(url, json.dumps(data), content_type="application/json")
        url = reverse("blogs:blogset-list")
        with freeze_time(reget_time):
            response = self.client.get(
                url,
                content_type="application/json",
                HTTP_IF_MODIFIED_SINCE=(get_time.strftime("%a, %d %b %Y %H:%M:%S GMT")),
            )
            self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_mark_as_unread_triggers_200(self):
        """
        Marking as unread should trigger 200 on subsequent list get
        """
        create_time = datetime.datetime(2018, 4, 6, 12, 0, 0, 0, pytz.utc)
        get_time = create_time + datetime.timedelta(seconds=1)
        mark_time = get_time + datetime.timedelta(seconds=1)
        reget_time = mark_time + datetime.timedelta(seconds=1)
        with freeze_time(create_time):
            blog_set = BlogSetFactory.create()
        url = reverse("blogs:blogset-mark-as-unread", [blog_set.pk])
        data = {}
        with freeze_time(mark_time):
            self.client.post(url, json.dumps(data), content_type="application/json")
        url = reverse("blogs:blogset-list")
        with freeze_time(reget_time):
            response = self.client.get(
                url,
                content_type="application/json",
                HTTP_IF_MODIFIED_SINCE=(get_time.strftime("%a, %d %b %Y %H:%M:%S GMT")),
            )
            self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_mark_as_archived_triggers_200(self):
        """
        Marking as archived should trigger 200 on subsequent list get
        """
        create_time = datetime.datetime(2018, 4, 6, 12, 0, 0, 0, pytz.utc)
        get_time = create_time + datetime.timedelta(seconds=1)
        mark_time = get_time + datetime.timedelta(seconds=1)
        reget_time = mark_time + datetime.timedelta(seconds=1)
        with freeze_time(create_time):
            blog_set = BlogSetFactory.create()
        url = reverse("blogs:blogset-mark-as-archived", [blog_set.pk])
        data = {}
        with freeze_time(mark_time):
            self.client.post(url, json.dumps(data), content_type="application/json")
        url = reverse("blogs:blogset-list")
        with freeze_time(reget_time):
            response = self.client.get(
                url,
                content_type="application/json",
                HTTP_IF_MODIFIED_SINCE=(get_time.strftime("%a, %d %b %Y %H:%M:%S GMT")),
            )
            self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_mark_as_unarchived_triggers_200(self):
        """
        Marking as unarchived should trigger 200 on subsequent list get
        """
        create_time = datetime.datetime(2018, 4, 6, 12, 0, 0, 0, pytz.utc)
        get_time = create_time + datetime.timedelta(seconds=1)
        mark_time = get_time + datetime.timedelta(seconds=1)
        reget_time = mark_time + datetime.timedelta(seconds=1)
        with freeze_time(create_time):
            blog_set = BlogSetFactory.create()
        url = reverse("blogs:blogset-mark-as-unarchived", [blog_set.pk])
        data = {}
        with freeze_time(mark_time):
            self.client.post(url, json.dumps(data), content_type="application/json")
        url = reverse("blogs:blogset-list")
        with freeze_time(reget_time):
            response = self.client.get(
                url,
                content_type="application/json",
                HTTP_IF_MODIFIED_SINCE=(get_time.strftime("%a, %d %b %Y %H:%M:%S GMT")),
            )
            self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_mark_blog_as_archived(self):
        """
        Employee can mark blogs as archived
        """
        me = self.jwt_user
        blog_set = BlogSetFactory.create()
        url = reverse("blogs:blogset-mark-as-archived", [blog_set.pk])
        data = {}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        blog_set.refresh_from_db()
        self.assertIn(me, blog_set.archived_by.all())


class AssessorTestCase(DenormMixin, AssessorJWTTestCase):
    def test_cannot_create_blogset(self):
        """
        Assessor can create blogset
        """
        url = reverse("blogs:blogset-list")
        data = {}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)
        self.assertEqual(0, BlogSet.objects.count())

    def test_can_get_blogset(self):
        BlogSetFactory.create_batch(5)
        url = reverse("blogs:blogset-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertIn("count", response_dict)
        self.assertEqual(5, response_dict["count"])

    def test_cannot_create_blogs(self):
        """
        Assessor cannot create blogs
        """
        url = reverse("blogs:blog-list")
        blog_set = BlogSetFactory.create()

        data = {
            "blog_set": reverse("blogs:blogset-detail", [blog_set.pk]),
            "language": "en",
            "subject": "Proximity is power",
            "body": "Proximity is power. Proximity is power. Proximity is power.",
        }
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)
        self.assertEqual(0, Blog.objects.count())

    def test_can_get_blogs(self):
        """
        Assessor can get blogs
        """
        BlogFactory.create_batch(5)
        url = reverse("blogs:blog-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertIn("count", response_dict)
        self.assertEqual(5, response_dict["count"])
        self.assertEqual(5, Blog.objects.count())

    def test_can_get_blog_detail(self):
        """
        Assessor can get blogs
        """
        blog_set = BlogSetFactory.create()
        blog = BlogFactory.create(
            blog_set=blog_set,
            language="en",
            subject="Proximity is power",
            body="Proximity is power. Proximity is power. Proximity is power.",
        )
        url = reverse("blogs:blog-detail", [blog.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_mark_blog_as_read(self):
        """
        Assessor can mark blogs as read
        """
        me = self.jwt_user
        blog_set = BlogSetFactory.create()
        url = reverse("blogs:blogset-mark-as-read", [blog_set.pk])
        data = {}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        blog_set.refresh_from_db()
        self.assertIn(me, blog_set.read_by.all())

    def test_can_mark_blog_as_archived(self):
        """
        Assessor can mark blogs as archived
        """
        me = self.jwt_user
        blog_set = BlogSetFactory.create()
        url = reverse("blogs:blogset-mark-as-archived", [blog_set.pk])
        data = {}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        blog_set.refresh_from_db()
        self.assertIn(me, blog_set.archived_by.all())


class QualityReviewerTestCase(DenormMixin, QualityReviewerJWTTestCase):
    def test_cannot_create_blogset(self):
        """
        QualityReviewer can create blogset
        """
        url = reverse("blogs:blogset-list")
        data = {}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)
        self.assertEqual(0, BlogSet.objects.count())

    def test_can_get_blogset(self):
        BlogSetFactory.create_batch(5)
        url = reverse("blogs:blogset-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertIn("count", response_dict)
        self.assertEqual(5, response_dict["count"])

    def test_cannot_create_blogs(self):
        """
        QualityReviewer cannot create blogs
        """
        url = reverse("blogs:blog-list")
        blog_set = BlogSetFactory.create()

        data = {
            "blog_set": reverse("blogs:blogset-detail", [blog_set.pk]),
            "language": "en",
            "subject": "Proximity is power",
            "body": "Proximity is power. Proximity is power. Proximity is power.",
        }
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)
        self.assertEqual(0, Blog.objects.count())

    def test_can_get_blogs(self):
        """
        QualityReviewer can get blogs
        """
        url = reverse("blogs:blog-list")
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_get_blog_detail(self):
        """
        QualityReviewer can get blogs
        """
        blog_set = BlogSetFactory.create()
        blog = BlogFactory.create(
            blog_set=blog_set,
            language="en",
            subject="Proximity is power",
            body="Proximity is power. Proximity is power. Proximity is power.",
        )
        url = reverse("blogs:blog-detail", [blog.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_mark_blog_as_read(self):
        """
        QualityReviewer can mark blogs as read
        """
        me = self.jwt_user
        blog_set = BlogSetFactory.create()
        url = reverse("blogs:blogset-mark-as-read", [blog_set.pk])
        data = {}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        blog_set.refresh_from_db()
        self.assertIn(me, blog_set.read_by.all())

    def test_can_mark_blog_as_archived(self):
        """
        QualityReviewer can mark blogs as archived
        """
        me = self.jwt_user
        blog_set = BlogSetFactory.create()
        url = reverse("blogs:blogset-mark-as-archived", [blog_set.pk])
        data = {}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        blog_set.refresh_from_db()
        self.assertIn(me, blog_set.archived_by.all())


class CustomerUserTestCase(DenormMixin, CustomerUserJWTTestCase):
    def test_cannot_create_blogset(self):
        """
        CustomerUser can create blogset
        """
        url = reverse("blogs:blogset-list")
        data = {}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)
        self.assertEqual(0, BlogSet.objects.count())

    def test_can_get_blogset(self):
        BlogSetFactory.create_batch(5)
        url = reverse("blogs:blogset-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertIn("count", response_dict)
        self.assertEqual(5, response_dict["count"])

    def test_cannot_create_blogs(self):
        """
        CustomerUser cannot create blogs
        """
        url = reverse("blogs:blog-list")
        blog_set = BlogSetFactory.create()

        data = {
            "blog_set": reverse("blogs:blogset-detail", [blog_set.pk]),
            "language": "en",
            "subject": "Proximity is power",
            "body": "Proximity is power. Proximity is power. Proximity is power.",
        }
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)
        self.assertEqual(0, Blog.objects.count())

    def test_can_get_blogs(self):
        """
        CustomerUser can get blogs
        """
        url = reverse("blogs:blog-list")
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_get_blog_detail(self):
        """
        CustomerUser can get blogs
        """
        blog_set = BlogSetFactory.create()
        blog = BlogFactory.create(
            blog_set=blog_set,
            language="en",
            subject="Proximity is power",
            body="Proximity is power. Proximity is power. Proximity is power.",
        )
        url = reverse("blogs:blog-detail", [blog.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_mark_blog_as_read(self):
        """
        CustomerUser can mark blogs as read
        """
        me = self.jwt_user
        blog_set = BlogSetFactory.create()
        url = reverse("blogs:blogset-mark-as-read", [blog_set.pk])
        data = {}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        blog_set.refresh_from_db()
        self.assertIn(me, blog_set.read_by.all())

    def test_can_mark_blog_as_archived(self):
        """
        CustomerUser can mark blogs as archived
        """
        me = self.jwt_user
        blog_set = BlogSetFactory.create()
        url = reverse("blogs:blogset-mark-as-archived", [blog_set.pk])
        data = {}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        blog_set.refresh_from_db()
        self.assertIn(me, blog_set.archived_by.all())


class CustomerAdminTestCase(DenormMixin, CustomerAdminJWTTestCase):
    def test_cannot_create_blogset(self):
        """
        CustomerAdmin can create blogset
        """
        url = reverse("blogs:blogset-list")
        data = {}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)
        self.assertEqual(0, BlogSet.objects.count())

    def test_can_get_blogset(self):
        BlogSetFactory.create_batch(5)

        url = reverse("blogs:blogset-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertIn("count", response_dict)
        self.assertEqual(5, response_dict["count"])

    def test_can_get_blogs(self):
        """
        CustomerAdmin can get blogs
        """
        url = reverse("blogs:blog-list")
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_get_blog_detail(self):
        """
        CustomerAdmin can get blog
        """
        blog_set = BlogSetFactory.create()
        blog = BlogFactory.create(
            blog_set=blog_set,
            language="en",
            subject="Proximity is power",
            body="Proximity is power. Proximity is power. Proximity is power.",
        )
        url = reverse("blogs:blog-detail", [blog.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_mark_blog_as_read(self):
        """
        CustomerAdmin can mark blogs as read
        """
        me = self.jwt_user
        blog_set = BlogSetFactory.create()
        url = reverse("blogs:blogset-mark-as-read", [blog_set.pk])
        data = {}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        blog_set.refresh_from_db()
        self.assertIn(me, blog_set.read_by.all())

    def test_can_mark_blog_as_archived(self):
        """
        CustomerAdmin can mark blogs as archived
        """
        me = self.jwt_user
        blog_set = BlogSetFactory.create()
        url = reverse("blogs:blogset-mark-as-archived", [blog_set.pk])
        data = {}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        blog_set.refresh_from_db()
        self.assertIn(me, blog_set.archived_by.all())


class ContactTestCase(DenormMixin, ContactJWTTestCase):
    def test_cannot_create_blogset(self):
        """
        Contact can create blogset
        """
        url = reverse("blogs:blogset-list")
        data = {}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)
        self.assertEqual(0, BlogSet.objects.count())

    def test_can_get_blogset(self):
        BlogSetFactory.create_batch(5)

        url = reverse("blogs:blogset-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertIn("count", response_dict)
        self.assertEqual(5, response_dict["count"])

    def test_cannot_create_blogs(self):
        """
        Contact cannot create blogs
        """
        url = reverse("blogs:blog-list")
        blog_set = BlogSetFactory.create()

        data = {
            "blog_set": reverse("blogs:blogset-detail", [blog_set.pk]),
            "language": "en",
            "subject": "Proximity is power",
            "body": "Proximity is power. Proximity is power. Proximity is power.",
        }
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)
        self.assertEqual(0, Blog.objects.count())

    def test_can_get_blogs(self):
        """
        Contact can get blogs
        """
        url = reverse("blogs:blog-list")
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_get_blog_detail(self):
        """
        Contact can get blogs
        """
        blog_set = BlogSetFactory.create()
        blog = BlogFactory.create(
            blog_set=blog_set,
            language="en",
            subject="Proximity is power",
            body="Proximity is power. Proximity is power. Proximity is power.",
        )
        url = reverse("blogs:blog-detail", [blog.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_mark_blog_as_read(self):
        """
        Contact can mark blogs as read
        """
        me = self.jwt_user
        blog_set = BlogSetFactory.create()
        url = reverse("blogs:blogset-mark-as-read", [blog_set.pk])
        data = {}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        blog_set.refresh_from_db()
        self.assertIn(me, blog_set.read_by.all())

    def test_can_mark_blog_as_archived(self):
        """
        Contact can mark blogs as archived
        """
        me = self.jwt_user
        blog_set = BlogSetFactory.create()
        url = reverse("blogs:blogset-mark-as-archived", [blog_set.pk])
        data = {}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        blog_set.refresh_from_db()
        self.assertIn(me, blog_set.archived_by.all())
