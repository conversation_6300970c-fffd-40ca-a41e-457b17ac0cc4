# Generated by Django 3.1.14 on 2023-08-04 08:33

from django.db import migrations


def add_new_validations(apps, schema_editor):
    InputValidation = apps.get_model("assessments", "InputValidationLimits")
    full_time_employees = InputValidation(
        name="number_of_outgrowers",
        value=1000,
    )
    full_time_employees.save()
    part_time_employees = InputValidation(
        name="number_of_active_outgrowers",
        value=1000,
    )
    part_time_employees.save()


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0423_assessment_rapid_finalization_date"),
    ]

    operations = [migrations.RunPython(add_new_validations)]
