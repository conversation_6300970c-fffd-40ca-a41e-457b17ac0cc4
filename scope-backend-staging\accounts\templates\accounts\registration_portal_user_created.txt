{% load i18n %}
{% language language_code %}
{% if trainee %}
{% blocktrans with fullname=to_user.get_full_name email=to_user.email reset_uuid=reset_request.uuid %}
Dear {{ fullname }},

You have been successfully registered for using the Trainee SCOPE Portal with the following email address: {{ email }}

To begin using the application you have to create your own password. Please use the following link to do so: <a href="{{ frontend }}/set-password?token={{ reset_uuid }}">{{ frontend }}/#/set-password?token={{ reset_uuid }}</a>.

Once you have created your password you can login to the SCOPE Portal here <a href="{{ frontend }}">{{ frontend }}</a>.

If you have any questions or concerns regarding this email, please do not hesitate to contact SCOPEinsight at <a href="mailto:<EMAIL>"><EMAIL></a>.

Best regards,

SCOPEinsight
{% endblocktrans %}
{% else %}
{% blocktrans with fullname=to_user.get_full_name email=to_user.email reset_uuid=reset_request.uuid %}
Dear {{ fullname }},

You have been successfully registered for using the SCOPE Portal with the following email address: {{ email }}

To begin using the application you have to create your own password. Please use the following link to do so: <a href="{{ frontend }}/set-password?token={{ reset_uuid }}">{{ frontend }}/#/set-password?token={{ reset_uuid }}</a>.

Once you have created your password you can login to the SCOPE Portal here <a href="{{ frontend }}">{{ frontend }}</a>.

If you have any questions or concerns regarding this email, please do not hesitate to contact SCOPEinsight at <a href="mailto:<EMAIL>"><EMAIL></a>.

Best regards,

SCOPEinsight
{% endblocktrans %}
{% endif %}
{% endlanguage %}