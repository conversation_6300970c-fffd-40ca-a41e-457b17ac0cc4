from django.apps import apps
from django.core.management.base import BaseCommand
from mptt.managers import TreeManager


class Command(BaseCommand):
    help = "Rebuild the MPTT tree structure for a specific model"

    def add_arguments(self, parser):
        parser.add_argument("model", help="The name of the model to rebuild")

    def handle(self, *args, **options):
        model_name = options["model"]

        try:
            model = apps.get_model(
                app_label="assessments", model_name="SectionResponse"
            )
            tree_manager = TreeManager()
            model.objects.rebuild()
            self.stdout.write(
                self.style.SUCCESS(f"Successfully rebuilt MPTT tree for {model_name}")
            )
        except LookupError:
            self.stderr.write(self.style.ERROR(f"Model {model_name} not found"))
