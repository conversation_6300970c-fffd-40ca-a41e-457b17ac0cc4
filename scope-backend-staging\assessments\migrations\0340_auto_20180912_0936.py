# Generated by Django 1.11.15 on 2018-09-12 09:36


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0339_merge_20180904_0940")]

    operations = [
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="comment_number_of_active_members",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="comment_number_of_active_outgrowers",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="comment_number_of_executives",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="comment_number_of_full_time_employees",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="comment_number_of_member_cooperatives",
            field=models.TextField(blank=True),
        ),
        migrations.Add<PERSON><PERSON>(
            model_name="producingorganizationdetails",
            name="comment_number_of_member_unions",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="comment_number_of_members",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="comment_number_of_non_executives",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="comment_number_of_outgrowers",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="comment_number_of_part_time_employees",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="comment_number_of_seasonal_employees",
            field=models.TextField(blank=True),
        ),
    ]
