from collections import Counter

from django.db import migrations


def fill_assessment_projections_year(apps, schema_editor):
    Assessment = apps.get_model("assessments", "Assessment")
    assessments = Assessment.objects.all()
    for assessment in assessments:
        year_counter = Counter(
            assessment.monthlyincomeprojection_set.all().values_list("year", flat=True)
        ) + Counter(
            assessment.monthlyexpensesprojection_set.all().values_list(
                "year", flat=True
            )
        )
        try:
            year = year_counter.most_common(1)[0][0]
        except IndexError:
            pass
        else:
            assessment.projections_year = year
            assessment.save()


class Migration(migrations.Migration):

    dependencies = [("assessments", "0218_assessment_projections_year")]

    operations = [migrations.RunPython(fill_assessment_projections_year)]
