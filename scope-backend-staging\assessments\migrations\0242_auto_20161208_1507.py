# Generated by Django 1.10.4 on 2016-12-08 15:07


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0241_merge_20161205_1721")]

    operations = [
        migrations.AddField(
            model_name="assessment",
            name="agent_tab_accepted",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="assessment_tab_accepted",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="documents_tab_accepted",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="finance_product_tab_accepted",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="finance_tab_accepted",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="governance_tab_accepted",
            field=models.<PERSON>oleanField(default=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="observations_tab_accepted",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="organizational_tab_accepted",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="production_tab_accepted",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="value_chain_tab_accepted",
            field=models.BooleanField(default=True),
        ),
    ]
