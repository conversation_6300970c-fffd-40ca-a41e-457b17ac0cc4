from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0052_subquestion_description"),
        ("assessments", "0137_auto_20151117_1025"),
    ]

    operations = [
        migrations.AddField(
            model_name="subresponse",
            name="selected_option",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                to="products.SubQuestionOption",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="subresponse",
            name="checked_options",
            field=models.ManyToManyField(
                related_name="+", to="products.SubQuestionOption"
            ),
            preserve_default=True,
        ),
    ]
