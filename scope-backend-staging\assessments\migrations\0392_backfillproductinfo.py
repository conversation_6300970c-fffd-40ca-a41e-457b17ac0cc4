# Generated by Django 2.1.15 on 2020-08-14 12:22
from django.db import migrations
from django.db.models import Q
from django.db.models.signals import post_save, pre_delete, pre_save
from factory.django import mute_signals

from assessments.constants import (
    category_code_int_to_name,
    category_name_to_code,
    item_code_int_to_name,
    name_to_item_code,
)


def backfillproductinfo(apps, schema_editor):
    ProductPerTypeOption = apps.get_model("products", "ProductPerTypeOption")
    GlobalProductTypeOption = apps.get_model("products", "GlobalProductTypeOption")
    Product = apps.get_model("assessments", "Product")
    inputdata = [
        ("abaca", "FRUITS AND DERIVED PRODUCTS", 8, "Bananas", 486),
        (
            "Afircan Birds Eye, Birds eye, Thin Chilli",
            "VEGETABLES AND DERIVED PRODUCTS",
            7,
            "Chillies and peppers (green)",
            401,
        ),
        (
            "agricole",
            "MACHINERY",
            23,
            "Agricultural machinery not else specified (n.e.s.) refer to total Agricultural machines as described by the Harmonised Coding System (HS) codes 8435-8436. Data refer to the value of the trade expressed in 1000 USD",
            2455017,
        ),
        (
            "AGRICOLE",
            "MACHINERY",
            23,
            "Agricultural machinery not else specified (n.e.s.) refer to total Agricultural machines as described by the Harmonised Coding System (HS) codes 8435-8436. Data refer to the value of the trade expressed in 1000 USD",
            2455017,
        ),
        (
            "Alquiler maquinaria agricola",
            "MACHINERY",
            23,
            "Agricultural tractors, total generally refer to total wheel, crawler or track-laying type tractors and pedestrian tractors used in agriculture. Data are available for numbers in use in the agricultural sector as of 2000. Data on import and export in value and number are also available as of 1961.",
            2455009,
        ),
        (
            "Apple, Ngoe, Tommy Atkins, Kent, Keitt",
            "FRUITS AND DERIVED PRODUCTS",
            8,
            "Mangoes",
            571,
        ),
        ("Aquaculture essentials", "FODDER CROPS AND PRODUCTS", 11, "Fish Meal", 1174),
        (
            "Arabica",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Coffee green",
            656,
        ),
        (
            "ARABICA",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Coffee green",
            656,
        ),
        (
            "Arabica AA,AB,C PB,TT and T.",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Coffee green",
            656,
        ),
        (
            "Arabica AA, AB, C, PB,TT,T,HE,E.",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Coffee green",
            656,
        ),
        (
            "Arabica grade AA, AB, C,PB,TT and T",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Coffee green",
            656,
        ),
        (
            "Arabica grade AA,AB,C,PB,TT,E,T UG",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Coffee green",
            656,
        ),
        (
            "Arabica Grade AA,AB,C,PB,TT,T,HE",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Coffee green",
            656,
        ),
        (
            "Arabica Grade AA,AB,PB,C,T,TT,E,HE &UG.",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Coffee green",
            656,
        ),
        (
            "Arabica Grades AA,AB, C, TT, T HE, E, AND PB.",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Coffee green",
            656,
        ),
        (
            "Araca AA,AB, C, PB, TT ,T,HE, E",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Coffee green",
            656,
        ),
        ("Baby corn", "VEGETABLES AND DERIVED PRODUCTS", 7, "Green Corn (Maize)", 446),
        ("bananas and plantains", "FRUITS AND DERIVED PRODUCTS", 8, "Bananas", 486),
        ("Beans, dry", "PULSES AND DERIVED PRODUCTS", 4, "Beans, dry", 176),
        (
            "bee-keeping and production of honey and beeswax",
            "LIVESTOCK",
            16,
            "Beehives",
            1181,
        ),
        (
            "beet seeds (excluding sugar beet seeds)",
            "FODDER CROPS AND PRODUCTS",
            11,
            "Beet Tops",
            629,
        ),
        ("Berries nes", "FRUITS AND DERIVED PRODUCTS", 8, "Berries nes", 558),
        (
            "birds, such as parakeets etc.",
            "LIVESTOCK",
            16,
            "Pigeons and other birds",
            1083,
        ),
        ("Bread", "CEREALS AND CEREAL PRODUCTS", 1, "Bread", 20),
        ("Brinjals & Ravaya", "VEGETABLES AND DERIVED PRODUCTS", 7, "Eggplants", 399),
        ("Broad beans, dry", "PULSES AND DERIVED PRODUCTS", 4, "Broad beans, dry", 181),
        ("Bulls", "LIVESTOCK", 16, "Cattle", 866),
        (
            "cacao",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Cocoa beans",
            661,
        ),
        (
            "Cacao",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Cocoa beans",
            661,
        ),
        (
            "CACAO",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Cocoa beans",
            661,
        ),
        (
            "cacao beans fermented",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Cocoa beans",
            661,
        ),
        (
            "cacao beans unfermented",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Cocoa beans",
            661,
        ),
        (
            "café",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Coffee, Roasted",
            657,
        ),
        (
            "Café",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Coffee, Roasted",
            657,
        ),
        (
            "Café Décortiqué",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Coffee green",
            656,
        ),
        (
            "Café Oro",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Coffee Extracts",
            659,
        ),
        ("Cran Barry", "FRUITS AND DERIVED PRODUCTS", 8, "Cranberries", 554),
        ("Rose Coco", "PULSES AND DERIVED PRODUCTS", 4, "Beans, dry", 176),
        ("Pinto", "PULSES AND DERIVED PRODUCTS", 4, "Beans, dry", 176),
        ("CANDLES", "VEGETABLE AND ANIMAL OILS AND FATS", 14, "Vegetable tallow", 306),
        ("carrots", "VEGETABLES AND DERIVED PRODUCTS", 7, "Carrot", 426),
        ("Carrots", "VEGETABLES AND DERIVED PRODUCTS", 7, "Carrot", 426),
        ("Cattle/beef", "LIVESTOCK", 16, "Cattle", 866),
        ("Cattle", "LIVESTOCK", 16, "Cattle", 866),
        ("goats", "LIVESTOCK", 16, "Goats", 1016),
        ("sheep", "LIVESTOCK", 16, "Sheep", 976),
        ("camel", "LIVESTOCK", 16, "Camels", 1126),
        (" donkey", "LIVESTOCK", 16, "Mules", 1110),
        (
            "Cavedish& Grand nine, Chinese Dwarf",
            "FRUITS AND DERIVED PRODUCTS",
            8,
            "Bananas",
            486,
        ),
        (
            "Cenchrus cillaris ",
            "FODDER CROPS AND PRODUCTS",
            11,
            "Hay, non leguminous",
            857,
        ),
        ("Centro de acopio de leche", "MACHINERY", 23, "Milking machines", 2455008),
        ("Cherry", "FRUITS AND DERIVED PRODUCTS", 8, "Fruit, fresh nes", 619),
        ("Cherry and Mbuni", "FRUITS AND DERIVED PRODUCTS", 8, "Fruit, dried nes", 620),
        ("CHERRY AND MBUNI", "FRUITS AND DERIVED PRODUCTS", 8, "Fruit, dried nes", 620),
        ("Cherry, Mbuni", "FRUITS AND DERIVED PRODUCTS", 8, "Fruit, dried nes", 620),
        ("chick peas", "PULSES AND DERIVED PRODUCTS", 4, "Chick-peas, dry", 191),
        ("Chick-peas, dry", "PULSES AND DERIVED PRODUCTS", 4, "Chick-peas, dry", 191),
        (
            "chile dulce para ensalada",
            "VEGETABLES AND DERIVED PRODUCTS",
            7,
            "Chillies and peppers (green)",
            401,
        ),
        (
            "chile dulce para ensalada",
            "VEGETABLES AND DERIVED PRODUCTS",
            7,
            "Chillies and peppers (green)",
            401,
        ),
        (
            "Chillies and peppers (green)",
            "VEGETABLES AND DERIVED PRODUCTS",
            7,
            "Chillies and peppers (green)",
            401,
        ),
        (
            "Chocolate Products nes",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Chocolate Products nes",
            666,
        ),
        ("Climbing Beans", "VEGETABLES AND DERIVED PRODUCTS", 7, "Beans, green", 414),
        (
            "Cocoa beans",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Cocoa beans",
            661,
        ),
        (
            "Coffee green",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Coffee green",
            656,
        ),
        (
            "Cotton, Carded or Combed",
            "FIBRES OF VEGETAL AND ANIMAL ORIGIN",
            9,
            "Cotton, Carded or Combed",
            768,
        ),
        (
            "Cotton Lint",
            "FIBRES OF VEGETAL AND ANIMAL ORIGIN",
            9,
            "Cotton Linters",
            770,
        ),
        (
            "Cow milk, whole (fresh)",
            "PRODUCTS FROM LIVE ANIMALS",
            18,
            "Cow milk, whole (fresh)",
            882,
        ),
        ("Cow peas, dry", "PULSES AND DERIVED PRODUCTS", 4, "Cow peas, dry", 195),
        (
            "CP722086",
            "SUGAR CROPS AND SWEETENERS AND DERIVED PRODUCTS",
            3,
            "Sugar crops nes",
            161,
        ),
        ("CREAMS", "PRODUCTS FROM LIVE ANIMALS", 18, "Cream, Fresh", 885),
        ("Crema", "PRODUCTS FROM LIVE ANIMALS", 18, "Cream, Fresh", 885),
        (
            "Criolla",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Cocoa beans",
            661,
        ),
        ("Cuerno Enano", "FRUITS AND DERIVED PRODUCTS", 8, "Bananas", 486),
        ("Dairy", "PRODUCTS FROM LIVE ANIMALS", 18, "Cow milk, whole (fresh)", 882),
        ("Egg", "PRODUCTS FROM LIVE ANIMALS", 18, "Hen Egg", 1062),
        ("Egg Plants", "VEGETABLES AND DERIVED PRODUCTS", 7, "Eggplants", 399),
        (
            "eggplants (aubergines)",
            "VEGETABLES AND DERIVED PRODUCTS",
            7,
            "Eggplants",
            399,
        ),
        (
            "Essential oil",
            "TOBACCO AND RUBBER AND OTHER CROPS",
            13,
            "Essential oils nes",
            753,
        ),
        ("fève", "VEGETABLES AND DERIVED PRODUCTS", 7, "Peas, green", 417),
        (
            "fève de cacao",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Cocoa beans",
            661,
        ),
        (
            "Fève de cacao",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Cocoa beans",
            661,
        ),
        ("feves", "VEGETABLES AND DERIVED PRODUCTS", 7, "Peas, green", 417),
        ("Fèves", "VEGETABLES AND DERIVED PRODUCTS", 7, "Peas, green", 417),
        ("fêves", "VEGETABLES AND DERIVED PRODUCTS", 7, "Peas, green", 417),
        ("Fèves sèche", "PULSES AND DERIVED PRODUCTS", 4, "Broad beans, dry", 181),
        ("Fèves sèchées", "PULSES AND DERIVED PRODUCTS", 4, "Broad beans, dry", 181),
        ("Fèves sèches", "PULSES AND DERIVED PRODUCTS", 4, "Broad beans, dry", 181),
        ("Fingerings", " ROOTS AND TUBERS AND DERIVED PRODUCTS", 2, "Potatoes", 116),
        ("Fingerlings", " ROOTS AND TUBERS AND DERIVED PRODUCTS", 2, "Potatoes", 116),
        (
            "Finger millet,Brush Millet & Gardan rice",
            " CEREALS AND CEREAL PRODUCTS",
            1,
            "Millet",
            79,
        ),
        ("Fish Feeds", " VEGETABLES AND DERIVED PRODUCTS", 7, "Fish Meal", 7020),
        (
            "Flour of Cereals nes",
            "CEREALS AND CEREAL PRODUCTS",
            1,
            "Flour of Cereals nes",
            111,
        ),
        ("forage kale", " FODDER CROPS AND PRODUCTS", 11, "Forage Products nes", 651),
        (
            "fowls of the species Gallus domesticus (chickens and capons)",
            "LIVESTOCK",
            16,
            "Chickens",
            866,
        ),
        ("ducks", "LIVESTOCK", 16, "Ducks", 1068),
        ("geese", "LIVESTOCK", 16, "Geese", 1072),
        ("turkeys", "LIVESTOCK", 16, "Turkeys", 1079),
        ("guineafowls", "LIVESTOCK", 16, "Pigeons and other birds", 1083),
        ("French beans", "VEGETABLES AND DERIVED PRODUCTS", 7, "Beans, green", 414),
        ("French Beans", "VEGETABLES AND DERIVED PRODUCTS", 7, "Beans, green", 414),
        (
            "Fresh milk",
            "PRODUCTS FROM LIVE ANIMALS",
            18,
            "Cow milk, whole (fresh)",
            882,
        ),
        ("Frijol", "PULSES AND DERIVED PRODUCTS", 4, "Beans, dry", 176),
        ("Fruit", "FRUITS AND DERIVED PRODUCTS", 8, "Fruit, fresh nes", 619),
        ("Fruit, fresh nes", "FRUITS AND DERIVED PRODUCTS", 8, "Fruit, fresh nes", 619),
        (
            "Fruit, tropical (fresh) nes",
            "FRUITS AND DERIVED PRODUCTS",
            8,
            "Fruit, tropical (fresh) nes",
            603,
        ),
        ("Fruta", "FRUITS AND DERIVED PRODUCTS", 8, "Fruit, fresh nes", 619),
        ("Fruta madura", "FRUITS AND DERIVED PRODUCTS", 8, "Fruit, fresh nes", 619),
        ("Ganado en pie", "LIVESTOCK", 16, "Cattle", 866),
        ("grain maize", "CEREALS AND CEREAL PRODUCTS", 1, "Maize", 56),
        ("Grains", "CEREALS AND CEREAL PRODUCTS", 1, "Malt", 49),
        ("Green pods", "VEGETABLES AND DERIVED PRODUCTS", 7, "Peas, green", 417),
        ("Green Pods", "VEGETABLES AND DERIVED PRODUCTS", 7, "Peas, green", 417),
        (
            "Groundnuts, in shell",
            " OIL-BEARING CROPS AND DERIVED PRODUCTS",
            6,
            "Groundnuts, in shell",
            242,
        ),
        ("harvesting", "MACHINERY", 23, "Harvester and threshers (trade)", 2455014),
        ("Hass", "FRUITS AND DERIVED PRODUCTS", 8, "avocado", 572),
        ("Hass, Fuerte", "FRUITS AND DERIVED PRODUCTS", 8, "avocado", 572),
        ("Head", "VEGETABLES AND DERIVED PRODUCTS", 7, "Lettuce and chicory", 372),
        ("HIVES", "LIVESTOCK", 16, "Beehives", 1181),
        (
            "imoprted seeds",
            "TOBACCO AND RUBBER AND OTHER CROPS",
            13,
            "Seeds for Planting",
            1294,
        ),
        (
            "imported seeds",
            "TOBACCO AND RUBBER AND OTHER CROPS",
            13,
            "Seeds for Planting",
            1294,
        ),
        (
            "Indigenous Cow Milk",
            "PRODUCTS FROM LIVE ANIMALS",
            18,
            "Cow milk, whole (fresh)",
            882,
        ),
        ("Inta Cardenas", "PULSES AND DERIVED PRODUCTS", 4, "Beans, dry", 176),
        ("Inta rojo", "PULSES AND DERIVED PRODUCTS", 4, "Beans, dry", 176),
        (
            "la fève de cacao",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Cocoa beans",
            661,
        ),
        ("Leche fluida.", "PRODUCTS FROM LIVE ANIMALS", 18, "Standardized Milk", 883),
        ("Lentils, dry", "PULSES AND DERIVED PRODUCTS", 4, "Lentils, dry", 201),
        (
            "Local Soya Beans",
            "OIL-BEARING CROPS AND DERIVED PRODUCTS",
            6,
            "Soybeans",
            236,
        ),
        ("Maiz Dekal 390", "CEREALS AND CEREAL PRODUCTS", 1, "maize", 56),
        ("Mbuni", " LIVESTOCK", 16, "Live animals nes", 1171),
        ("MD-2", "FRUITS AND DERIVED PRODUCTS", 8, "Pineapple", 574),
        (
            "meat (chicken)",
            "PRODUCTS FROM SLAUGHTERED ANIMALS",
            17,
            "Chicken Meat",
            1058,
        ),
        (
            "meat (other)",
            "PRODUCTS FROM SLAUGHTERED ANIMALS",
            17,
            "Meat, Prepared nes",
            1172,
        ),
        ("Milk", "PRODUCTS FROM LIVE ANIMALS", 18, "Cow milk, whole (fresh)", 882),
        ("MILK", "PRODUCTS FROM LIVE ANIMALS", 18, "Cow milk, whole (fresh)", 882),
        ("milk (buffalo)", "PRODUCTS FROM LIVE ANIMALS", 18, "Buffalo milk", 951),
        (
            "milk (cow)",
            "PRODUCTS FROM LIVE ANIMALS",
            18,
            "Cow milk, whole (fresh)",
            882,
        ),
        ("milk (other)", "PRODUCTS FROM LIVE ANIMALS", 18, "Goat milk", 1020),
        ("millets", "CEREALS AND CEREAL PRODUCTS", 1, "Millet", 79),
        ("Mixed grain", "CEREALS AND CEREAL PRODUCTS", 1, "Mixed grain", 103),
        ("Musica", "VEGETABLES AND DERIVED PRODUCTS", 7, "Beans, green", 414),
        ("NAMBALE", "PULSES AND DERIVED PRODUCTS", 4, "Beans, dry", 176),
        (
            "Navel, Page (minneola), fix",
            "FRUITS AND DERIVED PRODUCTS",
            8,
            "Orangers",
            490,
        ),
        (
            "niger seed",
            "OIL-BEARING CROPS AND DERIVED PRODUCTS",
            6,
            "Oilseeds nes",
            339,
        ),
        ("Nuts nes", "NUTS AND DERIVED PRODUCTS", 5, "Nuts nes", 234),
        (
            "Oil palm fruit",
            "OIL-BEARING CROPS AND DERIVED PRODUCTS",
            6,
            "[Oil palm fruit]",
            254,
        ),
        ("oil palms", "VEGETABLE AND ANIMAL OILS AND FATS", 14, "Oil of palm", 257),
        (
            "Oilseeds nes",
            "OIL-BEARING CROPS AND DERIVED PRODUCTS",
            6,
            "Oilseeds nes",
            339,
        ),
        (
            "onions (incl. shallots)",
            "VEGETABLES AND DERIVED PRODUCTS",
            7,
            "Onions, shallots (green)",
            402,
        ),
        (
            "Onions, shallots (green)",
            "VEGETABLES AND DERIVED PRODUCTS",
            7,
            "Onions, shallots (green)",
            402,
        ),
        (
            "other birds (except poultry)",
            "LIVESTOCK",
            16,
            "Pigeons and other birds",
            1083,
        ),
        (
            "other leafy or stem vegetables",
            "VEGETABLES AND DERIVED PRODUCTS",
            7,
            "Vegetables, Fresh n.e.s.",
            463,
        ),
        (
            "other leguminous crops",
            "VEGETABLES AND DERIVED PRODUCTS",
            7,
            "Forage Products nes",
            651,
        ),
        (
            "other melons ",
            "VEGETABLES AND DERIVED PRODUCTS",
            7,
            "Melons, Cantaloupes",
            568,
        ),
        (
            "fruit-bearing vegetables",
            "VEGETABLES AND DERIVED PRODUCTS",
            7,
            "Vegetables, Fresh n.e.s.",
            463,
        ),
        (
            "other oil seeds",
            "OIL-BEARING CROPS AND DERIVED PRODUCTS",
            6,
            "Oilseeds nes",
            339,
        ),
        (
            "other oleaginous fruits",
            "OIL-BEARING CROPS AND DERIVED PRODUCTS",
            6,
            "Oilseeds nes",
            339,
        ),
        (
            "other root, bulb or tuberous vegetables",
            "ROOTS AND TUBERS AND DERIVED PRODUCTS",
            2,
            "Roots and Tubers, Dried nes",
            152,
        ),
        ("other spices and aromatic crops", "SPICES", 10, "Spices nes", 723),
        ("Paddy rice", "CEREALS AND CEREAL PRODUCTS", 1, "Rice, paddy", 27),
        ("Palm oil", "VEGETABLE AND ANIMAL OILS AND FATS", 14, "Oil of palm", 257),
        ("Parchment", "HIDES AND SKINS", 19, "Skins nes, Calves", 930),
        ("Peas, dry", "PULSES AND DERIVED PRODUCTS", 4, "Peas, dry", 187),
        ("Pishori", "CEREALS AND CEREAL PRODUCTS", 1, "Rice, Milled", 31),
        (
            "plants for planting",
            "TOBACCO AND RUBBER AND OTHER CROPS",
            13,
            "Seeds for Planting",
            1294,
        ),
        ("pomelo", "FRUITS AND DERIVED PRODUCTS", 8, "Grapefruit and pomelo", 507),
        (
            "Post harvest technology",
            "MACHINERY",
            23,
            "Harvester and threshers (trade)",
            2455014,
        ),
        ("poultry", "LIVESTOCK", 16, "Chickens", 1057),
        ("poultry hatcheries", "LIVESTOCK", 16, "Chickens", 1057),
        ("PROPOLIS", "PRODUCTS FROM LIVE ANIMALS", 18, "Beeswax", 1183),
        ("Pullet", "LIVESTOCK", 16, "Chickens", 1057),
        ("Pulses nes", "PULSES AND DERIVED PRODUCTS", 4, "Pulses nes", 211),
        (
            "Queso semi descremado",
            "PRODUCTS FROM LIVE ANIMALS",
            18,
            "Cheese from Whole Cow Milk",
            901,
        ),
        ("Rabo F1", "VEGETABLES AND DERIVED PRODUCTS", 7, "Tomatoes, fresh", 388),
        (
            "raw cow milk from cows or buffaloes",
            "PRODUCTS FROM LIVE ANIMALS",
            18,
            "Cow milk, whole (fresh)",
            882,
        ),
        ("Raw Milk", "PRODUCTS FROM LIVE ANIMALS", 18, "Cow milk, whole (fresh)", 882),
        ("raw sheep or goat milk", "PRODUCTS FROM LIVE ANIMALS", 18, "Goat milk", 1020),
        ("Redcherry", "FRUITS AND DERIVED PRODUCTS", 8, "Fruit, fresh nes", 619),
        (
            "Red chilly",
            "VEGETABLES AND DERIVED PRODUCTS",
            7,
            "Chillies and peppers (green)",
            401,
        ),
        ("Rice", "CEREALS AND CEREAL PRODUCTS", 1, "Rice, Milled", 31),
        (
            "rice (including organic farming and the growing of genetically modified rice)",
            "CEREALS AND CEREAL PRODUCTS",
            1,
            "Rice, Milled",
            31,
        ),
        ("Rojo seda", "PULSES AND DERIVED PRODUCTS", 4, "Beans, dry", 176),
        (
            "Roots and tubers nes",
            "ROOTS AND TUBERS AND DERIVED PRODUCTS",
            2,
            "Roots and tubers nes",
            149,
        ),
        (
            "Ruiru 11",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Coffee green",
            656,
        ),
        (
            "Ruiru 11 and Batian",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Coffee green",
            656,
        ),
        (
            "Ruiru 11,SL28,",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Coffee green",
            656,
        ),
        ("SC513", "CEREALS AND CEREAL PRODUCTS", 1, "Maize", 56),
        (
            "Snow and Garden peas",
            "VEGETABLES AND DERIVED PRODUCTS",
            7,
            "Peas, green",
            417,
        ),
        (
            "SNOW AND SNAP PEAS",
            "VEGETABLES AND DERIVED PRODUCTS",
            7,
            "Peas, green",
            417,
        ),
        ("soya beans", "OIL-BEARING CROPS AND DERIVED PRODUCTS", 6, "Soybeans", 236),
        ("Standard Maize", "CEREALS AND CEREAL PRODUCTS", 1, "Maize", 56),
        (
            "SYRUP",
            "SUGAR CROPS AND SWEETENERS AND DERIVED PRODUCTS",
            3,
            "Sugar and Syrups nes",
            167,
        ),
        (
            "Tanya Tomato Seeds",
            "VEGETABLES AND DERIVED PRODUCTS",
            7,
            "Tomatoes, fresh",
            388,
        ),
        (
            "Tea Leaves",
            "STIMULANT CROPS AND DERIVED PRODUCTS (COFFEE, TEA, COCOA, ETC))",
            12,
            "Tea",
            667,
        ),
        (
            "Tomatoes, fresh",
            "VEGETABLES AND DERIVED PRODUCTS",
            7,
            "Tomatoes, fresh",
            388,
        ),
        (
            "Tuber",
            "ROOTS AND TUBERS AND DERIVED PRODUCTS",
            2,
            "Roots and Tubers, Dried nes",
            151,
        ),
        (
            "Tubers",
            "ROOTS AND TUBERS AND DERIVED PRODUCTS",
            2,
            "Roots and Tubers, Dried nes",
            151,
        ),
        (
            "Vegetables, Fresh n.e.s.",
            "VEGETABLES AND DERIVED PRODUCTS",
            7,
            "Vegetables, Fresh n.e.s.",
            463,
        ),
        (
            "Venta de concentrados",
            "FODDER CROPS AND PRODUCTS",
            11,
            "Other Concentrates nes",
            852,
        ),
        (
            "Yello Soya beans",
            "OIL-BEARING CROPS AND DERIVED PRODUCTS",
            6,
            "Soybeans",
            236,
        ),
        (
            "YELLOW PASSION FRUIT",
            "FRUITS AND DERIVED PRODUCTS",
            8,
            "Pome fruit nes",
            542,
        ),
    ]
    with mute_signals(post_save, pre_save, pre_delete):
        for (
            dbname,
            fao_category,
            fao_category_id,
            fao_item_name,
            fao_item_id,
        ) in inputdata:
            product_name = item_code_int_to_name.get(int(fao_item_id), fao_item_name)
            category_name = category_code_int_to_name[int(fao_category_id)]
            category, _ = GlobalProductTypeOption.objects.get_or_create(
                name=category_name
            )
            ProductPerTypeOption.objects.get_or_create(
                name=product_name, product_type=category
            )
            for product in Product.objects.filter(
                Q(name=dbname) | Q(fao_item_code=name_to_item_code[product_name])
            ):
                product.name = product_name
                product.fao_item_code = name_to_item_code[product_name]
                product.global_product_type = category_name
                product.fao_category_code = category_name_to_code[category_name]
                product.save()


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0391_product_fao_category_code"),
        ("products", "0174_auto_20200114_2112"),
    ]

    operations = [migrations.RunPython(backfillproductinfo)]
