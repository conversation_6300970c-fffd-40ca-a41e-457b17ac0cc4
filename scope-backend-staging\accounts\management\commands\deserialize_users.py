import json
import sys

from django.core.management.base import BaseCommand, CommandError

from accounts.serializers import SendToTrainingUserSerializer


class Command(BaseCommand):
    help = "Deserialize users to import from a different server"

    def handle(self, *args, **options):
        user_datas = json.loads(sys.stdin.read())
        serializers = [
            SendToTrainingUserSerializer(data=user_data) for user_data in user_datas
        ]
        for serializer in serializers:
            serializer.is_valid(raise_exception=True)
        for serializer in serializers:
            serializer.save()
