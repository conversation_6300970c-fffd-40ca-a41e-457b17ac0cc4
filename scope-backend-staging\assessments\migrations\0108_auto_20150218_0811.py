import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0107_auto_20150218_0752")]

    operations = [
        migrations.AddField(
            model_name="enablingplayer",
            name="contact_email",
            field=models.TextField(
                blank=True, validators=[django.core.validators.EmailValidator()]
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="enablingplayer",
            name="contact_name",
            field=models.TextField(blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="enablingplayer",
            name="contact_phone_number",
            field=models.TextField(blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="valuechainplayer",
            name="contact_email",
            field=models.TextField(
                blank=True, validators=[django.core.validators.EmailValidator()]
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="valuechainplayer",
            name="contact_name",
            field=models.TextField(blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="valuechainplayer",
            name="contact_phone_number",
            field=models.TextField(blank=True),
            preserve_default=True,
        ),
    ]
