from django.conf import settings
from django.db import models

from accounts.models import User
from libs.models_helpers import BaseModel, DocumentMixin


class BlogSet(BaseModel):
    read_by = models.ManyToManyField(User, related_name="read_blogs", blank=True)
    archived_by = models.ManyToManyField(
        User, related_name="archived_blogs", blank=True
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ("-created_at",)


class Blog(BaseModel):
    subject = models.TextField()
    body = models.TextField()
    language = models.CharField(max_length=2, choices=settings.LANGUAGES, default="en")
    blog_set = models.ForeignKey(
        "BlogSet", related_name="blogs", on_delete=models.PROTECT
    )

    class Meta:
        ordering = ("pk",)


class BlogImage(DocumentMixin, BaseModel):
    blog = models.ForeignKey(Blog, related_name="images", on_delete=models.CASCADE)
