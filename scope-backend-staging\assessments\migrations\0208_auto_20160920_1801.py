from decimal import ROUND_HALF_UP, Decimal

from django.db import migrations


def round_subresponse_value(apps, schema_editor):
    """
    In preperation for conversion to integerfield, round value
    """
    SubResponse = apps.get_model("assessments", "SubResponse")
    for subresponse in SubResponse.objects.filter(value__isnull=False):
        subresponse.value = subresponse.value.quantize(
            Decimal("0"), rounding=ROUND_HALF_UP
        )
        subresponse.save()


class Migration(migrations.Migration):

    dependencies = [("assessments", "0207_auto_20160920_1748")]

    operations = [migrations.RunPython(round_subresponse_value)]
