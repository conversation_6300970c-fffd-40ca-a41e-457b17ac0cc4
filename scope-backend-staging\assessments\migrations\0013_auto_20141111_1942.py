from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "assessments",
            "0012_financialratio_financialscore_financialscores_generalcheck_generalchecks_ratioscores",
        )
    ]

    operations = [
        migrations.AlterField(
            model_name="assessmentcomment",
            name="user",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="assessmentcomments",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="assessmentdocumentcomment",
            name="user",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="assessmentdocumentcomments",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheetcomment",
            name="user",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="balancesheetcomments",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=True,
        ),
        migrations.Alter<PERSON>ield(
            model_name="balancesheetdocumentcomment",
            name="user",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="balancesheetdocumentcomments",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="costofsalecomment",
            name="user",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="costofsalecomments",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="costofsaledocumentcomment",
            name="user",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="costofsaledocumentcomments",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="expensecomment",
            name="user",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="expensecomments",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="expensedocumentcomment",
            name="user",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="expensedocumentcomments",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="profitlossstatementcomment",
            name="user",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="profitlossstatementcomments",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="profitlossstatementdocumentcomment",
            name="user",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="profitlossstatementdocumentcomments",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="responsecomment",
            name="user",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="responsecomments",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="responsedocumentcomment",
            name="user",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="responsedocumentcomments",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="unscoredresponsecomment",
            name="user",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="unscoredresponsecomments",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="unscoredresponsedocumentcomment",
            name="user",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="unscoredresponsedocumentcomments",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=True,
        ),
    ]
