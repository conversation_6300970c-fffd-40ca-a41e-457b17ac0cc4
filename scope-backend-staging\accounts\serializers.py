import datetime
import re

from django.conf import settings
from django.contrib.auth.models import Group
from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.db.models import Avg, Count, Q
from django.db.models.query import QuerySet
from django.utils.translation import ugettext as _
from rest_framework import serializers
from rest_framework.reverse import reverse
from rest_framework_jwt.settings import api_settings

from assessments.models import Assessment, AssessmentAssignment
from customers.models import Contact, Customer, ProducingOrganization
from hrm.models import Assessor, AssessorTraining, Employee, FinancialSpecialistTraining
from libs.rest_framework_helpers.authentication_helpers import not_expired
from libs.rest_framework_helpers.fields import TranslatableTextField
from libs.rest_framework_helpers.serializers import CustomHyperlinkedModelSerializer
from products.models import ToolType
from projects.models import Project

from .models import PasswordResetRequest, User

jwt_payload_handler = api_settings.JWT_PAYLOAD_HANDLER
jwt_encode_handler = api_settings.JWT_ENCODE_HANDLER


class RegistrationSerializer(serializers.Serializer):
    first_name = serializers.CharField(required=True)
    last_name = serializers.CharField(required=True)
    city = serializers.CharField(required=True)
    country = serializers.CharField(required=True)
    email = serializers.EmailField(required=True)
    language = serializers.CharField(required=True)
    organization = serializers.IntegerField(required=True)
    password = serializers.CharField(required=True)

    def save(self, **kwargs):
        user = User.objects.create_user(
            password=self.validated_data["password"],
            first_name=self.validated_data["first_name"],
            last_name=self.validated_data["last_name"],
            city=self.validated_data["city"],
            country=self.validated_data["country"],
            email=self.validated_data["email"],
            language=self.validated_data["language"],
            organization_id=self.validated_data["organization"],
            allowed_frontends='[{"app": "smartclient", "name": "SCOPE App"}]',
            is_self_registered=True,
        )
        assessor = Assessor.objects.create(self_assessor=True, user=user)
        assessor.tools.add(ToolType.objects.filter(name="SCOPE Rapid").last())
        return user


class UserSerializer(CustomHyperlinkedModelSerializer):
    can_access_prod = serializers.SerializerMethodField()
    is_assessor = serializers.SerializerMethodField()
    is_financial_specialist = serializers.SerializerMethodField()
    is_scope_employee = serializers.SerializerMethodField()
    has_valid_license = serializers.SerializerMethodField()
    assessor_license_validity = serializers.SerializerMethodField()
    self_assessments_limit = serializers.SerializerMethodField()

    def get_self_assessments_limit(self, obj):
        organization = obj.organization
        if organization and hasattr(organization, 'producing_organization'):
            producing_organization = organization.producing_organization
            if producing_organization:
                return producing_organization.self_assessments_limit
        return None
    def get_can_access_prod(self, obj):
        if obj.is_active:
            if obj.is_employee:
                return True
            if obj.is_customer_admin:
                return True
            if obj.is_customer_user:
                return True
            if (
                obj.is_assessor
                and obj.assessor.expiration_date_assessor
                and obj.assessor.expiration_date_assessor > datetime.date.today()
            ):
                return True
            if (
                obj.is_financial_specialist
                and obj.assessor.expiration_date_financial_specialist
                and obj.assessor.expiration_date_financial_specialist
                > datetime.date.today()
            ):
                return True
        return False

    def get_is_assessor(self, obj):
        return obj.is_assessor and not_expired(obj)

    def get_is_financial_specialist(self, obj):
        return obj.is_financial_specialist and not_expired(obj)

    def get_is_scope_employee(self, obj):
        return obj.organization.name == "SCOPEinsight"

    def get_validation_exclusions(self, instance, data):
        response = super(UserSerializer, self).get_validation_exclusions(instance, data)
        response.append("password")
        return response

    def to_internal_value(self, data):
        user = self.context["request"].user
        if user.is_customer_admin:
            data["organization"] = reverse(
                "customers:customer-detail", [user.organization.pk]
            )
        return super(UserSerializer, self).to_internal_value(data)

    def get_has_valid_license(self, obj):
        if obj.is_active:
            if (
                obj.is_assessor
                and obj.assessor.expiration_date_assessor
                and obj.assessor.expiration_date_assessor > datetime.date.today()
            ):
                return True
            elif (
                obj.is_financial_specialist
                and obj.assessor.expiration_date_financial_specialist
                and obj.assessor.expiration_date_financial_specialist
                > datetime.date.today()
            ):
                return True
            elif (
                obj.is_customer_user
                and obj.organization.expiration_date_dashboard_licence
                and obj.organization.expiration_date_dashboard_licence
                > datetime.date.today()
            ) or (
                obj.is_customer_admin
                and obj.organization.expiration_date_dashboard_licence
                and obj.organization.expiration_date_dashboard_licence
                > datetime.date.today()
            ):
                return True
        return False

    def get_assessor_license_validity(self, obj):
        if obj.is_active:
            if obj.is_assessor:
                return obj.assessor.expiration_date_assessor
        return 'N/A'

    class Meta:
        model = User
        fields = (
            "id",
            "url",
            "modified_at",
            "email",
            "first_name",
            "groups",
            "language",
            "languages_spoken",
            "last_name",
            "phone_number",
            "is_active",
            "is_superuser",
            "organization",
            "allowed_frontends",
            "can_create_projects",
            "employee",
            "assessor",
            "customer_contact",
            "street",
            "street_number",
            "zipcode",
            "city",
            "country",
            "region",
            "region_iso",
            "global_region",
            "second_region",
            "is_assessor",
            "is_financial_specialist",
            "is_scope_employee",
            "is_data_collector",
            "is_self_assessor",
            "is_employee",
            "is_quality_reviewer",
            "is_contact",
            "is_customer_admin",
            "is_customer_user",
            "email_notifications_on",
            "can_access_trainee",
            "can_access_prod",
            "display_in_list",
            "portal_licenses",
            "expiration_date_portal_license",
            "has_valid_license",
            "assessor_license_validity",
            "self_assessments_limit",
        )
        extra_kwargs = {
            "url": {"view_name": "accounts:user-detail"},
            "organization": {"view_name": "customers:customer-detail"},
            "groups": {"view_name": "accounts:group-detail", "read_only": True},
            "employee": {"view_name": "hrm:employee-detail", "read_only": True},
            "assessor": {"view_name": "hrm:assessor-detail", "read_only": True},
            "customer_contact": {
                "view_name": "customers:contact-detail",
                "read_only": True,
            },
            "portal_licenses": {"view_name": "hrm:portallicense-detail"},
        }


class GroupSerializer(CustomHyperlinkedModelSerializer):
    class Meta:
        model = Group
        fields = ("id", "url", "name", "user_set")
        extra_kwargs = {
            "url": {"view_name": "accounts:group-detail"},
            "user_set": {"view_name": "accounts:user-detail"},
        }


class DashboardSerializer(serializers.Serializer):
    countries = serializers.SerializerMethodField()
    assessors = serializers.SerializerMethodField()
    totals = serializers.SerializerMethodField()
    projects = serializers.SerializerMethodField()
    clients = serializers.SerializerMethodField()

    def get_countries(self, *args, **kwargs):
        countries = (
            ProducingOrganization.objects.all()
            .values_list("country")
            .annotate(number_of_assessments=Count("assessments"))
            .annotate(progress=Avg("assessments__progress"))
            .distinct()
            .values("country", "number_of_assessments", "progress")
            .order_by("-number_of_assessments")
        )
        request = self.context.get("request", None)
        if request:
            language = request.user.language
        else:
            language = "en"
        if language != "en":
            if not TranslatableTextField.in_cache(language):
                TranslatableTextField.refresh_cache(language)
        return [
            {
                "abbr": "KE",
                "name": item["country"],
                "display_name": (
                    (
                        item["country"]
                        if language == "en"
                        else (
                            TranslatableTextField.from_cache(language, item["country"])
                        )
                    )
                    if item["country"]
                    else ""
                ),
                "number_of_assessments": item["number_of_assessments"],
                "progress": item["progress"],
            }
            for item in countries
            if item["number_of_assessments"] > 0
        ]

    def get_assessors(self, *args, **kwargs):
        return [
            {
                "name": a.user.get_full_name(),
                "active_assessments": "",
                "total_assessments": a.assessment_count,
            }
            for a in (
                Assessor.objects.filter(user__is_active=True)
                .annotate(assessment_count=Count("assessmentassignments"))
                .select_related("user")
                .order_by("-assessment_count")
                .distinct()[:20]
            )
        ]

    def get_totals(self, *args, **kwargs):
        return {
            "projects_total": Project.objects.exclude(customer__pk=1963).count(),
            "assessments_this_month": (
                Assessment.objects.exclude(project__customer__pk=1963)
                .filter(date__month=datetime.date.today().month)
                .count()
            ),
            "assessments_this_year": (
                Assessment.objects.exclude(project__customer__pk=1963)
                .filter(date__year=datetime.date.today().year)
                .count()
            ),
            "assessments_total": Assessment.objects.exclude(
                project__customer__pk=1963
            ).count(),
            "assessments_pipeline": (
                Assessment.objects.exclude(project__customer__pk=1963)
                .filter(date__gt=datetime.date.today())
                .count()
            ),
            "assessors_total": (Assessor.objects.filter(user__is_active=True).count()),
        }

    def get_projects(self, *args, **kwargs):
        return [
            {"id": p.id, "name": p.name, "deadline": p.deadline}
            for p in (
                Project.objects.exclude(customer__pk=1963).filter(
                    deadline__gte=(datetime.date.today() - datetime.timedelta(days=30))
                )
            )
        ]

    def get_clients(self, *args, **kwargs):
        return [
            {"id": client.id, "name": client.name}
            for client in (
                Customer.objects.filter(
                    id__in=(
                        AssessmentAssignment.objects.exclude(
                            assessment__project__customer__pk=1963
                        )
                        .filter(
                            Q(locked_for_employee=False) | Q(locked_for_assessor=False)
                        )
                        .values_list("assessment__project__customer__id", flat=True)
                    )
                ).distinct()
            )
        ]

    class Meta:
        fields = ["countries", "assessors", "totals", "projects", "clients"]
        read_only_fields = ["countries", "assessors", "totals", "projects", "clients"]


class PasswordResetRequestSerializer(serializers.Serializer):
    email = serializers.EmailField()

    def validate_email(self, value):
        email = value
        if not User.objects.filter(email=email).exists():
            raise serializers.ValidationError("")
        return value

    def save(self):
        email = self.validated_data.get("email")
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            pass
        else:
            return PasswordResetRequest.objects.get_or_create(user=user)[0]


class PasswordResetTokenSerializer(serializers.Serializer):
    token = serializers.CharField(min_length=36, max_length=36)

    def validate_token(self, value):
        token = value
        data = {}
        if PasswordResetRequest.objects.filter(uuid=token).exists():
            data = {"token_valid": True}
        else:
            data = {"token_valid": False}
        return data


class PasswordResetApplySerializer(serializers.Serializer):
    token = serializers.CharField(min_length=36, max_length=36)
    password = serializers.CharField()

    def validate_password(self, value):
        password = value
        invalid_password_re = (
            r"^("
            "([^A-Za-z]|[A-Z])+"  # No lowercase letters
            "|([^A-Za-z]|[a-z])+"  # No uppercase letters
            "|([A-Z]|[a-z])+"  # No numbers or special characters
            "|.{0,7})"  # Too short
            "$"
        )
        if re.match(invalid_password_re, password):
            raise serializers.ValidationError("Password does not match rules")
        return value

    def validate_token(self, value):
        token = value
        if not PasswordResetRequest.objects.filter(uuid=token).exists():
            raise serializers.ValidationError(
                (
                    "Invalid token. You have already used the link once to "
                    "reset your password. Please request for another Reset "
                    "Link."
                )
            )
        return value

    def save(self):
        token = self.validated_data.get("token")
        password = self.validated_data.get("password")
        password_reset_request = PasswordResetRequest.objects.get(uuid=token)
        user = password_reset_request.user
        user.set_password(password)
        user.save()
        password_reset_request.delete()


class EmailAvailableSerializer(serializers.Serializer):
    email = serializers.EmailField()

    def save(self):
        email = self.validated_data["email"]
        exists = User.objects.filter(email=email).exists()
        if exists:
            user = User.objects.get(email=email)
            if (
                user.is_employee
                or user.is_quality_reviewer
                or user.is_customer_admin
                or user.is_customer_user
            ):
                dashboard_access = True
            else:
                dashboard_access = False
        else:
            dashboard_access = False
        return {"is_available": not exists, "dashboard_access": dashboard_access}


class UserImpersonationSerializer(serializers.Serializer):
    email = serializers.EmailField()

    class Meta:
        fields = ["email"]

    def validate_email(self, value):
        if not User.objects.filter(email=value).exists():
            raise ValidationError(_("User does not exist"))
        origin = self.context["request"].META.get("HTTP_ORIGIN")
        if origin in [
            settings.FRONTEND_DASHBOARD,
            "http://localhost:8082",
            settings.FRONTEND_PORTAL,
        ]:
            contact_exists = (
                Contact.objects.exclude(access_to_dashboard="")
                .filter(user__email=value)
                .filter(user__is_active=True)
                .exists()
            )
            employee_exists = (
                Employee.objects.filter(user__email=value)
                .filter(user__is_active=True)
                .exists()
            )
            if contact_exists or employee_exists:
                return value
        if origin in [settings.FRONTEND_APP, "http://localhost:8080"]:
            assessor_exists = (
                Assessor.objects.filter(user__email=value)
                .filter(user__is_active=True)
                .exists()
            )
            if assessor_exists:
                return value
        raise ValidationError(_("Invalid user to impersonate"))

    def save(self):
        email = self.validated_data.get("email")
        user = User.objects.get(email=email)
        payload = jwt_payload_handler(user)

        return {"token": jwt_encode_handler(payload)}


class SendToTrainingEmployeeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Employee
        fields = ("qc_only",)


class SendToTrainingToolTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ToolType
        fields = ("name",)


class SendToTrainingAssessorSerializer(serializers.ModelSerializer):
    tools = SendToTrainingToolTypeSerializer(many=True)
    expiration_date_assessor = serializers.DateField(allow_null=True)
    expiration_date_financial_specialist = serializers.DateField(allow_null=True)

    def create(self, validated_data):
        tools = validated_data.pop("tools")
        tools = ToolType.objects.filter(
            name__in=[tooltype["name"] for tooltype in tools]
        )
        validated_data["tools"] = tools
        instance = super().create(validated_data)
        expiration_date_assessor = validated_data.get("expiration_date_assessor")
        if expiration_date_assessor:
            AssessorTraining.objects.create(
                assessor=instance, certification_valid_until=expiration_date_assessor
            )
        expiration_date_financial_specialist = validated_data.get(
            "expiration_date_financial_specialist"
        )
        if expiration_date_financial_specialist:
            FinancialSpecialistTraining.objects.create(
                assessor=instance,
                certification_valid_until=expiration_date_financial_specialist,
            )
        return instance

    def update(self, instance, validated_data):
        tools = validated_data.pop("tools")
        tools = ToolType.objects.filter(
            name__in=[tooltype["name"] for tooltype in tools]
        )
        validated_data["tools"] = tools
        instance = super().update(instance, validated_data)
        expiration_date_assessor = validated_data.get("expiration_date_assessor")
        if expiration_date_assessor:
            try:
                assessor_training = AssessorTraining.objects.filter(
                    assessor=instance, certification_valid_until=None
                ).order_by("-pk")[0]
            except IndexError:
                try:
                    assessor_training = AssessorTraining.objects.filter(
                        assessor=instance
                    ).order_by("-pk")[0]
                except IndexError:
                    assessor_training = AssessorTraining.objects.create(
                        assessor=instance
                    )
            assessor_training.certification_valid_until = expiration_date_assessor
            assessor_training.save()
        expiration_date_financial_specialist = validated_data.get(
            "expiration_date_financial_specialist"
        )
        if expiration_date_financial_specialist:
            try:
                financial_specialist_training = (
                    FinancialSpecialistTraining.objects.filter(
                        assessor=instance, certification_valid_until=None
                    ).order_by("-pk")[0]
                )
            except IndexError:
                try:
                    financial_specialist_training = (
                        FinancialSpecialistTraining.objects.filter(
                            assessor=instance
                        ).order_by("-pk")[0]
                    )
                except IndexError:
                    financial_specialist_training = (
                        FinancialSpecialistTraining.objects.create(assessor=instance)
                    )
            financial_specialist_training.certification_valid_until = (
                expiration_date_assessor
            )
            financial_specialist_training.save()
        return instance

    class Meta:
        model = Assessor
        fields = (
            "function",
            "background",
            "comment",
            "financial_specialist",
            "is_assessor",
            "expiration_date_assessor",
            "expiration_date_financial_specialist",
            "tools",
            "available_to_all_organizations",
        )


class SendToTrainingContactSerializer(serializers.ModelSerializer):
    class Meta:
        model = Contact
        fields = ("position", "access_to_dashboard")


class SendToTrainingCustomerSerializer(serializers.ModelSerializer):
    def save(self, **kwargs):
        try:
            customer = Customer.objects.get(name=self.validated_data["name"])
        except Customer.DoesNotExist:
            customer = super().save(**kwargs)
        return customer

    class Meta:
        model = Customer
        fields = ("name", "abbreviation")
        extra_kwargs = {"name": {"validators": []}}


class SendToTrainingUserSerializer(serializers.ModelSerializer):
    employee = SendToTrainingEmployeeSerializer(allow_null=True, required=False)
    assessor = SendToTrainingAssessorSerializer(allow_null=True, required=False)
    customer_contact = SendToTrainingContactSerializer(allow_null=True, required=False)
    organization = SendToTrainingCustomerSerializer(allow_null=True, required=False)

    def validate_training_id(self, value):
        if value and not User.objects.filter(pk=value).exists():
            raise ValidationError("user with id matching training_id doesn't exist")
        return value

    def validate(self, data):
        different_user_with_email = (
            data.get("training_id") is None
            and data.get("email") is not None
            and User.objects.filter(email=data["email"]).exists()
        ) or (
            data.get("training_id") is not None
            and data.get("email") is not None
            and User.objects.filter(email=data["email"]).exists()
            and User.objects.filter(email=data["email"]).values_list("id", flat=True)[0]
            != data.get("training_id")
        )
        if different_user_with_email:
            raise ValidationError(
                {"email": "different user with same email already exists"}
            )
        return data

    def save(self, **kwargs):
        # set instance here if user already exists
        user_id = self.validated_data.get("training_id")
        if user_id:
            self.instance = User.objects.get(pk=user_id)
            created = False
        else:
            created = True
        instance = super().save(**kwargs)
        return instance, created

    def create(self, validated_data):
        nested_serializers = {}
        for (name, field) in self.fields.items():
            if isinstance(field, serializers.BaseSerializer):
                value = validated_data.pop(name)
                if value is not None:
                    if "user" in [item.name for item in field.Meta.model._meta.fields]:
                        nested_serializers[name] = value
                    else:
                        nested_serializer = type(self.fields[name])(data=value)
                        nested_serializer.is_valid(raise_exception=True)
                        validated_data[name] = nested_serializer.save()
        instance = super().create(validated_data)
        for name, value in nested_serializers.items():
            nested_serializer = type(self.fields[name])(data=value)
            nested_serializer.is_valid(raise_exception=True)
            validated_data[name] = nested_serializer.save(user=instance)
        return instance

    def update(self, instance, validated_data):
        nested_serializers = {}
        for (name, field) in self.fields.items():
            if isinstance(field, serializers.BaseSerializer):
                value = validated_data.pop(name, None)
                if value is not None:
                    if "user" in [item.name for item in field.Meta.model._meta.fields]:
                        nested_serializers[name] = value
                    else:
                        nested_serializer = type(self.fields[name])(data=value)
                        nested_serializer.is_valid(raise_exception=True)
                        validated_data[name] = nested_serializer.save()
        instance = super().update(instance, validated_data)
        for name, value in nested_serializers.items():
            try:
                nested_instance = getattr(instance, name)
            except ObjectDoesNotExist:
                nested_instance = None
            nested_serializer = type(self.fields[name])(
                instance=nested_instance, data=value
            )
            nested_serializer.is_valid(raise_exception=True)
            validated_data[name] = nested_serializer.save(user=instance)
        return instance

    class Meta:
        model = User
        fields = (
            "username",
            "password",
            "email",
            "first_name",
            "last_name",
            "language",
            "phone_number",
            "allowed_frontends",
            "email_notifications_on",
            "languages_spoken",
            "street",
            "street_number",
            "zipcode",
            "city",
            "region",
            "region_iso",
            "country",
            "global_region",
            "second_region",
            "employee",
            "assessor",
            "customer_contact",
            "organization",
            "training_id",
            "display_in_list",
        )
        extra_kwargs = {"email": {"validators": [], "required": False}}


class SendToTrainingResponseUserSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()

    def update(self, instance, validated_data):
        instance.training_id = validated_data["id"]
        instance.can_access_trainee = True
        instance.save()
        return instance

    class Meta:
        model = User
        fields = ("id",)
