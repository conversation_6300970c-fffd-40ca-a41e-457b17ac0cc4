import json

from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import AgentBankAccountLoanHistoryFactory, AssessmentFactory
from assessments.models import AgentBankAccountLoanHistory
from assessments.tasks import build_response_tree_task
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class AgentBankAccountLoanHistoryTestCase(DenormMixin, AssessorJWTTestCase):
    def test_agent_bank_account_loan_history_exists(self):
        """
        assessment.agent_bank_account_loan_history should be auto-created when assessment is created
        """
        assessment = AssessmentFactory.create(build_response_tree_completed=False)
        build_response_tree_task(assessment.pk)
        self.assertIsNotNone(assessment.agent_bank_account_loan_history)

    def test_agent_bank_account_loan_history_visible_in_api(self):
        """
        agent_bank_account_loan_history should be visible on assessment in api
        """
        agent_bank_account_loan_history = AgentBankAccountLoanHistoryFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse(
            "assessments:assessment-detail",
            [agent_bank_account_loan_history.assessment_id],
        )
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code, response.content)
        response_dict = json.loads(response.content)
        self.assertIn("agent_bank_account_loan_history", response_dict)
        agent_bank_account_loan_history_dict = response_dict[
            "agent_bank_account_loan_history"
        ]
        self.assertSetEqual(
            set(
                [
                    "id",
                    "url",
                    "modified_at",
                    "assessment",
                    "has_bank_account",
                    "has_mobile_account",
                    "has_loan_history",
                    "has_paid_back_loans",
                ]
            ),
            set(agent_bank_account_loan_history_dict.keys()),
        )

    def test_agent_bank_account_loan_history_patchable(self):
        """
        agent_bank_account_loan_history should be patchable through api
        """
        agent_bank_account_loan_history = AgentBankAccountLoanHistoryFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse(
            "assessments:agentbankaccountloanhistory-detail",
            [agent_bank_account_loan_history.pk],
        )
        data = {
            "has_bank_account": True,
            "has_mobile_account": False,
            "has_loan_history": True,
            "has_paid_back_loans": False,
        }
        self.client.patch(url, json.dumps(data), content_type="application/json")
        db_obj = AgentBankAccountLoanHistory.objects.get(
            pk=agent_bank_account_loan_history.pk
        )
        self.assertTrue(db_obj.has_bank_account)
        self.assertFalse(db_obj.has_mobile_account)
        self.assertTrue(db_obj.has_loan_history)
        self.assertFalse(db_obj.has_paid_back_loans)
