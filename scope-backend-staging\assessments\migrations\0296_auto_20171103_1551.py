# Generated by Django 1.11.7 on 2017-11-03 15:51


from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [("assessments", "0295_auto_20171025_1549")]

    operations = [
        migrations.RemoveField(model_name="assessmentcomment", name="_assessment"),
        migrations.RemoveField(model_name="assessmentcomment", name="assessment"),
        migrations.RemoveField(model_name="assessmentcomment", name="user"),
        migrations.RemoveField(model_name="balancesheetcomment", name="_assessment"),
        migrations.RemoveField(model_name="balancesheetcomment", name="balancesheet"),
        migrations.RemoveField(model_name="balancesheetcomment", name="user"),
        migrations.RemoveField(model_name="bankaccountcomment", name="_assessment"),
        migrations.RemoveField(model_name="bankaccountcomment", name="bankaccount"),
        migrations.RemoveField(model_name="bankaccountcomment", name="user"),
        migrations.Remo<PERSON><PERSON><PERSON>(
            model_name="basicfinancialinfocomment", name="_assessment"
        ),
        migrations.RemoveField(
            model_name="basicfinancialinfocomment", name="basicfinancialinfo"
        ),
        migrations.RemoveField(model_name="basicfinancialinfocomment", name="user"),
        migrations.RemoveField(
            model_name="cashflowstatementcomment", name="_assessment"
        ),
        migrations.RemoveField(
            model_name="cashflowstatementcomment", name="cashflowstatement"
        ),
        migrations.RemoveField(model_name="cashflowstatementcomment", name="user"),
        migrations.RemoveField(model_name="collateralassetcomment", name="_assessment"),
        migrations.RemoveField(
            model_name="collateralassetcomment", name="collateralasset"
        ),
        migrations.RemoveField(model_name="collateralassetcomment", name="user"),
        migrations.RemoveField(model_name="costofsalecomment", name="_assessment"),
        migrations.RemoveField(model_name="costofsalecomment", name="costofsale"),
        migrations.RemoveField(model_name="costofsalecomment", name="user"),
        migrations.RemoveField(
            model_name="documentavailabilityresponsecomment", name="_assessment"
        ),
        migrations.RemoveField(
            model_name="documentavailabilityresponsecomment",
            name="documentavailabilityresponse",
        ),
        migrations.RemoveField(
            model_name="documentavailabilityresponsecomment", name="user"
        ),
        migrations.RemoveField(model_name="enablingplayercomment", name="_assessment"),
        migrations.RemoveField(
            model_name="enablingplayercomment", name="enablingplayer"
        ),
        migrations.RemoveField(model_name="enablingplayercomment", name="user"),
        migrations.RemoveField(model_name="expensecomment", name="_assessment"),
        migrations.RemoveField(model_name="expensecomment", name="expense"),
        migrations.RemoveField(model_name="expensecomment", name="user"),
        migrations.RemoveField(model_name="financialratiocomment", name="_assessment"),
        migrations.RemoveField(
            model_name="financialratiocomment", name="financialratio"
        ),
        migrations.RemoveField(model_name="financialratiocomment", name="user"),
        migrations.RemoveField(model_name="financialscorescomment", name="_assessment"),
        migrations.RemoveField(
            model_name="financialscorescomment", name="financialscores"
        ),
        migrations.RemoveField(model_name="financialscorescomment", name="user"),
        migrations.RemoveField(model_name="generalcheckscomment", name="_assessment"),
        migrations.RemoveField(model_name="generalcheckscomment", name="generalchecks"),
        migrations.RemoveField(model_name="generalcheckscomment", name="user"),
        migrations.RemoveField(model_name="granthistorycomment", name="_assessment"),
        migrations.RemoveField(model_name="granthistorycomment", name="granthistory"),
        migrations.RemoveField(model_name="granthistorycomment", name="user"),
        migrations.RemoveField(model_name="inputpurchasecomment", name="_assessment"),
        migrations.RemoveField(model_name="inputpurchasecomment", name="inputpurchase"),
        migrations.RemoveField(model_name="inputpurchasecomment", name="user"),
        migrations.RemoveField(model_name="insurancecomment", name="_assessment"),
        migrations.RemoveField(model_name="insurancecomment", name="insurance"),
        migrations.RemoveField(model_name="insurancecomment", name="user"),
        migrations.RemoveField(model_name="loanapplicationcomment", name="_assessment"),
        migrations.RemoveField(
            model_name="loanapplicationcomment", name="loanapplication"
        ),
        migrations.RemoveField(model_name="loanapplicationcomment", name="user"),
        migrations.RemoveField(model_name="loanhistorycomment", name="_assessment"),
        migrations.RemoveField(model_name="loanhistorycomment", name="loanhistory"),
        migrations.RemoveField(model_name="loanhistorycomment", name="user"),
        migrations.RemoveField(model_name="loanrequirementcomment", name="_assessment"),
        migrations.RemoveField(
            model_name="loanrequirementcomment", name="loanrequirement"
        ),
        migrations.RemoveField(model_name="loanrequirementcomment", name="user"),
        migrations.RemoveField(
            model_name="producingorganizationdetailscomment", name="_assessment"
        ),
        migrations.RemoveField(
            model_name="producingorganizationdetailscomment",
            name="producingorganizationdetails",
        ),
        migrations.RemoveField(
            model_name="producingorganizationdetailscomment", name="user"
        ),
        migrations.RemoveField(model_name="productcomment", name="_assessment"),
        migrations.RemoveField(model_name="productcomment", name="product"),
        migrations.RemoveField(model_name="productcomment", name="user"),
        migrations.RemoveField(
            model_name="productionfigurecomment", name="_assessment"
        ),
        migrations.RemoveField(
            model_name="productionfigurecomment", name="productionfigure"
        ),
        migrations.RemoveField(model_name="productionfigurecomment", name="user"),
        migrations.RemoveField(
            model_name="productionpurchasecomment", name="_assessment"
        ),
        migrations.RemoveField(
            model_name="productionpurchasecomment", name="productionpurchase"
        ),
        migrations.RemoveField(model_name="productionpurchasecomment", name="user"),
        migrations.RemoveField(model_name="productionsalecomment", name="_assessment"),
        migrations.RemoveField(
            model_name="productionsalecomment", name="productionsale"
        ),
        migrations.RemoveField(model_name="productionsalecomment", name="user"),
        migrations.RemoveField(
            model_name="profitlossstatementcomment", name="_assessment"
        ),
        migrations.RemoveField(
            model_name="profitlossstatementcomment", name="profitlossstatement"
        ),
        migrations.RemoveField(model_name="profitlossstatementcomment", name="user"),
        migrations.RemoveField(model_name="ratioscorescomment", name="_assessment"),
        migrations.RemoveField(model_name="ratioscorescomment", name="ratioscores"),
        migrations.RemoveField(model_name="ratioscorescomment", name="user"),
        migrations.RemoveField(model_name="shareholdercomment", name="_assessment"),
        migrations.RemoveField(model_name="shareholdercomment", name="shareholder"),
        migrations.RemoveField(model_name="shareholdercomment", name="user"),
        migrations.RemoveField(model_name="subresponsecomment", name="_assessment"),
        migrations.RemoveField(model_name="subresponsecomment", name="subresponse"),
        migrations.RemoveField(model_name="subresponsecomment", name="user"),
        migrations.RemoveField(
            model_name="unscoredsectionresponsecomment", name="_assessment"
        ),
        migrations.RemoveField(
            model_name="unscoredsectionresponsecomment", name="unscoredsectionresponse"
        ),
        migrations.RemoveField(
            model_name="unscoredsectionresponsecomment", name="user"
        ),
        migrations.RemoveField(
            model_name="unscoredtoolresponsecomment", name="_assessment"
        ),
        migrations.RemoveField(
            model_name="unscoredtoolresponsecomment", name="unscoredtoolresponse"
        ),
        migrations.RemoveField(model_name="unscoredtoolresponsecomment", name="user"),
        migrations.RemoveField(
            model_name="valuechainplayercomment", name="_assessment"
        ),
        migrations.RemoveField(model_name="valuechainplayercomment", name="user"),
        migrations.RemoveField(
            model_name="valuechainplayercomment", name="valuechainplayer"
        ),
        migrations.DeleteModel(name="AssessmentComment"),
        migrations.DeleteModel(name="BalanceSheetComment"),
        migrations.DeleteModel(name="BankAccountComment"),
        migrations.DeleteModel(name="BasicFinancialInfoComment"),
        migrations.DeleteModel(name="CashFlowStatementComment"),
        migrations.DeleteModel(name="CollateralAssetComment"),
        migrations.DeleteModel(name="CostOfSaleComment"),
        migrations.DeleteModel(name="DocumentAvailabilityResponseComment"),
        migrations.DeleteModel(name="EnablingPlayerComment"),
        migrations.DeleteModel(name="ExpenseComment"),
        migrations.DeleteModel(name="FinancialRatioComment"),
        migrations.DeleteModel(name="FinancialScoresComment"),
        migrations.DeleteModel(name="GeneralChecksComment"),
        migrations.DeleteModel(name="GrantHistoryComment"),
        migrations.DeleteModel(name="InputPurchaseComment"),
        migrations.DeleteModel(name="InsuranceComment"),
        migrations.DeleteModel(name="LoanApplicationComment"),
        migrations.DeleteModel(name="LoanHistoryComment"),
        migrations.DeleteModel(name="LoanRequirementComment"),
        migrations.DeleteModel(name="ProducingOrganizationDetailsComment"),
        migrations.DeleteModel(name="ProductComment"),
        migrations.DeleteModel(name="ProductionFigureComment"),
        migrations.DeleteModel(name="ProductionPurchaseComment"),
        migrations.DeleteModel(name="ProductionSaleComment"),
        migrations.DeleteModel(name="ProfitLossStatementComment"),
        migrations.DeleteModel(name="RatioScoresComment"),
        migrations.DeleteModel(name="ShareholderComment"),
        migrations.DeleteModel(name="SubResponseComment"),
        migrations.DeleteModel(name="UnscoredSectionResponseComment"),
        migrations.DeleteModel(name="UnscoredToolResponseComment"),
        migrations.DeleteModel(name="ValueChainPlayerComment"),
    ]
