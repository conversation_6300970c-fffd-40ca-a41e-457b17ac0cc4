from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0209_auto_20160920_1812")]

    operations = [
        migrations.AlterField(
            model_name="assessmentdocument",
            name="accepted",
            field=models.BooleanField(default=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheetdocument",
            name="accepted",
            field=models.BooleanField(default=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="costofsaledocument",
            name="accepted",
            field=models.BooleanField(default=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="documentavailabilityresponsedocument",
            name="accepted",
            field=models.BooleanField(default=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="expensedocument",
            name="accepted",
            field=models.BooleanField(default=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="profitlossstatementdocument",
            name="accepted",
            field=models.BooleanField(default=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="response",
            name="accepted",
            field=models.BooleanField(default=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="responsedocument",
            name="accepted",
            field=models.BooleanField(default=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="subresponse",
            name="accepted",
            field=models.BooleanField(default=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="subresponsedocument",
            name="accepted",
            field=models.BooleanField(default=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="unscoredsectionresponse",
            name="accepted",
            field=models.BooleanField(default=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="unscoredtoolresponse",
            name="accepted",
            field=models.BooleanField(default=True),
            preserve_default=True,
        ),
    ]
