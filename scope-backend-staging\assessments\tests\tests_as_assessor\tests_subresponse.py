import datetime
import json

from denorm import flush
from django.conf import settings
from freezegun import freeze_time
from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import (
    AssessmentAssignmentFactory,
    AssessmentFactory,
    SubResponseFactory,
)
from assessments.models import Assessment, SubResponse
from libs.test_helpers import AssessorJWTTestCase, DenormMixin
from products.factories import SubQuestionFactory, SubQuestionOptionFactory


class SubResponseTestCase(DenormMixin, AssessorJWTTestCase):
    def test_can_be_none_of_the_above_in_api(self):
        """
        can_be_none_of_the_above should be visible on a subresponse
        """
        subresponse = SubResponseFactory.create(
            subquestion__can_be_none_of_the_above=True,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        flush()
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("can_be_none_of_the_above", response_dict)
        self.assertTrue(response_dict["can_be_none_of_the_above"])

    def test_none_of_the_above_patchable(self):
        """
        none_of_the_above needs to be patchable
        when can_be_none_of_the_above is True
        """
        subresponse = SubResponseFactory.create(
            subquestion__can_be_none_of_the_above=True,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        flush()
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        data = {"none_of_the_above": True}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("none_of_the_above", response_dict)
        self.assertTrue(response_dict["none_of_the_above"])

    def test_none_of_the_above_not_patchable(self):
        """
        none_of_the_above needs to be not patchable
        when can_be_none_of_the_above is False
        """
        subresponse = SubResponseFactory.create(
            subquestion__can_be_none_of_the_above=False,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        flush()
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        data = {"none_of_the_above": True}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("none_of_the_above", response_dict)
        self.assertEqual(
            ["none_of_the_above not allowed"], response_dict["none_of_the_above"]
        )

    def test_none_of_the_above_defaults_to_false(self):
        """
        none_of_the_above should default to False
        """
        subresponse = SubResponseFactory.create()
        self.assertFalse(subresponse.none_of_the_above)

    def test_score_is_one_when_none_of_the_above(self):
        """
        When none_of_the_above is True, score should be 1
        """
        subresponse = SubResponseFactory.create(none_of_the_above=True)
        self.assertEqual(1, subresponse.score)

    def test_not_empty_when_none_of_the_above(self):
        """
        When none_of_the_above is True, not_empty should be True
        """
        subresponse = SubResponseFactory.create(none_of_the_above=True)
        self.assertTrue(subresponse.not_empty)

    def test_can_be_not_relevant_in_api(self):
        """
        can_be_not_relevant should be visible on a subresponse
        """
        subresponse = SubResponseFactory.create(
            subquestion__can_be_not_relevant=True,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        flush()
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("can_be_not_relevant", response_dict)
        self.assertTrue(response_dict["can_be_not_relevant"])

    def test_not_relevant_patchable(self):
        """
        not_relevant needs to be patchable
        when can_be_not_relevant is True
        """
        subresponse = SubResponseFactory.create(
            subquestion__can_be_not_relevant=True,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        flush()
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        data = {"not_relevant": True}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("not_relevant", response_dict)
        self.assertTrue(response_dict["not_relevant"])

    def test_not_relevant_not_patchable(self):
        """
        not_relevant needs to be not patchable
        when can_be_not_relevant is False
        """
        subresponse = SubResponseFactory.create(
            subquestion__can_be_not_relevant=False,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        flush()
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        data = {"not_relevant": True}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("not_relevant", response_dict)
        self.assertEqual(["not_relevant not allowed"], response_dict["not_relevant"])

    def test_not_relevant_defaults_to_false(self):
        """
        not_relevant should default to False
        """
        subresponse = SubResponseFactory.create()
        self.assertFalse(subresponse.not_relevant)

    def test_score_is_zero_when_not_relevant(self):
        """
        When not_relevant is True, score should be 0
        """
        subresponse = SubResponseFactory.create(not_relevant=True)
        self.assertEqual(0, subresponse.score)

    def test_not_empty_when_not_relevant(self):
        """
        When not_relevant is True, not_empty should be True
        """
        subresponse = SubResponseFactory.create(not_relevant=True)
        self.assertTrue(subresponse.not_empty)

    def test_min_value_in_api(self):
        """
        min_value should be in api for numeric subresponse
        """
        subresponse = SubResponseFactory.create(
            subquestion__type="numeric",
            assessment__assessmentassignments__assessor=self.assessor,
        )
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("min_value", response_dict)

    def test_min_value_number(self):
        """
        min_value should give the correct number for a closed range
        """
        subquestion = SubQuestionFactory.create(type="numeric")
        for i in range(-10, 10):
            SubQuestionOptionFactory.create(subquestion=subquestion, range_min=i)
        subresponse = SubResponseFactory.create(subquestion=subquestion)
        self.assertEqual(-10, subresponse.min_value)

    def test_min_value_neg_inf(self):
        """
        min_value should give None for an open range
        """
        subquestion = SubQuestionFactory.create(type="numeric")
        for i in range(-10, 10):
            SubQuestionOptionFactory.create(subquestion=subquestion, range_min=i)
        SubQuestionOptionFactory.create(subquestion=subquestion, range_min=None)
        subresponse = SubResponseFactory.create(subquestion=subquestion)
        self.assertEqual(None, subresponse.min_value)

    def test_min_value_not_patchable(self):
        """
        min_value should not be patchable
        """
        subresponse = SubResponseFactory.create()
        data = {"min_value": -10}
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        self.client.patch(url, json.dumps(data), content_type="application/json")
        subresponse = SubResponse.objects.get(pk=subresponse.pk)
        self.assertIsNone(subresponse.min_value)

    def test_max_value_in_api(self):
        """
        max_value should be in api for numeric subresponse
        """
        subresponse = SubResponseFactory.create(
            subquestion__type="numeric",
            assessment__assessmentassignments__assessor=self.assessor,
        )
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("max_value", response_dict)

    def test_max_value_number(self):
        """
        max_value should give the correct number for a closed range
        """
        subquestion = SubQuestionFactory.create(type="numeric")
        for i in range(-10, 10):
            SubQuestionOptionFactory.create(subquestion=subquestion, range_max=i)
        subresponse = SubResponseFactory.create(subquestion=subquestion)
        self.assertEqual(9, subresponse.max_value)

    def test_max_value_neg_inf(self):
        """
        max_value should give None for an open range
        """
        subquestion = SubQuestionFactory.create(type="numeric")
        for i in range(-10, 10):
            SubQuestionOptionFactory.create(subquestion=subquestion, range_max=i)
        SubQuestionOptionFactory.create(subquestion=subquestion, range_max=None)
        subresponse = SubResponseFactory.create(subquestion=subquestion)
        self.assertEqual(None, subresponse.max_value)

    def test_max_value_not_patchable(self):
        """
        max_value should not be patchable
        """
        subresponse = SubResponseFactory.create()
        data = {"max_value": -10}
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        self.client.patch(url, json.dumps(data), content_type="application/json")
        subresponse = SubResponse.objects.get(pk=subresponse.pk)
        self.assertIsNone(subresponse.max_value)

    def test_value_outside_min_and_max_fails(self):
        """
        A subresponse should return a 400 when value does not satisfy
        min_value <= value <= max_value
        """
        subquestion = SubQuestionFactory.create(type="numeric")
        SubQuestionOptionFactory.create(
            subquestion=subquestion, range_min=0, range_max=10, score=1
        )
        subresponse = SubResponseFactory.create(
            subquestion=subquestion,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        flush()
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        data = {"value": -1}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("value", response_dict)
        self.assertEqual(
            ["-1 does not satisfy 0.0000 <= value <= 10.0000"], response_dict["value"]
        )

    def test_value_inside_min_and_max_works(self):
        """
        A subresponse should return a 200 when value does satisfy
        min_value <= value <= max_value
        """
        subquestion = SubQuestionFactory.create(type="numeric")
        SubQuestionOptionFactory.create(
            subquestion=subquestion, range_min=0, range_max=10, score=1
        )
        subresponse = SubResponseFactory.create(
            subquestion=subquestion,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        flush()
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        data = {"value": 5}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("value", response_dict)
        self.assertEqual(5, response_dict["value"])

    def test_value_exactly_min_works(self):
        """
        A subresponse should return a 200 when value == min_value
        """
        subquestion = SubQuestionFactory.create(type="numeric")
        SubQuestionOptionFactory.create(
            subquestion=subquestion, range_min=0, range_max=5, score=1
        )
        SubQuestionOptionFactory.create(
            subquestion=subquestion, range_min=5, range_max=10, score=2
        )
        subresponse = SubResponseFactory.create(
            subquestion=subquestion,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        flush()
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        data = {"value": 0}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("value", response_dict)
        self.assertEqual(0, response_dict["value"])

    def test_value_exactly_max_works(self):
        """
        A subresponse should return a 200 when value == max_value
        """
        subquestion = SubQuestionFactory.create(type="numeric")
        SubQuestionOptionFactory.create(
            subquestion=subquestion, range_min=0, range_max=5, score=1
        )
        SubQuestionOptionFactory.create(
            subquestion=subquestion, range_min=5, range_max=10, score=2
        )
        subresponse = SubResponseFactory.create(
            subquestion=subquestion,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        flush()
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        data = {"value": 10}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("value", response_dict)
        self.assertEqual(10, response_dict["value"])

    def test_can_not_modify_accepted_answer_after_submit(self):
        """
        Assessor should not be able to modify an accepted answer
        """
        subresponse = SubResponseFactory.create(
            assessment__assessmentassignments__submitted_at_least_once=True,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        data = {}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)

    def test_can_modify_not_accepted_answer_after_submit(self):
        """
        Assessor should be able to modify not accepted answer
        """
        subresponse = SubResponseFactory.create(
            _response__accepted=False,
            assessment__assessmentassignments__submitted_at_least_once=True,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        data = {}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_modify_answer_before_submit(self):
        """
        Assesshor should be able to modify answer before submitting
        """
        subresponse = SubResponseFactory.create(
            assessment__assessmentassignments__submitted_at_least_once=False,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        data = {}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_comment_is_always_allowed(self):
        """
        subresponse.comment modification is always allowed
        """
        subresponse = SubResponseFactory.create(
            assessment__assessmentassignments__submitted_at_least_once=True,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        data = {"comment": "bliep"}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_not_patch_on_locked_assessment(self):
        """
        Should not be allowed to patch when assessment is locked
        """
        subresponse = SubResponseFactory.create(
            assessment__assessmentassignments__locked_for_assessor=True,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        data = {}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)

    def test_submitted_at_least_once_set_properly(self):
        """
        submitted_at_least_once should be set properly
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        assignment = AssessmentAssignmentFactory.create(assessment=assessment)
        self.assertTrue(assignment.locked_for_employee)
        self.assertFalse(assignment.locked_for_assessor)
        self.assertFalse(assignment.submitted_at_least_once)
        assignment.locked_for_employee = False
        assignment.locked_for_assessor = True
        assignment.save()
        assessment = Assessment.objects.get(pk=assessment.pk)
        assignment.refresh_from_db()
        self.assertFalse(assignment.locked_for_employee)
        self.assertTrue(assignment.locked_for_assessor)
        self.assertTrue(assignment.submitted_at_least_once)

    def test_submitted_first_set_properly(self):
        """
        Date of submission can be set properly
        """
        start = settings.LAST_BREAKING_CODE_CHANGE
        ax_create_time = start + datetime.timedelta(seconds=1)
        axa_create_time = start + datetime.timedelta(seconds=2)
        submit_time = start + datetime.timedelta(seconds=3)
        refetch_time = start + datetime.timedelta(seconds=4)
        url = reverse("projects:project-list")
        with freeze_time(axa_create_time):
            assignment = AssessmentAssignmentFactory.create(
                assessor=self.assessor, assessment__date=ax_create_time
            )
        self.assertTrue(assignment.locked_for_employee)
        self.assertFalse(assignment.locked_for_assessor)
        self.assertFalse(assignment.submitted_at_least_once)
        with freeze_time(submit_time):
            assignment.locked_for_employee = False
            assignment.locked_for_assessor = True
            assignment.save()
        assignment.refresh_from_db()
        self.assertFalse(assignment.locked_for_employee)
        self.assertTrue(assignment.locked_for_assessor)
        self.assertTrue(assignment.submitted_at_least_once)

    def test_can_patch_empty_list_to_checked_options(self):
        """
        It should be possible to patch an empty list of checked options to a subresponse
        """
        subresponse = SubResponseFactory.create(
            subquestion__can_be_none_of_the_above=True,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        options = SubQuestionOptionFactory.create_batch(
            3, subquestion=subresponse.subquestion
        )
        subresponse.checked_options.set(options)
        url = reverse("assessments:subresponse-detail", [subresponse.pk])
        data = {"checked_options": []}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code, response.content)
        self.assertEqual(0, subresponse.checked_options.count())
