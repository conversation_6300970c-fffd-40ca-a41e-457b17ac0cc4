from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0025_assessmentinvitation_skill_types")]

    operations = [
        migrations.AlterField(
            model_name="assessmentassignment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="assessmentassignments",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="assessmentassignment",
            name="assessor",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="assessmentassignments",
                to="hrm.Assessor",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="assessmentinvitation",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="assessmentinvitations",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="assessmentinvitation",
            name="assessor",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="assessmentinvitations",
                to="hrm.Assessor",
            ),
            preserve_default=True,
        ),
    ]
