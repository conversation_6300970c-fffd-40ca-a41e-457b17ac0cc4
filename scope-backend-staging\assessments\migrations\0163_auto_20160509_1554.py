from django.db import migrations, transaction


def make_sure_all_copy_score_and_weight_deps_exist(assessment, SectionResponse):
    all_sections = SectionResponse.objects.filter(
        tree_id__in=(
            assessment.section_responses.all()
            .order_by()
            .values_list("tree_id", flat=True)
            .distinct()
        )
    )
    sections_missing_children = (
        all_sections.filter(children__isnull=True)
        .filter(section__children__isnull=False)
        .distinct()
    )
    for section in sections_missing_children:
        for sec in section.section.children.all():
            SectionResponse.objects.create(
                parent=section,
                section=sec,
                lft=0,
                rght=0,
                tree_id=section.tree_id,
                level=0,
            )


def connect_copy_score_and_weight_from_fks(self, SectionResponse):
    """
    Connect all _copy_score_and_weight_from FKs for sections
    """
    all_sections = SectionResponse.objects.filter(
        tree_id__in=(
            self.section_responses.all()
            .order_by()
            .values_list("tree_id", flat=True)
            .distinct()
        )
    )
    dependent_sections = (
        all_sections.filter(section__copy_score_and_weight_from__isnull=False)
        .filter(_copy_score_and_weight_from__isnull=True)
        .distinct()
    )
    dep_sec_count = dependent_sections.count()
    if dep_sec_count:
        for i, dependent_section in enumerate(dependent_sections, 1):
            dependent_section._copy_score_and_weight_from = all_sections.get(
                section=dependent_section.section.copy_score_and_weight_from
            )
            dependent_section.save()


def connect_old_copy_score_and_weight_from_fks(apps, schema_editor):
    Assessment = apps.get_model("assessments", "Assessment")
    SectionResponse = apps.get_model("assessments", "SectionResponse")
    with transaction.atomic():
        for i, assessment in enumerate(Assessment.objects.all(), 1):
            make_sure_all_copy_score_and_weight_deps_exist(assessment, SectionResponse)
            connect_copy_score_and_weight_from_fks(assessment, SectionResponse)
        # Horrible hack that imports current model to rebuild mptt fields...
        from assessments.models import SectionResponse as DjangoSectionResponse

        DjangoSectionResponse.objects.rebuild()


class Migration(migrations.Migration):

    dependencies = [("assessments", "0162_merge")]

    operations = [migrations.RunPython(connect_old_copy_score_and_weight_from_fks)]
