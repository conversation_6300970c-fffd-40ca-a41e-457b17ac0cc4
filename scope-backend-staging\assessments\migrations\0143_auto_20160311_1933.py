from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0142_subresponse_not_relevant")]

    operations = [
        migrations.AlterModelOptions(
            name="sectionresponse",
            options={"ordering": ("section_display_position", "section__position")},
        ),
        migrations.AddField(
            model_name="financialratio",
            name="_balancesheet",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="_financial_ratios",
                blank=True,
                editable=False,
                to="assessments.BalanceSheet",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="financialratio",
            name="_profitlossstatement",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="_financial_ratios",
                blank=True,
                editable=False,
                to="assessments.ProfitLossStatement",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="financialratio",
            name="_profitlossstatement_previous_year",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="_previous_financial_ratios",
                blank=True,
                editable=False,
                to="assessments.ProfitLossStatement",
                null=True,
            ),
            preserve_default=True,
        ),
    ]
