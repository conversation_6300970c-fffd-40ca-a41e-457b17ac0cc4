# Generated by Django 1.10.5 on 2017-03-08 10:31


from django.db import migrations


def move_contact_pos_to_user(apps, schema_editor):
    Contact = apps.get_model("customers", "Contact")

    for contact in Contact.objects.filter(customers__isnull=False):
        user = contact.user
        user.organization = contact.customers.all()[0]
        user.save()


def add_fake_organization_to_user(apps, schema_editor):
    User = apps.get_model("accounts", "User")
    Customer = apps.get_model("customers", "Customer")

    fake_customer = Customer.objects.create(name="Fake organization ************")

    User.objects.filter(organization__isnull=True).update(organization=fake_customer)


class Migration(migrations.Migration):

    dependencies = [
        ("customers", "0049_auto_20170307_1040"),
        ("accounts", "0020_user_organization"),
    ]

    operations = [
        migrations.RunPython(move_contact_pos_to_user),
        migrations.RunPython(add_fake_organization_to_user),
    ]
