# Generated by Django 2.1.7 on 2019-02-15 14:26

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0368_auto_20190206_1524")]

    operations = [
        migrations.AlterField(
            model_name="additionalforestryinfo",
            name="commercial_census",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="afi_2",
                to="assessments.ForestryInventory",
            ),
        ),
        migrations.AlterField(
            model_name="additionalforestryinfo",
            name="forestry_inventory",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="afi_1",
                to="assessments.ForestryInventory",
            ),
        ),
        migrations.AlterField(
            model_name="agentbankaccountloanhistory",
            name="assessment",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="agent_bank_account_loan_history",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="agentincome",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="agent_income",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="assessment",
            name="additional_forestry_info",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="assessments.AdditionalForestryInfo",
            ),
        ),
        migrations.AlterField(
            model_name="assessment",
            name="forest_annual_plan",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="assessment2",
                to="assessments.ForestryPlan",
            ),
        ),
        migrations.AlterField(
            model_name="assessment",
            name="forest_management_plan",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="assessment1",
                to="assessments.ForestryPlan",
            ),
        ),
        migrations.AlterField(
            model_name="assessment",
            name="producing_organization_details",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="assessments.ProducingOrganizationDetails",
            ),
        ),
        migrations.AlterField(
            model_name="assessmentlog",
            name="actor",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="assessment_logs",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="assessmentlog",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="logs",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="assessmenttabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="assessmenttabcomments",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheettabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="balancesheettabcomments",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowtabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="cashflowtabcomments",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="documentationtabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="documentationtabcomments",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="financetabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financetabcomments",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="financialperformancetabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialperformancetabcomments",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="assessment",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financial_scores",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="asset_turnover",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialscores7",
                to="assessments.FinancialScore",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="current_ratio",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialscores13",
                to="assessments.FinancialScore",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="days_inventory_outstanding",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialscores15",
                to="assessments.FinancialScore",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="days_sales_outstanding",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialscores16",
                to="assessments.FinancialScore",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="debt_coverage_ratio",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialscores11",
                to="assessments.FinancialScore",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="debt_servicing",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialscores12",
                to="assessments.FinancialScore",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="debt_to_assets_ratio",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialscores9",
                to="assessments.FinancialScore",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="debt_to_equity_ratio",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialscores10",
                to="assessments.FinancialScore",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="gross_margin",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialscores2",
                to="assessments.FinancialScore",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="net_profit_growth",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialscores1",
                to="assessments.FinancialScore",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="net_profit_margin",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialscores4",
                to="assessments.FinancialScore",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="operating_profit_margin",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialscores3",
                to="assessments.FinancialScore",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="quick_ratio",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialscores14",
                to="assessments.FinancialScore",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="return_on_assets",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialscores6",
                to="assessments.FinancialScore",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="return_on_capital",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialscores17",
                to="assessments.FinancialScore",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="return_on_equity",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialscores5",
                to="assessments.FinancialScore",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="revenue_growth",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialscores0",
                to="assessments.FinancialScore",
            ),
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="working_capital_turnover",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financialscores8",
                to="assessments.FinancialScore",
            ),
        ),
        migrations.AlterField(
            model_name="financialstrategy",
            name="assessment",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="financial_strategy",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="accepted_format",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks5",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="assessment",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="general_checks",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="asset_register",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks9",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="audited",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks6",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="balance_sheet",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks1",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="bank_statements",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks7",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="cash_flow",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks3",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="cash_flow_projections",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks4",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="collateral",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks8",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="depreciation_policy",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks10",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="external_valuation",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks11",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="financial_statements",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks0",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="grant_history",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks17",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="grants_recorded_in_financial_statement",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks18",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="loan_default_known",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks16",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="loan_history",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks14",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="loan_recorded_in_financial_statement",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks15",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="profit_and_loss",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks2",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="record_keeping",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks12",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="software_system",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="generalchecks13",
                to="assessments.GeneralCheck",
            ),
        ),
        migrations.AlterField(
            model_name="governancetabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="governancetabcomments",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="monthlyproductiontabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="monthlyproductiontabcomments",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="netmonthlyincomeprojection",
            name="assessment",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.PROTECT, to="assessments.Assessment"
            ),
        ),
        migrations.AlterField(
            model_name="observationstabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="observationstabcomments",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="organisationaltabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="organisationaltabcomments",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="land_used_by_members_outgrowers",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="producingorganizationdetails2",
                to="assessments.TotalLandUse",
            ),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="land_used_by_producing_organization",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="producingorganizationdetails1",
                to="assessments.TotalLandUse",
            ),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="land_used_for_forestry",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="producingorganizationdetails",
                to="assessments.TotalLandUseForestry",
            ),
        ),
        migrations.AlterField(
            model_name="productiontabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="productiontabcomments",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="profitlosstabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="profitlosstabcomments",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="assessment",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="ratio_scores",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="sectionresponse",
            name="_assessment",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="all_section_responses",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="termsandconditionstabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="termsandconditionstabcomments",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="totallanduse",
            name="leased_rented",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="totallanduse2",
                to="assessments.LandUse",
            ),
        ),
        migrations.AlterField(
            model_name="totallanduse",
            name="owned",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="totallanduse1",
                to="assessments.LandUse",
            ),
        ),
        migrations.AlterField(
            model_name="totallanduse",
            name="used_otherwise",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="totallanduse3",
                to="assessments.LandUse",
            ),
        ),
        migrations.AlterField(
            model_name="totallanduseforestry",
            name="agriculture",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="totallanduse2",
                to="assessments.LandUseForestry",
            ),
        ),
        migrations.AlterField(
            model_name="totallanduseforestry",
            name="cultural",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="totallanduse4",
                to="assessments.LandUseForestry",
            ),
        ),
        migrations.AlterField(
            model_name="totallanduseforestry",
            name="forestry",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="totallanduse1",
                to="assessments.LandUseForestry",
            ),
        ),
        migrations.AlterField(
            model_name="totallanduseforestry",
            name="infrastructure",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="totallanduse3",
                to="assessments.LandUseForestry",
            ),
        ),
        migrations.AlterField(
            model_name="totallanduseforestry",
            name="not_used",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="totallanduse7",
                to="assessments.LandUseForestry",
            ),
        ),
        migrations.AlterField(
            model_name="totallanduseforestry",
            name="other",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="totallanduse6",
                to="assessments.LandUseForestry",
            ),
        ),
        migrations.AlterField(
            model_name="totallanduseforestry",
            name="water",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="totallanduse5",
                to="assessments.LandUseForestry",
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyexpensesprojection",
            name="_net_income",
            field=models.OneToOneField(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="total_expenses",
                to="assessments.NetMonthlyIncomeProjection",
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyexpensesprojection",
            name="assessment",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.PROTECT, to="assessments.Assessment"
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyincomeprojection",
            name="_net_income",
            field=models.OneToOneField(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="total_income",
                to="assessments.NetMonthlyIncomeProjection",
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyincomeprojection",
            name="assessment",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.PROTECT, to="assessments.Assessment"
            ),
        ),
        migrations.AlterField(
            model_name="valuechaintabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="valuechaintabcomments",
                to="assessments.Assessment",
            ),
        ),
    ]
