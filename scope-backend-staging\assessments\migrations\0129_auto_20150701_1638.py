from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0128_auto_20150626_0719")]

    operations = [
        migrations.AlterField(
            model_name="monthlyexpensesprojection",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="monthlyexpensesprojection_set",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="monthlyincomeprojection",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="monthlyincomeprojection_set",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="monthlyproduction",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="monthlyproduction_set",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
    ]
