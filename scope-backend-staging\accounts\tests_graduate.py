import datetime
import json
from unittest.mock import patch

import httpretty
from denorm import flush
from denorm.models import DirtyInstance
from django.test import TestCase
from requests.models import Response
from rest_framework import status
from rest_framework.reverse import reverse

from accounts.factories import UserFactory
from accounts.serializers import SendToTrainingUserSerializer
from hrm.factories import (
    AssessorFactory,
    AssessorTrainingFactory,
    EmployeeFactory,
    FinancialSpecialistTrainingFactory,
)
from hrm.models import Assessor
from libs.test_helpers import (
    AssessorJWTTestCase,
    ContactJWTTestCase,
    CustomerAdminJWTTestCase,
    CustomerUserJWTTestCase,
    DenormMixin,
    EmployeeJWTTestCase,
    FinancialSpecialistJWTTestCase,
    QualityReviewerJWTTestCase,
)


class SetupMixin(object):
    USER_PASSWORD = "blub"

    @classmethod
    def setUpTestData(cls):
        cls.target_user = UserFactory.create(
            is_active=False, can_access_trainee=True, password=cls.USER_PASSWORD
        )
        cls.target_assessor = AssessorFactory.create(
            user=cls.target_user, is_assessor=True
        )
        AssessorTrainingFactory.create(assessor=cls.target_assessor)
        FinancialSpecialistTrainingFactory.create(assessor=cls.target_assessor)
        DirtyInstance.objects.create(content_object=cls.target_user)
        flush()
        cls.url = reverse("accounts:user-graduate", [cls.target_user.pk])
        super().setUpTestData()


class GraduateTestCase(SetupMixin, DenormMixin, TestCase):
    fake_api_url = "https://faketraineeapi.scopeinsight.com/users/receive/"

    @classmethod
    def setUpTestData(cls):
        httpretty.register_uri(
            httpretty.POST, cls.fake_api_url, json.dumps({"id": 255})
        )
        httpretty.enable(allow_net_connect=False)
        super().setUpTestData()

    @classmethod
    def tearDownClass(cls):
        super().tearDownClass()
        httpretty.disable()
        httpretty.reset()

    def setUp(self):
        super().setUp()
        self.target_assessor.user.training_id = self.target_assessor.user.id
        self.target_assessor.user.save()
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            self.target_assessor.graduate()
        self.target_assessor.refresh_from_db()
        self.target_user.refresh_from_db()

    def tearDown(self):
        httpretty.httpretty.latest_requests = []

    def test_url(self):
        self.assertEqual(f"/accounts/users/{self.target_user.pk}/graduate/", self.url)

    def test_local_expiration_date_assessor(self):
        new_expiration = datetime.date.today() + datetime.timedelta(days=365)
        self.assertEqual(new_expiration, self.target_assessor.expiration_date_assessor)

    def test_local_expiration_date_financial_specialist(self):
        new_expiration = datetime.date.today() + datetime.timedelta(days=365)
        self.assertEqual(
            new_expiration, self.target_assessor.expiration_date_financial_specialist
        )

    def test_local_user_is_active(self):
        self.assertTrue(self.target_user.is_active)

    def test_local_user_access_to_trainee(self):
        self.assertFalse(self.target_user.can_access_trainee)

    def test_correct_url_used(self):
        self.assertEqual(1, len(httpretty.httpretty.latest_requests))
        request = httpretty.httpretty.latest_requests[0]
        self.assertEqual(
            self.fake_api_url, f"https://{request.headers['Host']}{request.path}"
        )
        self.assertEqual("POST", request.method)

    def test_correct_data_assessor(self):
        request = httpretty.httpretty.latest_requests[0]
        self.assertIn("assessor", request.parsed_body)
        expected_data = {
            "expiration_date_assessor": datetime.date.today().isoformat(),
            "expiration_date_financial_specialist": datetime.date.today().isoformat(),
        }
        self.assertDictContainsSubset(expected_data, request.parsed_body["assessor"])

    def test_no_login_after_graduate(self):
        request = httpretty.httpretty.latest_requests[0]
        serializer = SendToTrainingUserSerializer(data=request.parsed_body)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        flush()
        response = self.client.post(
            "/api-token-auth/",
            {"email": self.target_user.email, "password": self.USER_PASSWORD},
            HTTP_ORIGIN="http://app.testserver",
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        self.assertDictEqual(
            {
                "non_field_errors": [
                    "Oops, it looks like your Certification/License has expired! To renew your Certificate / License, please get in touch with your account manager or reach out to <NAME_EMAIL>. We'll be happy to assist you with the renewal process. Thank you!"
                ]
            },
            json.loads(response.content),
        )


class NoGetMixin(object):
    def test_cannot_get(self):
        response = self.client.get(self.url, content_type="application/json")
        self.assertEqual(405, response.status_code)


class NoPostMixin(object):
    def test_cannot_post(self):
        response = self.client.post(
            self.url, json.dumps({}), content_type="application/json"
        )
        self.assertEqual(403, response.status_code)


class EmployeeTestCase(NoGetMixin, SetupMixin, DenormMixin, EmployeeJWTTestCase):
    def test_post(self):
        with patch.object(Assessor, "graduate", autospec=True) as mock_graduate:
            mock_response = Response()
            mock_response.status_code = 200
            mock_response._content = json.dumps({"a": "b"})
            mock_graduate.return_value = mock_response
            response = self.client.post(
                self.url, json.dumps({}), content_type="application/json"
            )
        self.assertEqual(200, response.status_code)
        mock_graduate.assert_called_once_with(self.target_assessor)

    def test_post_non_assessor(self):
        employee = EmployeeFactory.create()
        flush()
        url = reverse("accounts:user-graduate", [employee.user.pk])
        response = self.client.post(
            url, json.dumps({}), content_type="application/json"
        )
        self.assertEqual(400, response.status_code)
        self.assertListEqual(
            ["User is not an assessor or financial specialist"],
            json.loads(response.content),
        )


class NonEmployeeBase(NoGetMixin, NoPostMixin, SetupMixin, DenormMixin):
    pass


class ContactTestCase(NonEmployeeBase, ContactJWTTestCase):
    pass


class CustomerAdminTestCase(NonEmployeeBase, DenormMixin, CustomerAdminJWTTestCase):
    pass


class CustomerUserTestCase(NonEmployeeBase, DenormMixin, CustomerUserJWTTestCase):
    pass


class AssessorTestCase(NonEmployeeBase, DenormMixin, AssessorJWTTestCase):
    pass


class FinancialSpecialistTestCase(
    NonEmployeeBase, DenormMixin, FinancialSpecialistJWTTestCase
):
    pass


class QualityReviewerTestCase(NonEmployeeBase, DenormMixin, QualityReviewerJWTTestCase):
    pass
