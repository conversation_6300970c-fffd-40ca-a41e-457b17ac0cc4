
import os, json
from openai import AzureOpenAI
from django.core.management.base import BaseCommand
from django.conf import settings
from django.http import HttpResponse, JsonResponse

class Command(BaseCommand):
    help = 'Upload a file to Azure OpenAI'

    def add_arguments(self, parser):
        pass

    def handle(self, *args, **kwargs):
        client = AzureOpenAI(
            api_key=settings.OPENAI_TOKEN,
            api_version=settings.OPENAI_API_VERSION,
            azure_endpoint=settings.OPENAI_URL,
        )

        deployment_name = settings.OPENAI_DEPLOYMENT
        with open("Assessment_8211.json", 'r') as file:
            json_data = json.load(file)
        print(json_data)
        response = client.chat.completions.create(
            model=deployment_name,
            messages=[
                {
                    "role": "system",
                    "content": "As an expert in agricultural organization business development services (BDS) and capacity building, you will receive a JSON input containing responses to a SCOPEinsight professionalism assessment for a farmer organization. Each question has multiple answer options ; the checked answers are the answers provided by the farmer organization. Based on these responses, generate a detailed technical assistance plan that identifies areas of strengths and weaknesses and provides targeted recommendations to address them not limited but especially for internal management, financial management, operations, production base and market dimensions. The plan should be comprehensive and include actionable steps to improve professionalism, scale up the business, gain better access to markets and finance, and boost production and sales. Exclude the action plan section."
                },
                {"role": "user", "content": json.dumps(json_data)}
            ]
        )
        # Parse the JSON part
        with open("Assessment_8211_guide.txt", 'w') as file:
            file.write(response.choices[0].message.content)


#         client = AzureOpenAI(
#             api_key=settings.OPENAI_TOKEN,
#             api_version=settings.OPENAI_API_VERSION,
#             azure_endpoint=settings.OPENAI_URL,
#         )
#         file_path = kwargs['file_path']
#         purpose = 'assistants'
#         message_file = client.files.create(
#             file=open(file_path, "rb"), purpose="assistants"
#         )
        
#         thread = client.beta.threads.create(
#             messages=[
#                 {
#                     "role": "user",
#                     "content": "How many company shares were outstanding last quarter?",
#                     # Attach the new file to the message.
#                     "attachments": [
#                         { "file_id": message_file.id, "tools": [{"type": "file_search"}] }
#                     ],
#                 }
#             ]
#         ) # You can print the status and the file counts of the batch to see the result of this operation.
#         print(thread.tool_resources.file_search)

#         return
#         assistant = client.beta.assistants.create(
#             name="Technical Assistant",
#             instructions="""As an expert in agricultural organization business development services (BDS) and capacity building, you will
#             receive a JSON input containing responses to a SCOPEinsight professionalism assessment for a farmer organization.
#             Each question has multiple answer options ; the checked answers are the answers provided by the farmer organization.
#             Based on these responses, generate a detailed technical assistance plan that identifies areas of strengths and weaknesses and
#             provides targeted recommendations to address them not limited but especially for internal management, financial management,
#             operations, production base and market dimensions. The plan should be comprehensive and include actionable steps to improve
#             professionalism, scale up the business, gain better access to markets and finance, and boost production and sales.
#             Exclude the action plan section.""",
#             tools=[{"type": "file_search"}],
#             model=settings.OPENAI_DEPLOYMENT
#         )
#         try:
#             with open(file_path, "rb") as file:
#                 response = client.files.create(
#                     file=file,
#                     purpose=purpose
#                 )
#             self.stdout.write(self.style.SUCCESS(f'Successfully uploaded file: {response}'))
#         except Exception as e:
#             self.stdout.write(self.style.ERROR(f'Error uploading file: {e}'))