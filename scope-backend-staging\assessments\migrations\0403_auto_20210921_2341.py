# Generated by Django 2.2.20 on 2021-09-21 21:41

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0402_auto_20210831_2021"),
    ]

    operations = [
        migrations.AddField(
            model_name="enablingplayer",
            name="other",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="inputpurchase",
            name="other",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="product",
            name="land_size_under_production",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=5,
                null=True,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(100000),
                ],
            ),
        ),
        migrations.AddField(
            model_name="product",
            name="priority",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="supplier",
            name="other",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="valuechainplayer",
            name="other",
            field=models.TextField(blank=True),
        ),
    ]
