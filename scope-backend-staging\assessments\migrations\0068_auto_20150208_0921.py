from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0067_auto_20150208_0825")]

    operations = [
        migrations.AddField(
            model_name="assessmentcomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="balancesheetcomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="bankaccountcomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="basicfinancialinfocomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="cashflowstatementcomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="costofsale",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="costofsalecomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="documentavailabilityresponsecomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="enablingplayercomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="expense",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="expensecomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="financialratiocomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="financialscorescomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="generalcheckscomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="granthistorycomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="inputpurchasecomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="loanhistorycomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="producingorganizationdetailscomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="productcomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="productionfigure",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="productionfigurecomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="productionpurchase",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="productionpurchasecomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="productionsale",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="productionsalecomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="profitlossstatementcomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscorescomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="responsecomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="unscoredsectionresponsecomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="unscoredtoolresponsecomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="valuechainplayercomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
    ]
