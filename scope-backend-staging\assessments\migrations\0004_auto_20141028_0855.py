from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0003_auto_20141028_0837")]

    operations = [
        migrations.RenameField(
            model_name="unscoredresponsedocument",
            old_name="response",
            new_name="unscoredresponse",
        ),
        migrations.AlterField(
            model_name="assessmentdocument",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="assessmentdocument_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="responsedocument",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="responsedocument_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="unscoredresponsedocument",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="unscoredresponsedocument_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
        ),
    ]
