import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0172_auto_20160526_2006")]

    operations = [
        migrations.AlterField(
            model_name="additionalforestryinfo",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="assessment",
            name="producing_organization",
            field=models.ForeignKey(
                related_name="assessments",
                on_delete=django.db.models.deletion.PROTECT,
                to="customers.ProducingOrganization",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="assessment",
            name="project",
            field=models.ForeignKey(
                related_name="assessments",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="projects.Project",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="assessment",
            name="tool",
            field=models.ForeignKey(
                related_name="assessments",
                on_delete=django.db.models.deletion.PROTECT,
                to="products.Tool",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="assessmentassignment",
            name="invitation",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="assessments.AssessmentInvitation",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="assessmentcomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="assessmentdocument",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="assessmentdocument",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="assessmentdocument_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="assessmentdocumentcomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="assessmentevaluation",
            name="evaluator",
            field=models.ForeignKey(
                related_name="assessment_evaluations",
                on_delete=django.db.models.deletion.PROTECT,
                to="hrm.Employee",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="assessor",
            field=models.ForeignKey(
                related_name="balancesheets",
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="hrm.Assessor",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="balancesheets_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheetcomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheetdocument",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheetdocument",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="balancesheetdocument_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheetdocumentcomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="bankaccount",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="bankaccounts_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="bankaccountcomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="basicfinancialinfo",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="basicfinancialinfos_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="basicfinancialinfocomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="assessor",
            field=models.ForeignKey(
                related_name="cashflowstatements",
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="hrm.Assessor",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="cashflowstatements_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="cashflowstatementcomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="certification",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="certifications_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="collateralasset",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="collateralassets_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="collateralassetcomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="costofsalecomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="costofsaledocument",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="costofsaledocument",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="costofsaledocument_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="costofsaledocumentcomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="documentavailabilityresponse",
            name="assessor",
            field=models.ForeignKey(
                related_name="document_availability_responses",
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="hrm.Assessor",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="documentavailabilityresponse",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="document_availability_responses_quality_controlled",
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="documentavailabilityresponse",
            name="question",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.PROTECT,
                to="products.DocumentAvailabilityQuestion",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="documentavailabilityresponsecomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="documentavailabilityresponsedocument",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="documentavailabilityresponsedocument",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="documentavailabilityresponsedocument_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="draftassessment",
            name="accountant",
            field=models.ForeignKey(
                related_name="draft_assessments_as_accountant",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Assessor",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="draftassessment",
            name="assessor",
            field=models.ForeignKey(
                related_name="draft_assessments_as_assessor",
                on_delete=django.db.models.deletion.PROTECT,
                to="hrm.Assessor",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="draftassessment",
            name="producing_organization",
            field=models.ForeignKey(
                related_name="draft_assessments",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="customers.ProducingOrganization",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="draftassessment",
            name="project",
            field=models.ForeignKey(
                related_name="draft_assessments",
                on_delete=django.db.models.deletion.PROTECT,
                to="projects.Project",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="draftassessment",
            name="tool",
            field=models.ForeignKey(
                related_name="draft_assessments",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="products.Tool",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="draftassessment",
            name="tool_type",
            field=models.ForeignKey(
                to="products.ToolType", on_delete=django.db.models.deletion.PROTECT
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="enablingplayer",
            name="contact",
            field=models.ForeignKey(
                related_name="enablingplayers",
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="customers.Contact",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="enablingplayer",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="enablingplayers_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="enablingplayercomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="executive",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="executives_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="expensecomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="expensedocument",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="expensedocument",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="expensedocument_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="expensedocumentcomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="assessor",
            field=models.ForeignKey(
                related_name="financial_ratios",
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="hrm.Assessor",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="financialratios_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratiocomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="financialscoress_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialscorescomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="forestryequipment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="forestryinventory",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="forestryplan",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="generalcheckss_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalcheckscomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="governance",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="governances_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="granthistory",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="granthistorys_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="granthistorycomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="inputpurchase",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="inputpurchases_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="inputpurchasecomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="insurance",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="insurances_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="insurancecomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="landuse",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="landuseforestry",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="loanapplication",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="loanapplications_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="loanapplicationcomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="loanhistory",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="loanhistorys_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="loanhistorycomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="loanrequirement",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="loanrequirements_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="loanrequirementcomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="loanrequirementpurpose",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="loanrequirementpurposes_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="monthlyexpensesprojection",
            name="_total",
            field=models.ForeignKey(
                related_name="projections",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.TotalMonthlyExpensesProjection",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="monthlyexpensesprojection",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="monthlyexpensesprojections_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="monthlyincomeprojection",
            name="_total",
            field=models.ForeignKey(
                related_name="projections",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.TotalMonthlyIncomeProjection",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="monthlyincomeprojection",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="monthlyincomeprojections_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="monthlyproduction",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="monthlyproductions_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="netmonthlyincomeprojection",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="netmonthlyincomeprojections_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="nonexecutive",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="nonexecutives_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="producingorganizationdetailss_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="producingorganizationdetailscomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="product",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="products_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="productcomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="productionfigure",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="productionfigures_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="productionfigurecomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="productionpurchase",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="productionpurchases_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="productionpurchasecomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="productionsale",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="productionsales_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="productionsalecomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="assessor",
            field=models.ForeignKey(
                related_name="profitlossstatements",
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="hrm.Assessor",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="profitlossstatements_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="profitlossstatementcomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="profitlossstatementdocument",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="profitlossstatementdocument",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="profitlossstatementdocument_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="profitlossstatementdocumentcomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="asset_turnover",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="current_ratio",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="days_inventory_outstanding",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="days_sales_outstanding",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="debt_coverage_ratio",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="debt_servicing",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="debt_to_assets_ratio",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="debt_to_equity_ratio",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="financial_scores",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.FinancialScores",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="gross_margin",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="net_profit_growth",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="net_profit_margin",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="operating_profit_margin",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="ratioscoress_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="quick_ratio",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="return_on_assets",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="return_on_equity",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="revenue_growth",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscores",
            name="working_capital_turnover",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="ratioscorescomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="response",
            name="assessor",
            field=models.ForeignKey(
                related_name="responses",
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="hrm.Assessor",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="response",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="responses_quality_controlled",
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="response",
            name="question",
            field=models.ForeignKey(
                related_name="responses",
                on_delete=django.db.models.deletion.PROTECT,
                to="products.Question",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="response",
            name="question_scale_type",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="products.ScaleType",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="responsecomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="responsedocument",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="responsedocument",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="responsedocument_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="responsedocumentcomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="sectionresponse",
            name="section",
            field=models.ForeignKey(
                related_name="section_responses",
                on_delete=django.db.models.deletion.PROTECT,
                to="products.Section",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="shareholder",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="shareholders_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="shareholdercomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="subresponse",
            name="assessor",
            field=models.ForeignKey(
                related_name="subresponses",
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="hrm.Assessor",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="subresponse",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="subresponses_quality_controlled",
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="subresponse",
            name="selected_option",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="products.SubQuestionOption",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="subresponsecomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="subresponsedocument",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="subresponsedocument",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="subresponsedocument_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="subresponsedocumentcomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="timesheetentry",
            name="assessment",
            field=models.ForeignKey(
                related_name="timesheet_entries",
                on_delete=django.db.models.deletion.PROTECT,
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="timesheetentry",
            name="assessor",
            field=models.ForeignKey(
                related_name="timesheet_entries",
                on_delete=django.db.models.deletion.PROTECT,
                to="hrm.Assessor",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="totallanduse",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="totallanduseforestry",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="totalmonthlyexpensesprojection",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="totalmonthlyexpensesprojections_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="totalmonthlyincomeprojection",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="totalmonthlyincomeprojections_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="unscoredsectionresponse",
            name="assessor",
            field=models.ForeignKey(
                related_name="unscored_section_responses",
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="hrm.Assessor",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="unscoredsectionresponse",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="unscored_section_responses_quality_controlled",
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="unscoredsectionresponse",
            name="question",
            field=models.ForeignKey(
                related_name="responses",
                on_delete=django.db.models.deletion.PROTECT,
                to="products.UnscoredSectionQuestion",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="unscoredsectionresponsecomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="unscoredtoolresponse",
            name="assessor",
            field=models.ForeignKey(
                related_name="unscored_tool_responses",
                on_delete=django.db.models.deletion.PROTECT,
                to="hrm.Assessor",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="unscoredtoolresponse",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="unscored_tool_responses_quality_controlled",
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="unscoredtoolresponse",
            name="question",
            field=models.ForeignKey(
                related_name="responses",
                on_delete=django.db.models.deletion.PROTECT,
                to="products.UnscoredToolQuestion",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="unscoredtoolresponsecomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="valuechainplayer",
            name="contact",
            field=models.ForeignKey(
                related_name="valuechainplayers",
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="customers.Contact",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="valuechainplayer",
            name="quality_controller",
            field=models.ForeignKey(
                related_name="valuechainplayers_quality_controlled",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="valuechainplayercomment",
            name="_assessment",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
    ]
