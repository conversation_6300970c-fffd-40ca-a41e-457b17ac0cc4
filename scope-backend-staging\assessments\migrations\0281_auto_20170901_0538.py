# Generated by Django 1.10.5 on 2017-09-01 05:38


from django.db import migrations


def move_qr_from_old_to_new(apps, schema_editor):
    Assessment = apps.get_model("assessments", "Assessment")
    for a in Assessment.objects.all():
        if a.quality_reviewer:
            a.new_quality_reviewer = a.quality_reviewer.user
            a.save()


class Migration(migrations.Migration):

    dependencies = [("assessments", "0280_assessment_new_quality_reviewer")]

    operations = [migrations.RunPython(move_qr_from_old_to_new)]
