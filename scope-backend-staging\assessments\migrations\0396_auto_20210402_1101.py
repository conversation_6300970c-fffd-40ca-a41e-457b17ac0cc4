# Generated by Django 2.2.19 on 2021-04-02 11:01

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0395_merge_20210211_2040"),
    ]

    operations = [
        migrations.AddField(
            model_name="assessment",
            name="primary_commodity",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="+",
                to="assessments.Product",
            ),
        ),
        migrations.AlterField(
            model_name="otherproductionpurchase",
            name="_assessment",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="otherproductionpurchases",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="productionfigure",
            name="_assessment",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="productionfigures",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="productionmargin",
            name="_assessment",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="productionmargins",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="productionpurchase",
            name="_assessment",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="productionpurchases",
                to="assessments.Assessment",
            ),
        ),
        migrations.AlterField(
            model_name="productionsale",
            name="_assessment",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="productionsales",
                to="assessments.Assessment",
            ),
        ),
    ]
