# Generated by Django 3.1.14 on 2024-02-13 17:32

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0043_auto_20231219_1502'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='is_self_registered',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='user',
            name='language',
            field=models.CharField(choices=[('en', 'English'), ('am', 'Amharic'), ('fr', 'French'), ('es', 'Spanish')], default='en', max_length=2),
        ),
        migrations.AlterField(
            model_name='user',
            name='languages_spoken',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(choices=[('en', 'English'), ('am', 'Amharic'), ('fr', 'French'), ('es', 'Spanish')], default='en', max_length=2), blank=True, null=True, size=None),
        ),
    ]
