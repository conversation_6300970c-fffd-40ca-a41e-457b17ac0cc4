# Generated by Django 2.1.8 on 2019-06-17 15:18

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("accounts", "0034_user_can_access_trainee")]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="user",
            name="language",
            field=models.CharField(
                choices=[("en", "English"), ("fr", "French"), ("es", "Spanish")],
                default="en",
                max_length=2,
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="languages_spoken",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    choices=[("en", "English"), ("fr", "French"), ("es", "Spanish")],
                    default="en",
                    max_length=2,
                ),
                blank=True,
                null=True,
                size=None,
            ),
        ),
    ]
