import json

from rest_framework import status

from accounts.factories import UserFactory
from customers.factories import ContactFactory
from libs.test_helpers import CustomerAdminJWTTestCase, DenormMixin


class ImpersonationAdminTestCase(DenormMixin, CustomerAdminJWTTestCase):
    def test_only_employee_can_impersonate(self):
        """
        Dashboard Admin cannot impersonate, because has no permission to do so
        """
        user = UserFactory.create(email="<EMAIL>")
        ContactFactory.create(user=user, access_to_dashboard="customer_admin")
        response = self.client.post(
            "/accounts/users/impersonate/",
            {"email": "<EMAIL>"},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual("Not allowed to impersonate.", response_dict["detail"])
