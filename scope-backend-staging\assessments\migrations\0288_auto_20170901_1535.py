# Generated by Django 1.10.5 on 2017-09-01 15:35


from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [("assessments", "0287_auto_20170901_1430")]

    operations = [
        migrations.RemoveField(model_name="assessmentdocument", name="accepted"),
        migrations.RemoveField(
            model_name="assessmentdocument", name="quality_controller"
        ),
        migrations.RemoveField(model_name="balancesheetdocument", name="accepted"),
        migrations.RemoveField(
            model_name="balancesheetdocument", name="quality_controller"
        ),
        migrations.RemoveField(model_name="costofsaledocument", name="accepted"),
        migrations.RemoveField(
            model_name="costofsaledocument", name="quality_controller"
        ),
        migrations.RemoveField(
            model_name="documentavailabilityresponsedocument", name="accepted"
        ),
        migrations.RemoveField(
            model_name="documentavailabilityresponsedocument", name="quality_controller"
        ),
        migrations.RemoveField(model_name="expensedocument", name="accepted"),
        migrations.RemoveField(model_name="expensedocument", name="quality_controller"),
        migrations.RemoveField(
            model_name="profitlossstatementdocument", name="accepted"
        ),
        migrations.RemoveField(
            model_name="profitlossstatementdocument", name="quality_controller"
        ),
        migrations.RemoveField(model_name="responsedocument", name="accepted"),
        migrations.RemoveField(
            model_name="responsedocument", name="quality_controller"
        ),
        migrations.RemoveField(model_name="subresponsedocument", name="accepted"),
        migrations.RemoveField(
            model_name="subresponsedocument", name="quality_controller"
        ),
    ]
