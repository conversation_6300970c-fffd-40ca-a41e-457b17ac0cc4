# Generated by Django 3.1.14 on 2021-12-29 11:17

import djmoney.models.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0409_auto_20211229_1024"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="agentbankaccountloanhistory",
            name="has_bank_account",
            field=models.BooleanField(null=True),
        ),
        migrations.AlterField(
            model_name="agentbankaccountloanhistory",
            name="has_loan_history",
            field=models.BooleanField(null=True),
        ),
        migrations.AlterField(
            model_name="agentbankaccountloanhistory",
            name="has_mobile_account",
            field=models.BooleanField(null=True),
        ),
        migrations.AlterField(
            model_name="agentbankaccountloanhistory",
            name="has_paid_back_loans",
            field=models.BooleanField(null=True),
        ),
        migrations.AlterField(
            model_name="agentincome",
            name="income",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="balancesheet",
            name="account_receivables",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Account receivables",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="accounts_payable",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Accounts payable",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="average_inventory",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="average_receivables",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="cash",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Cash and cash equivalents",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="deferred_tax",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Deferred tax",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="fixed_assets",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Fixed assets",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="goodwill",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Goodwill",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="grants",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Grants (seed capital)",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="income_tax_payable",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Income tax payable",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="intangible_assets",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Intangible assets",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="inventories",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Inventories",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="long_term_loans",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Long term loans",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="manual_total_assets",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Manually computed total assets",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="net_assets",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="net_working_capital",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Other",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other_current_assets",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Other current assets",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other_current_liabilities",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Other current liabilities",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other_non_current_assets",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Other non-current assets",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other_non_current_liabilities",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Other non current liabilities",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other_receivables",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Other receivables",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other_reserves",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Other reserves",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="overdrafts",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Overdrafts",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="provisions",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Provisions",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="retained_earnings",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Retained earnings",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="share_capital",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Share capital",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="share_premium",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Share premium",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="short_term_loans",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Short term loans",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="short_term_provisions",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Short term provisions",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="statutory_legal_reserves",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Statutory/legal reserves",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="total_assets",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="total_current_assets",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="total_current_liabilities",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="total_equity",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="total_liabilities",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="total_non_current_assets",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="total_non_current_liabilities",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="bankaccount",
            name="current_balance",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="basicfinancialinfo",
            name="cost_of_sales",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="basicfinancialinfo",
            name="net_profit",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="basicfinancialinfo",
            name="shareholders_equity",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="basicfinancialinfo",
            name="turnover",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="basicprofitlossstatement",
            name="cost_of_sales",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Cost of sales",
            ),
        ),
        migrations.AlterField(
            model_name="basicprofitlossstatement",
            name="gross_profit",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="basicprofitlossstatement",
            name="net_profit",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Net profit",
            ),
        ),
        migrations.AlterField(
            model_name="basicprofitlossstatement",
            name="operational_costs",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Operational costs",
            ),
        ),
        migrations.AlterField(
            model_name="basicprofitlossstatement",
            name="turnover",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Turnover",
            ),
        ),
        migrations.AlterField(
            model_name="capitalrequirement",
            name="amount",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="adjustment_for_tax",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Adjustment for tax",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="cash_at_beginning_of_period",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Cash at beginning of period",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="cash_at_end_of_period",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="cash_generated_from_operations",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="decrease_in_trade_and_other_receivables",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="(Increase) / Decrease in trade and other receivables",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="depreciation_and_amortization",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Depreciation and amortization",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="dividends_paid_to_organizations_shareholders",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Dividends paid to organization's shareholders",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="dividends_received",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Dividends received",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="financing_other",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Financing other",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="income_taxes_paid",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Income taxes paid",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="increase_in_inventories",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Increase / (decrease) in inventories",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="increase_in_trade_and_other_payables",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Increase / (decrease) in trade payables and other payables",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="interest_expense",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Interest expense",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="interest_paid",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Interest paid",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="interest_received",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Interest received",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="investing_other",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Investing other",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="investment_income",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Investment income",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="loans_granted_to_associates_or_subsidiaries",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Loans granted to associates or subsidiaries",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="lrrfaos",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Loan repayments received from associates or subsidiaries",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="net_cash_flow",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="net_cash_from_operating_activities",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="net_cash_used_in_financing_activities",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="net_cash_used_in_investing_activities",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="net_income",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Net income",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="proceeds_from_borrowings",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Proceeds from borrowings",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="proceeds_from_loan_from_subsidiary_undertaking",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Proceeds from loan from subsidiary undertaking",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="proceeds_from_sale_of_PPE",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Proceeds from sale of PPE",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="proceeds_from_sale_of_ordinary_shares",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Proceeds from sale of ordinary shares",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="profit_on_sale_of_ppe",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Profit / (loss) on sale of PP&E",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="purchase_of_treasury_shares",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Purchase of treasury shares",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="purchases_of_financial_assets",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Purchases of financial assets",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="purchases_of_intangible_assets",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Purchases of intangible assets",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="purchases_of_property_plant_and_equipment",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Purchases of property, plant and equipment (PPE)",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="repayments_from_borrowings",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Repayments from borrowings",
            ),
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="working_capital_changes",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Working capital changes",
            ),
        ),
        migrations.AlterField(
            model_name="collateralasset",
            name="estimated_value",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="collateralasset",
            name="valuation_proof_available",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="costofsale",
            name="value",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Value",
            ),
        ),
        migrations.AlterField(
            model_name="documentavailabilityresponse",
            name="available_with_profile",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="enablingplayer",
            name="amount_purchased",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="expense",
            name="value",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Value",
            ),
        ),
        migrations.AlterField(
            model_name="generalcheck",
            name="available",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="governance",
            name="present",
            field=models.BooleanField(null=True),
        ),
        migrations.AlterField(
            model_name="granthistory",
            name="amount",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="inputpurchase",
            name="price",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="insurance",
            name="indemnity_limit",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="loanapplication",
            name="amount",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="loanhistory",
            name="amount",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="loanrequirement",
            name="amount",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="loanrequirement",
            name="value_of_collateral_to_pledge",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyexpensesprojection",
            name="april",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyexpensesprojection",
            name="august",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyexpensesprojection",
            name="december",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyexpensesprojection",
            name="february",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyexpensesprojection",
            name="january",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyexpensesprojection",
            name="july",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyexpensesprojection",
            name="june",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyexpensesprojection",
            name="march",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyexpensesprojection",
            name="may",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyexpensesprojection",
            name="november",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyexpensesprojection",
            name="october",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyexpensesprojection",
            name="september",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyincomeprojection",
            name="april",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyincomeprojection",
            name="august",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyincomeprojection",
            name="december",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyincomeprojection",
            name="february",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyincomeprojection",
            name="january",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyincomeprojection",
            name="july",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyincomeprojection",
            name="june",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyincomeprojection",
            name="march",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyincomeprojection",
            name="may",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyincomeprojection",
            name="november",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyincomeprojection",
            name="october",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyincomeprojection",
            name="september",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="monthlyproduction",
            name="april",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="monthlyproduction",
            name="august",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="monthlyproduction",
            name="december",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="monthlyproduction",
            name="february",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="monthlyproduction",
            name="january",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="monthlyproduction",
            name="july",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="monthlyproduction",
            name="june",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="monthlyproduction",
            name="march",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="monthlyproduction",
            name="may",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="monthlyproduction",
            name="november",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="monthlyproduction",
            name="october",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="monthlyproduction",
            name="september",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="netmonthlyincomeprojection",
            name="april",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="netmonthlyincomeprojection",
            name="august",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="netmonthlyincomeprojection",
            name="december",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="netmonthlyincomeprojection",
            name="february",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="netmonthlyincomeprojection",
            name="january",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="netmonthlyincomeprojection",
            name="july",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="netmonthlyincomeprojection",
            name="june",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="netmonthlyincomeprojection",
            name="march",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="netmonthlyincomeprojection",
            name="may",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="netmonthlyincomeprojection",
            name="november",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="netmonthlyincomeprojection",
            name="october",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="netmonthlyincomeprojection",
            name="september",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="otherproductionpurchase",
            name="price",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="prefinancehistory",
            name="amount",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="product",
            name="operational_month_april",
            field=models.BooleanField(null=True),
        ),
        migrations.AlterField(
            model_name="product",
            name="operational_month_august",
            field=models.BooleanField(null=True),
        ),
        migrations.AlterField(
            model_name="product",
            name="operational_month_december",
            field=models.BooleanField(null=True),
        ),
        migrations.AlterField(
            model_name="product",
            name="operational_month_february",
            field=models.BooleanField(null=True),
        ),
        migrations.AlterField(
            model_name="product",
            name="operational_month_january",
            field=models.BooleanField(null=True),
        ),
        migrations.AlterField(
            model_name="product",
            name="operational_month_july",
            field=models.BooleanField(null=True),
        ),
        migrations.AlterField(
            model_name="product",
            name="operational_month_june",
            field=models.BooleanField(null=True),
        ),
        migrations.AlterField(
            model_name="product",
            name="operational_month_march",
            field=models.BooleanField(null=True),
        ),
        migrations.AlterField(
            model_name="product",
            name="operational_month_may",
            field=models.BooleanField(null=True),
        ),
        migrations.AlterField(
            model_name="product",
            name="operational_month_november",
            field=models.BooleanField(null=True),
        ),
        migrations.AlterField(
            model_name="product",
            name="operational_month_october",
            field=models.BooleanField(null=True),
        ),
        migrations.AlterField(
            model_name="product",
            name="operational_month_september",
            field=models.BooleanField(null=True),
        ),
        migrations.AlterField(
            model_name="product",
            name="primary_production",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="product",
            name="sustainable_production",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="productionmargin",
            name="margin",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="productionpurchase",
            name="price",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="productionsale",
            name="price",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="audited",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="depreciation_and_amortization",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Depreciation and amortization",
            ),
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="direct_sales",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Direct sales",
            ),
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="earnings_from_discontinued_operations",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Earnings (losses) from discontinued operations (net of tax)",
            ),
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="ebit",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="ebitda",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="gross_profit",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="income_from_continuing_operations",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="income_tax_expense",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Income tax expense",
            ),
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="indirect_sales",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Indirect sales",
            ),
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="interest_expense",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Interest expense",
            ),
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="manual_net_profit",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Manually computed net profit",
            ),
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="net_profit",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="other_income",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="Other income",
            ),
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="total_cost_of_sales",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="total_expenses",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="total_sales",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="soldtofarmers",
            name="sales_amount",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="supplier",
            name="amount_purchased",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyexpensesprojection",
            name="april",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyexpensesprojection",
            name="august",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyexpensesprojection",
            name="december",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyexpensesprojection",
            name="february",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyexpensesprojection",
            name="january",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyexpensesprojection",
            name="july",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyexpensesprojection",
            name="june",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyexpensesprojection",
            name="march",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyexpensesprojection",
            name="may",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyexpensesprojection",
            name="november",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyexpensesprojection",
            name="october",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyexpensesprojection",
            name="september",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyincomeprojection",
            name="april",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyincomeprojection",
            name="august",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyincomeprojection",
            name="december",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyincomeprojection",
            name="february",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyincomeprojection",
            name="january",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyincomeprojection",
            name="july",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyincomeprojection",
            name="june",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyincomeprojection",
            name="march",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyincomeprojection",
            name="may",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyincomeprojection",
            name="november",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyincomeprojection",
            name="october",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="totalmonthlyincomeprojection",
            name="september",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="valuechainplayer",
            name="amount_purchased",
            field=djmoney.models.fields.MoneyField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
    ]
