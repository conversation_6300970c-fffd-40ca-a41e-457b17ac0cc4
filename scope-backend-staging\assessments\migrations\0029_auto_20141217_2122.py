from django.db import migrations, models

import libs.field_helpers


class Migration(migrations.Migration):

    dependencies = [("assessments", "0028_assessmentassignment_invitation")]

    operations = [
        migrations.CreateModel(
            name="ProducingOrganizationDetails",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("number_of_female_executives", models.PositiveIntegerField(default=0)),
                ("number_of_male_executives", models.PositiveIntegerField(default=0)),
                (
                    "number_of_female_non_executives",
                    models.PositiveIntegerField(default=0),
                ),
                (
                    "number_of_male_non_executives",
                    models.PositiveIntegerField(default=0),
                ),
                ("number_of_female_members", models.PositiveIntegerField(default=0)),
                ("number_of_male_members", models.PositiveIntegerField(default=0)),
                (
                    "number_of_female_active_members",
                    models.PositiveIntegerField(default=0),
                ),
                (
                    "number_of_male_active_members",
                    models.PositiveIntegerField(default=0),
                ),
                (
                    "number_of_female_full_time_employees",
                    models.PositiveIntegerField(default=0),
                ),
                (
                    "number_of_male_full_time_employees",
                    models.PositiveIntegerField(default=0),
                ),
                (
                    "number_of_female_part_time_employees",
                    models.PositiveIntegerField(default=0),
                ),
                (
                    "number_of_male_part_time_employees",
                    models.PositiveIntegerField(default=0),
                ),
                (
                    "number_of_female_seasonal_employees",
                    models.PositiveIntegerField(default=0),
                ),
                (
                    "number_of_male_seasonal_employees",
                    models.PositiveIntegerField(default=0),
                ),
                (
                    "number_of_female_sharecroppers",
                    models.PositiveIntegerField(default=0),
                ),
                (
                    "number_of_male_sharecroppers",
                    models.PositiveIntegerField(default=0),
                ),
                (
                    "number_of_female_active_sharecroppers",
                    models.PositiveIntegerField(default=0),
                ),
                (
                    "number_of_male_active_sharecroppers",
                    models.PositiveIntegerField(default=0),
                ),
                ("number_of_female_outgrowers", models.PositiveIntegerField(default=0)),
                ("number_of_male_outgrowers", models.PositiveIntegerField(default=0)),
                (
                    "number_of_female_active_outgrowers",
                    models.PositiveIntegerField(default=0),
                ),
                (
                    "number_of_male_active_outgrowers",
                    models.PositiveIntegerField(default=0),
                ),
                ("apex_origin", models.TextField(blank=True)),
                (
                    "number_of_member_cooperatives",
                    models.PositiveIntegerField(default=0),
                ),
                ("number_of_member_unions", models.PositiveIntegerField(default=0)),
                ("access_roads", models.TextField(blank=True)),
                ("distance_to_hub", models.TextField(blank=True)),
                ("public_transportation", models.TextField(blank=True)),
                ("power_electricity", models.TextField(blank=True)),
                ("internet_access", models.TextField(blank=True)),
                ("mobile_network_coverage", models.TextField(blank=True)),
                ("running_water", models.TextField(blank=True)),
                (
                    "number_of_active_outgrowers",
                    models.PositiveIntegerField(default=0, editable=False),
                ),
                (
                    "number_of_outgrowers",
                    models.PositiveIntegerField(default=0, editable=False),
                ),
                (
                    "number_of_active_sharecroppers",
                    models.PositiveIntegerField(default=0, editable=False),
                ),
                (
                    "number_of_sharecroppers",
                    models.PositiveIntegerField(default=0, editable=False),
                ),
                (
                    "number_of_seasonal_employees",
                    models.PositiveIntegerField(default=0, editable=False),
                ),
                (
                    "number_of_part_time_employees",
                    models.PositiveIntegerField(default=0, editable=False),
                ),
                (
                    "number_of_full_time_employees",
                    models.PositiveIntegerField(default=0, editable=False),
                ),
                (
                    "number_of_active_members",
                    models.PositiveIntegerField(default=0, editable=False),
                ),
                (
                    "number_of_members",
                    models.PositiveIntegerField(default=0, editable=False),
                ),
                (
                    "number_of_non_executives",
                    models.PositiveIntegerField(default=0, editable=False),
                ),
                (
                    "number_of_executives",
                    models.PositiveIntegerField(default=0, editable=False),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Service",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("name", libs.field_helpers.LowerCaseTextField(unique=True)),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="services",
            field=models.ManyToManyField(related_name="+", to="assessments.Service"),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="assessment",
            name="producing_organization_details",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                null=True,
                blank=True,
                to="assessments.ProducingOrganizationDetails",
            ),
            preserve_default=True,
        ),
    ]
