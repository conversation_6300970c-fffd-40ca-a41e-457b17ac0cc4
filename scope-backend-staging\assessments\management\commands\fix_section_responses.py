from django.core.management.base import BaseCommand
from django.db.models import Q

from assessments.models import SectionResponse


class Command(BaseCommand):
    def handle(self, *args, **options):
        parents = SectionResponse.objects.filter(assessment_id__gte=8300)
        parents = parents.exclude(assessment_id__in=[8434, 8430, 8399])
        children = SectionResponse.objects.filter(
            parent_id__gte=310433,
        )
        for query in children:
            if query._assessment_id is None:
                try:
                    query._assessment_id = parents.get(id=query.parent_id).assessment.id
                    query.save()
                except:
                    continue
        print("GOTOVI SMO")
