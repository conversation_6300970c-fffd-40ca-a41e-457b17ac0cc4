import datetime
from celery import shared_task
from messaging.models import MessageType
from .models import User

@shared_task
def check_dashboard_admin_license_deadline():
    today = datetime.date.today()

    users = User.objects.filter(is_customer_admin = True)

    for user in users:
        if (user.organization.expiration_date_dashboard_licence
                and user.organization.expiration_date_dashboard_licence == today + datetime.timedelta(days=7)):
            MessageType.send_message(
                from_user=None,
                to_user=user,
                language=user.language,
                cc = ["<EMAIL>"],
                slug="dashboard_admin_license_expiration",
                template="accounts/dashboard_admin_license_expiration.txt",
                subject_template="accounts/dashboard_admin_license_expiration_subject.txt",
                context={
                    "to_user": user,
                },
            )
            print("sending email to " + str(user.email))