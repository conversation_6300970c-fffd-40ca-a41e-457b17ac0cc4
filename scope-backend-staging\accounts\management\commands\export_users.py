import json

import tablib
from django.core.management.base import BaseCommand

from accounts.models import User


class Command(BaseCommand):
    help = "Export users to xlsx file"

    def add_arguments(self, parser):
        # Positional arguments
        parser.add_argument("filename", type=str)

    def handle(self, *args, **options):
        filename = options["filename"]
        users = User.objects.all()
        data = []
        for user in users:
            try:
                allowed_frontends = json.loads(user.allowed_frontends)
            except Exception:
                allowed_frontends = []
            try:
                allowed_frontends = [item["app"] for item in allowed_frontends]
            except Exception:
                allowed_frontends = []
            has_dashboard_access = "dashboard" in allowed_frontends
            has_smartclient_access = "smartclient" in allowed_frontends
            has_portal_access = "website" in allowed_frontends
            userdata = [
                user.get_full_name(),
                user.email,
                has_dashboard_access,
                has_smartclient_access,
                has_portal_access,
                user.is_employee,
                user.is_assessor,
            ]
            data.append(userdata)
        headers = [
            "Name",
            "email",
            "dashboard",
            "smartclient",
            "portal",
            "employee",
            "assessor",
        ]
        xlsx_data = tablib.Dataset(*data, headers=headers).xlsx
        with open(filename, "wb") as f:
            f.write(xlsx_data)
