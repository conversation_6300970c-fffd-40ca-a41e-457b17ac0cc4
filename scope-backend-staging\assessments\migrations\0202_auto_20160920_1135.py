from django.db import migrations


def default_bs_to_zero(apps, schema_editor):
    BalanceSheet = apps.get_model("assessments", "BalanceSheet")
    fields = [
        "cash",
        "account_receivables",
        "other_receivables",
        "inventories",
        "other_current_assets",
        "fixed_assets",
        "intangible_assets",
        "goodwill",
        "other_non_current_assets",
        "accounts_payable",
        "short_term_loans",
        "overdrafts",
        "income_tax_payable",
        "short_term_provisions",
        "other_current_liabilities",
        "long_term_loans",
        "deferred_tax",
        "provisions",
        "other_non_current_liabilities",
        "share_capital",
        "share_premium",
        "retained_earnings",
        "grants",
        "statutory_legal_reserves",
        "other_reserves",
        "other",
    ]
    for field in fields:
        filter_kwargs = {"{}__isnull".format(field): True}
        update_kwargs = {field: 0}
        BalanceSheet.objects.filter(**filter_kwargs).update(**update_kwargs)


class Migration(migrations.Migration):

    dependencies = [("assessments", "0201_auto_20160919_1758")]

    operations = [migrations.RunPython(default_bs_to_zero)]
