import datetime

from denorm import flush
from django.test import TestCase

from assessments.factories import AssessmentFactory
from libs.test_helpers import DenormMixin
from reports.factories import ReportFactory
from reports.models import Report


class ReassessmentCounterTestCase(DenormMixin, TestCase):
    def test_start_0(self):
        assessment = AssessmentFactory.create()
        flush()
        assessment.refresh_from_db()
        self.assertEqual(0, assessment.reassessment_counter)

    def test_previously_finalized(self):
        report = ReportFactory.create(status=Report.STATUS_FINAL)
        assessment = AssessmentFactory.create(
            producing_organization=report.assessment.producing_organization,
            date=report.finalized_at + datetime.timedelta(days=1),
        )
        flush()
        assessment.refresh_from_db()
        self.assertEqual(1, assessment.reassessment_counter)

    def test_insert_in_middle(self):
        assessments = [AssessmentFactory.create()]
        assessments.append(
            AssessmentFactory.create(
                producing_organization=assessments[0].producing_organization,
                date=assessments[0].date + datetime.timedelta(days=2),
            )
        )
        flush()
        for assessment in assessments:
            assessment.refresh_from_db()
        self.assertEqual(0, assessments[0].reassessment_counter)
        self.assertEqual(1, assessments[1].reassessment_counter)
        middle_assessment = AssessmentFactory.create(
            producing_organization=assessments[0].producing_organization,
            date=assessments[0].date + datetime.timedelta(days=1),
        )
        flush()
        for assessment in assessments:
            assessment.refresh_from_db()
        middle_assessment.refresh_from_db()
        self.assertEqual(0, assessments[0].reassessment_counter)
        self.assertEqual(1, middle_assessment.reassessment_counter)
        self.assertEqual(2, assessments[1].reassessment_counter)

    def test_duplicate_customer(self):
        assessments = [AssessmentFactory.create()]
        assessments.append(
            AssessmentFactory.create(
                producing_organization__customer__duplicate_of=assessments[
                    0
                ].producing_organization.customer,
                date=assessments[0].date + datetime.timedelta(days=1),
            )
        )
        flush()
        for assessment in assessments:
            assessment.refresh_from_db()
        self.assertEqual(0, assessments[0].reassessment_counter)
        self.assertEqual(1, assessments[1].reassessment_counter)

    def test_duplicate_customer_later_on(self):
        assessments = [AssessmentFactory.create()]
        assessments.append(
            AssessmentFactory.create(
                date=assessments[0].date + datetime.timedelta(days=1)
            )
        )
        flush()
        for assessment in assessments:
            assessment.refresh_from_db()
        self.assertEqual(0, assessments[0].reassessment_counter)
        self.assertEqual(0, assessments[1].reassessment_counter)
        assessments[1].producing_organization.customer.duplicate_of = assessments[
            0
        ].producing_organization.customer
        assessments[1].producing_organization.customer.save()
        flush()
        for assessment in assessments:
            assessment.refresh_from_db()
        self.assertEqual(0, assessments[0].reassessment_counter)
        self.assertEqual(1, assessments[1].reassessment_counter)
