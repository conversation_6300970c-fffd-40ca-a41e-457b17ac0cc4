from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("hrm", "0003_auto_20141118_0858"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("assessments", "0019_auto_20141118_0946"),
    ]

    operations = [
        migrations.CreateModel(
            name="UnscoredSectionResponse",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("answer", models.TextField()),
                ("accepted", models.BooleanField(default=False)),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="UnscoredSectionResponseComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="UnscoredToolResponse",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("answer", models.TextField()),
                ("accepted", models.BooleanField(default=False)),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="unscored_tool_responses",
                        to="assessments.Assessment",
                    ),
                ),
                (
                    "assessor",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="unscored_tool_responses",
                        to="hrm.Assessor",
                    ),
                ),
                (
                    "quality_controller",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="unscored_tool_responses_quality_controlled",
                        blank=True,
                        to="hrm.Employee",
                        null=True,
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="UnscoredToolResponseComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "unscoredtoolresponse",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.UnscoredToolResponse",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="unscoredtoolresponsecomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.RemoveField(model_name="unscoredresponse", name="assessment"),
        migrations.RemoveField(model_name="unscoredresponse", name="assessor"),
        migrations.RemoveField(
            model_name="unscoredresponse", name="quality_controller"
        ),
        migrations.RemoveField(model_name="unscoredresponse", name="question"),
        migrations.RemoveField(
            model_name="unscoredresponsecomment", name="unscoredresponse"
        ),
        migrations.RemoveField(model_name="unscoredresponsecomment", name="user"),
        migrations.DeleteModel(name="UnscoredResponseComment"),
        migrations.RemoveField(
            model_name="unscoredresponsedocument", name="quality_controller"
        ),
        migrations.RemoveField(
            model_name="unscoredresponsedocument", name="unscoredresponse"
        ),
        migrations.DeleteModel(name="UnscoredResponse"),
        migrations.RemoveField(
            model_name="unscoredresponsedocumentcomment",
            name="unscoredresponsedocument",
        ),
        migrations.DeleteModel(name="UnscoredResponseDocument"),
        migrations.RemoveField(
            model_name="unscoredresponsedocumentcomment", name="user"
        ),
        migrations.DeleteModel(name="UnscoredResponseDocumentComment"),
    ]
