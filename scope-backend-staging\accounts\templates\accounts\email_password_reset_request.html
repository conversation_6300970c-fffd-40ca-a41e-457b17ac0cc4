{% load static i18n %}
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<title>{{ message.subject }} - SCOPE Insight</title>
<style type="text/css">
.ReadMsgBody {
  width: 100%;
  background-color: #fAfAfA;
}
.ExternalClass {
  width: 100%;
  background-color: #fAfAfA;
}
* {
  margin: 0;
  padding: 0;
}
body  {
  width: 100%;
  background-color: #f3f3f4;
  margin:0;
  padding:0;
}
table {
  border-collapse: collapse;
}
*[class~=spacer] {
  line-height: 13px;
}
#content {
  padding: 0 15px 2em;
  color: #676a6c;
  font-family: 'open sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  border-bottom: 3px solid #b4b9bd;
}
#content h1 {
  padding: 20px 0;
  color: #676a6c;
  font-family: 'open sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 21px;
  font-weight: 700;
}
#content a {
  color: #35383a;
}
*[class~=unsubscribe-footer] a {
  color: #ffffff;
}
@media screen and (max-width: 620px) {
  img {
    max-width: 100%;
    height: auto;
  }
  *[class~=row], *[class~=spacer] {
    width: 490px;
  }
  *[class~=socialmedia-footer] {
    width: 100px;
  }
}
@media screen and (max-width: 510px) {
  *[class~=row], *[class~=spacer] {
    width: 320px;
  }
  *[class~=hide-small] {
    display: none;
  }
  *[class~=unsubscribe-footer] {
    text-align: center !important;
  }
  *[class~=logo] {
    width: 100%;
  }
  *[class~=logo_img] {
    padding: 20px 0 30px !important;
  }
}
</style>
</head>
<body style="margin: 0;">
<div style="background-color:#f3f3f4;">
  <table height="100%" width="100%" cellpadding="0" cellspacing="0" border="0" summary="mailing">
    <tr>
      <td valign="top" align="center">
        <table width="600" align="center" cellpadding="0" cellspacing="0" border="0" summary="Content" class="row">
          <tr><td>
              <table cellpadding="0" cellspacing="0" align="left" class="logo" summary="logo">
                <tr><td align="left">
                  <a href="#" target="_blank" style="border: 0;"><img src="{{ host }}{% static "messaging/logo.jpg" %}" alt="Scope" style="padding: 15px; border: 0; width: 100px; height: auto;" /></a>
                </td></tr>
              </table>
          </td></tr>
          <tr>
            <td align="left" valign="top" bgcolor="#ffffff" style="white-space: pre-wrap; padding: 0 15px 2em; color: #676a6c; font-family: 'open sans', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 14px; line-height: 1.4; border-bottom: 3px solid #b4b9bd" id="content">
              {% blocktrans with frontend_host=frontend_host token=instance.uuid %}Please proceed to {{ frontend_host }}/#/set-password?token={{ token }} to set your password.{% endblocktrans %}
            </td>
          </tr>
        </table>
        <table width="600" align="center" cellpadding="0" cellspacing="0" border="0" summary="Footer" class="row">
          <tr>
            <td bgcolor="#2f4050" align="center">
              <table width="600" align="center" cellpadding="0" cellspacing="0" border="0" summary="footerimages" class="row">
                <tr>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</div>
</body>
</html>
