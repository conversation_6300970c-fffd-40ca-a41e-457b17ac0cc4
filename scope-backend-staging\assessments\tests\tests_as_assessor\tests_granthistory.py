import json

from rest_framework.reverse import reverse

from assessments.factories import AssessmentFactory, GrantHistoryFactory
from libs.test_helpers import AssessorJ<PERSON>TTestCase, DenormMixin


class GrantHistoryTestCase(DenormMixin, AssessorJWTTestCase):
    def test_create_with_no_amount(self):
        """
        When creating object, currency and amount are null
        """
        a = AssessmentFactory.create(assessmentassignments__assessor=self.assessor)
        data = {
            "assessment": reverse("assessments:assessment-detail", [a.pk]),
            "comment": "1233",
            "display_funder_type": "Donor",
            "display_in_cash_in_kind": "In cash",
            "funder": "213",
            "funder_type": "Donor",
            "in_cash_in_kind": "In cash",
            "purpose": "213",
            "start_date": "2019-03-02",
        }
        url = reverse("assessments:granthistory-list")
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        response_dict = json.loads(response.content)
        self.assertIsNone(response_dict["amount"])
