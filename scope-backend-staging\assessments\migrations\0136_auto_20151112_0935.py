from django.conf import settings
from django.db import migrations, models

import libs.models_helpers


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0051_auto_20151104_1653"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("hrm", "0013_assessor_second_region"),
        ("assessments", "0135_auto_20151022_1740"),
    ]

    operations = [
        migrations.CreateModel(
            name="SubResponse",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("comment", models.TextField(blank=True)),
                ("accepted", models.<PERSON>oleanField(default=False)),
                (
                    "score",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=4,
                        decimal_places=3,
                        blank=True,
                    ),
                ),
                (
                    "weight",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=4,
                        blank=True,
                    ),
                ),
                ("subquestion_title", models.TextField(editable=False, blank=True)),
                (
                    "subquestion_display_position",
                    models.TextField(editable=False, blank=True),
                ),
                (
                    "_response",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="subresponses",
                        blank=True,
                        to="assessments.Response",
                        null=True,
                    ),
                ),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="subresponses",
                        to="assessments.Assessment",
                    ),
                ),
                (
                    "assessor",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="subresponses",
                        blank=True,
                        to="hrm.Assessor",
                        null=True,
                    ),
                ),
                (
                    "checked_choices",
                    models.ManyToManyField(to="products.SubQuestionOption"),
                ),
                (
                    "quality_controller",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="subresponses_quality_controlled",
                        blank=True,
                        to="hrm.Employee",
                        null=True,
                    ),
                ),
                (
                    "subquestion",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="subresponses",
                        to="products.SubQuestion",
                    ),
                ),
            ],
            options={"ordering": ("subquestion__position",)},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="SubResponseComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "_assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        editable=False,
                        to="assessments.Assessment",
                        null=True,
                    ),
                ),
                (
                    "subresponse",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.SubResponse",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="subresponsecomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="SubResponseDocument",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "file",
                    models.FileField(
                        null=True,
                        upload_to=libs.models_helpers.class_based_upload_to,
                        blank=True,
                    ),
                ),
                ("accepted", models.BooleanField(default=False)),
                (
                    "_assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        editable=False,
                        to="assessments.Assessment",
                        null=True,
                    ),
                ),
                (
                    "quality_controller",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="subresponsedocument_quality_controlled",
                        blank=True,
                        to="hrm.Employee",
                        null=True,
                    ),
                ),
                (
                    "subresponse",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="documents",
                        to="assessments.SubResponse",
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="SubResponseDocumentComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "_assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        editable=False,
                        to="assessments.Assessment",
                        null=True,
                    ),
                ),
                (
                    "subresponsedocument",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.SubResponseDocument",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="subresponsedocumentcomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.AlterUniqueTogether(
            name="subresponse", unique_together=set([("assessment", "subquestion")])
        ),
    ]
