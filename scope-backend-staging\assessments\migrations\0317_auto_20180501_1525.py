# Generated by Django 1.11.12 on 2018-05-01 15:25


from django.db import migrations


def copy_submitted_at_least_once_from_assessment_to_assignment(apps, schema_editor):
    AssessmentAssignment = apps.get_model("assessments", "AssessmentAssignment")
    for aa in AssessmentAssignment.objects.all():
        aa.submitted_at_least_once = aa.assessment.submitted_at_least_once
        aa.save()


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0316_assessmentassignment_submitted_at_least_once")
    ]

    operations = [
        migrations.RunPython(copy_submitted_at_least_once_from_assessment_to_assignment)
    ]
