# Generated by Django 1.10.5 on 2017-04-28 08:30


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("hrm", "0032_employee_qc_only"),
        ("assessments", "0272_auto_20170419_1211"),
    ]

    operations = [
        migrations.AddField(
            model_name="assessmentassignment",
            name="assigned_as",
            field=models.CharField(
                choices=[
                    ("assessor", "assessor"),
                    ("financial_specialist", "financial_specialist"),
                ],
                default="assessor",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="assessmentinvitation",
            name="assigned_as",
            field=models.<PERSON>r<PERSON>ield(
                choices=[
                    ("assessor", "assessor"),
                    ("financial_specialist", "financial_specialist"),
                ],
                default="assessor",
                max_length=20,
            ),
        ),
        migrations.AlterUniqueTogether(
            name="assessmentassignment",
            unique_together=set([("assessment", "assessor", "assigned_as")]),
        ),
        migrations.AlterUniqueTogether(
            name="assessmentinvitation",
            unique_together=set([("assessment", "assessor", "assigned_as")]),
        ),
    ]
