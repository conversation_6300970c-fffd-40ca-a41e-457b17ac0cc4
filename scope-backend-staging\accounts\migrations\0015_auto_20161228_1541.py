# Generated by Django 1.10.4 on 2016-12-28 15:41


from collections import Counter

from django.db import migrations


def remove_duplicate_password_reset_requests(apps, schema_editor):
    PasswordResetRequest = apps.get_model("accounts", "PasswordResetRequest")
    c = Counter(
        PasswordResetRequest.objects.order_by().values_list("user_id", flat=True)
    )
    duplicates = list(
        filter(lambda user_id_count: user_id_count[1] > 1, list(c.items()))
    )
    for user_id, count in duplicates:
        objs = PasswordResetRequest.objects.filter(user_id=user_id).order_by("pk")[
            : count - 1
        ]
        for obj in objs:
            obj.delete()


class Migration(migrations.Migration):

    dependencies = [("accounts", "0014_passwordresetrequest_modified_at")]

    operations = [migrations.RunPython(remove_duplicate_password_reset_requests)]
