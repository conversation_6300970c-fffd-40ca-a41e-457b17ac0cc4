# Generated by Django 1.11.6 on 2017-10-20 10:21


from django.db import migrations


def set_build_response_tree_completed(apps, schema_editor):
    Assessment = apps.get_model("assessments", "Assessment")
    Assessment.objects.all().update(build_response_tree_completed=True)


class Migration(migrations.Migration):

    dependencies = [("assessments", "0292_assessment_build_response_tree_completed")]

    operations = [migrations.RunPython(set_build_response_tree_completed)]
