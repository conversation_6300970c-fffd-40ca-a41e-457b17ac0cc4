# Generated by Django 1.10.5 on 2017-04-06 12:27


from django.db import migrations


def change_assessment_status_if_no_assignment(apps, schema_editor):
    Assessment = apps.get_model("assessments", "Assessment")
    Assessment.objects.filter(submitted_at_least_once=True).filter(
        assessmentassignments__isnull=True
    ).update(status="created")


class Migration(migrations.Migration):

    dependencies = [("assessments", "0269_merge_20170404_0905")]

    operations = [migrations.RunPython(change_assessment_status_if_no_assignment)]
