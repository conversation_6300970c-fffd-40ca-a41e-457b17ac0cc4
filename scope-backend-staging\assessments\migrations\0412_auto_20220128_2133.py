# Generated by Django 3.1.14 on 2022-01-28 20:33

from django.db import migrations


def update_question_type(apps, schema_editor):
    SubResponse = apps.get_model("assessments", "SubResponse")
    SubQuestion = apps.get_model("products", "SubQuestion")

    for subquestion in SubQuestion.objects.filter(
        title="Which part of the objectives have been achieved in the past 12 months?"
    ):
        SubQuestion.objects.filter(id=subquestion.id).update(type="mpc")

    for subresponse in SubResponse.objects.filter(
        subquestion__title="Which part of the objectives have been achieved in the past 12 months?"
    ):
        SubResponse.objects.filter(id=subresponse.id).update(
            selected_option=subresponse.checked_options.first()
        )
        # subresponse.checked_options.clear()


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0411_auto_20211231_1001"),
    ]

    operations = [migrations.RunPython(update_question_type)]
