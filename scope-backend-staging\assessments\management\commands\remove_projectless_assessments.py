from django.core.management.base import BaseCommand
from django.db.models.deletion import Collector
from django.db.models.signals import post_save, pre_delete, pre_save
from factory.django import mute_signals

from assessments.models import Assessment


class Command(BaseCommand):
    help = "Delete projectless assessments and their related objects"

    def handle(self, *args, **options):
        c = Collector(using="default")
        c.collect([a for a in Assessment.objects.filter(project__isnull=True)])
        c.sort()

        for model, instance in c.instances_with_model():
            with mute_signals(post_save, pre_save, pre_delete):
                model.objects.filter(id=instance.id).delete()
