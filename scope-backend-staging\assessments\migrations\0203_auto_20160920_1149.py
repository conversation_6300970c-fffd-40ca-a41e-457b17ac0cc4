from decimal import Decimal

import djmoney.models.fields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [("assessments", "0202_auto_20160920_1135")]

    operations = [
        migrations.AlterField(
            model_name="balancesheet",
            name="account_receivables",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Account receivables",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="accounts_payable",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Accounts payable",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="cash",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Cash and cash equivalents",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="deferred_tax",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Deferred tax",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="fixed_assets",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Fixed assets",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="goodwill",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Goodwill",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="grants",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Grants (seed capital)",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="income_tax_payable",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Income tax payable",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="intangible_assets",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Intangible assets",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="inventories",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Inventories",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="long_term_loans",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Long term loans",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Other",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other_current_assets",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Other current assets",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other_current_liabilities",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Other current liabilities",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other_non_current_assets",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Other non-current assets",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other_non_current_liabilities",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Other non current liabilities",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other_receivables",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Other receivables",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other_reserves",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Other reserves",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="overdrafts",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Overdrafts",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="provisions",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Provisions",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="retained_earnings",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Retained earnings",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="share_capital",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Share capital",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="share_premium",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Share premium",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="short_term_loans",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Short term loans",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="short_term_provisions",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Short term provisions",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="statutory_legal_reserves",
            field=djmoney.models.fields.MoneyField(
                default=Decimal("0.0"),
                verbose_name="Statutory/legal reserves",
                max_digits=20,
                decimal_places=2,
            ),
            preserve_default=True,
        ),
    ]
