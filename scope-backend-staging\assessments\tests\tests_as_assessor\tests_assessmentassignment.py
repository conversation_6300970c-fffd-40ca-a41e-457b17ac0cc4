import datetime
import json

from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import AssessmentAssignmentFactory
from assessments.models import AssessmentAssignment
from hrm.factories import EmployeeFactory
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class AssessmentAssignmentTestCase(DenormMixin, AssessorJWTTestCase):
    def test_can_only_see_own_assignments_when_also_employee(self):
        """
        When the assessor is also an employee, he/she should still only be
        able to see own assignments when using app
        """
        EmployeeFactory.create(user=self.assessor.user)
        AssessmentAssignmentFactory.create_batch(2)
        AssessmentAssignmentFactory.create_batch(3, assessor=self.assessor)
        url = reverse("assessments:assessmentassignment-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(3, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(3, len(response_dict["results"]))

    def test_can_get_assessmentassignment(self):
        """
        It should be possible to get an assessmentassignment
        """
        assignment = AssessmentAssignmentFactory.create(assessor=self.assessor)
        url = (
            reverse("assessments:assessmentassignment-detail", [assignment.pk])
            + "?fields=assessment"
        )
        modified_since = assignment.modified_at - datetime.timedelta(seconds=1)
        modified_since = modified_since.strftime("%a, %d %b %Y %H:%M:%S GMT")
        response = self.client.get(
            url, content_type="application/json", HTTP_IF_MODIFIED_SINCE=modified_since
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code, response.content)

    def test_status_str_in_detail_response(self):
        assignment = AssessmentAssignmentFactory.create(assessor=self.assessor)
        url = reverse("assessments:assessmentassignment-detail", [assignment.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("status", response_dict)
        self.assertEqual("in_progress", response_dict["status"])

    def test_can_submit_two_assignments(self):
        """
        When an assessor is both regular and financial assessor on a single
        assessment, it should be possible to submit both assignments
        """
        regular_assignment = AssessmentAssignmentFactory.create(
            assessor=self.assessor, assigned_as=AssessmentAssignment.AS_ASSESSOR
        )
        financial_assignment = AssessmentAssignmentFactory.create(
            assessor=self.assessor,
            assessment=regular_assignment.assessment,
            assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        self.assertFalse(regular_assignment.locked_for_assessor)
        self.assertTrue(regular_assignment.locked_for_employee)
        self.assertFalse(financial_assignment.locked_for_assessor)
        self.assertTrue(financial_assignment.locked_for_employee)
        data = {"locked_for_assessor": True, "locked_for_employee": False}
        regular_url = reverse(
            "assessments:assessmentassignment-detail", [regular_assignment.pk]
        )
        regular_response = self.client.patch(
            regular_url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, regular_response.status_code)
        financial_url = reverse(
            "assessments:assessmentassignment-detail", [financial_assignment.pk]
        )
        financial_response = self.client.patch(
            financial_url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, financial_response.status_code)
        regular_assignment.refresh_from_db()
        financial_assignment.refresh_from_db()
        self.assertTrue(regular_assignment.locked_for_assessor)
        self.assertFalse(regular_assignment.locked_for_employee)
        self.assertTrue(financial_assignment.locked_for_assessor)
        self.assertFalse(financial_assignment.locked_for_employee)

    def test_can_edit_own_assignment(self):
        assignment = AssessmentAssignmentFactory.create(assessor=self.assessor)
        self.assertTrue(assignment.ax_can_edit(self.jwt_user))
