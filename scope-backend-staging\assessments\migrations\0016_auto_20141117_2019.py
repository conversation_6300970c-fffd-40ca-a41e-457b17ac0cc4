from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0015_auto_20141112_1931")]

    operations = [
        migrations.RemoveField(model_name="balancesheetcomment", name="balancesheet"),
        migrations.RemoveField(model_name="balancesheetcomment", name="user"),
        migrations.DeleteModel(name="BalanceSheetComment"),
        migrations.RemoveField(
            model_name="profitlossstatementcomment", name="profitlossstatement"
        ),
        migrations.RemoveField(model_name="profitlossstatementcomment", name="user"),
        migrations.DeleteModel(name="ProfitLossStatementComment"),
        migrations.AddField(
            model_name="balancesheet",
            name="comments",
            field=models.TextField(default="", blank=True),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="comments",
            field=models.TextField(default="", blank=True),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="comments",
            field=models.TextField(default="", blank=True),
            preserve_default=False,
        ),
    ]
