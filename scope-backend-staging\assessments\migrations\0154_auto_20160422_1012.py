from django.db import migrations


def move_input_purchases_to_product_level(apps, schema_editor):
    InputPurchase = apps.get_model("assessments", "InputPurchase")
    for input_purchase in InputPurchase.objects.all():
        try:
            product = input_purchase.assessment.products.all()[0]
        except IndexError:
            input_purchase.delete()
        else:
            input_purchase.product = product
            input_purchase.save()


class Migration(migrations.Migration):

    dependencies = [("assessments", "0153_inputpurchase_product")]

    operations = [migrations.RunPython(move_input_purchases_to_product_level)]
