from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0032_auto_20141230_2134")]

    operations = [
        migrations.AlterField(
            model_name="inputpurchase",
            name="price",
            field=models.DecimalField(max_digits=20, decimal_places=2),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="inputpurchase",
            name="volume",
            field=models.DecimalField(max_digits=20, decimal_places=2),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="productionfigure",
            name="volume",
            field=models.DecimalField(max_digits=20, decimal_places=2),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="productionpurchase",
            name="price",
            field=models.DecimalField(max_digits=20, decimal_places=2),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="productionpurchase",
            name="volume",
            field=models.DecimalField(max_digits=20, decimal_places=2),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="productionsale",
            name="price",
            field=models.DecimalField(max_digits=20, decimal_places=2),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="productionsale",
            name="volume",
            field=models.DecimalField(max_digits=20, decimal_places=2),
            preserve_default=True,
        ),
    ]
