from collections import Counter

from django.db import migrations, models


def fill_assessment_currency(apps, schema_editor):
    """
    Fill assessment.currency by popular vote
    """
    Assessment = apps.get_model("assessments", "Assessment")
    BalanceSheet = apps.get_model("assessments", "BalanceSheet")
    CashFlowStatement = apps.get_model("assessments", "CashFlowStatement")
    ProfitLossStatement = apps.get_model("assessments", "ProfitLossStatement")
    assessments = Assessment.objects.filter(
        models.Q(
            id__in=(BalanceSheet.objects.all().values_list("assessment_id", flat=True))
        )
        | models.Q(
            id__in=(
                CashFlowStatement.objects.all().values_list("assessment_id", flat=True)
            )
        )
        | models.Q(
            id__in=(
                ProfitLossStatement.objects.all().values_list(
                    "assessment_id", flat=True
                )
            )
        )
    )
    balancesheet_currencies = [
        field_name
        for field_name in [field.name for field in BalanceSheet._meta.get_fields()]
        if field_name.endswith("_currency") and not field_name.startswith("_")
    ]
    annotated_balancesheet_currencies = [
        "balancesheets__{}".format(field_name) for field_name in balancesheet_currencies
    ]
    cashflowstatement_currencies = [
        field_name
        for field_name in [field.name for field in CashFlowStatement._meta.get_fields()]
        if field_name.endswith("_currency") and not field_name.startswith("_")
    ]
    annotated_cashflowstatement_currencies = [
        "cashflowstatements__{}".format(field_name)
        for field_name in cashflowstatement_currencies
    ]
    profitlossstatement_currencies = [
        field_name
        for field_name in [
            field.name for field in ProfitLossStatement._meta.get_fields()
        ]
        if field_name.endswith("_currency") and not field_name.startswith("_")
    ]
    annotated_profitlossstatement_currencies = [
        "profitlossstatements__{}".format(field_name)
        for field_name in profitlossstatement_currencies
    ]
    currency_fields = (
        annotated_balancesheet_currencies
        + annotated_cashflowstatement_currencies
        + annotated_profitlossstatement_currencies
    )
    for assessment in assessments:
        currencies = Counter(
            list(
                filter(
                    lambda item: item != "XYZ",
                    [
                        item
                        for sublist in (
                            Assessment.objects.filter(pk=assessment.pk).values_list(
                                *currency_fields
                            )
                        )
                        for item in sublist
                    ],
                )
            )
        )
        try:
            winning_currency = currencies.most_common(1)[0][0]
        except IndexError:
            winning_currency = "USD"
        if winning_currency is None:
            winning_currency = "USD"
        balancesheet_update = dict(
            [(field_name, winning_currency) for field_name in balancesheet_currencies]
        )
        cashflowstatement_update = dict(
            [
                (field_name, winning_currency)
                for field_name in cashflowstatement_currencies
            ]
        )
        profitlossstatement_update = dict(
            [
                (field_name, winning_currency)
                for field_name in profitlossstatement_currencies
            ]
        )
        Assessment.objects.filter(pk=assessment.pk).update(currency=winning_currency)
        BalanceSheet.objects.filter(assessment_id=assessment.pk).update(
            **balancesheet_update
        )
        CashFlowStatement.objects.filter(assessment_id=assessment.pk).update(
            **cashflowstatement_update
        )
        ProfitLossStatement.objects.filter(assessment_id=assessment.pk).update(
            **profitlossstatement_update
        )


class Migration(migrations.Migration):

    dependencies = [("assessments", "0171_auto_20160526_1953")]

    operations = [migrations.RunPython(fill_assessment_currency)]
