import datetime
import json
import random

import pytz
from denorm import flush
from django.core import mail
from freezegun import freeze_time
from mock import patch
from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import (
    AssessmentAssignmentFactory,
    AssessmentFactory,
    AssessmentPurposeFactory,
    BankAccountFactory,
    BasicFinancialInfoFactory,
    GrantHistoryFactory,
    LoanHistoryFactory,
    LoanRequirementFactory,
    PreFinanceHistoryFactory,
    ProductionFigureFactory,
    SupplierFactory,
    TermsAndConditionsFactory,
)
from assessments.models import Assessment, AssessmentAssignment
from assessments.tasks import build_response_tree_task
from assessments.views import AssessmentViewSet
from customers.factories import ContactFactory, SectorFactory
from hrm.factories import EmployeeFactory
from libs.test_helpers import AssessorJWTTestCase, DenormMixin
from messaging.factories import MessageTypeFactory
from products.factories import (
    ProductTypeOptionFactory,
    QuestionFactory,
    SectionFactory,
    ToolFactory,
)
from reports.factories import ReportFactory
from reports.models import Report


class AssessmentTestCase(DenormMixin, AssessorJWTTestCase):
    maxDiff = None

    def test_projections_year_can_be_set(self):
        """
        projections_year should be settable
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {"projections_year": 2013}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        assessment = Assessment.objects.get(pk=assessment.pk)
        self.assertEqual(2013, assessment.projections_year)

    def test_completion_check_required_defaults_to_true(self):
        """
        completion_check_required should default to True
        """
        assessment = AssessmentFactory.create()
        self.assertTrue(assessment.completion_check_required)

    def test_completion_check_required_visible_in_api(self):
        """
        completion_check_required should be visible in the api
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("completion_check_required", response_dict)

    def test_completion_check_required_not_writable(self):
        """
        completion_check_required should not be writable
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {"completion_check_required": False}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        assessment = Assessment.objects.get(pk=assessment.pk)
        self.assertTrue(assessment.completion_check_required)

    def test_full_completion_check_required_defaults_to_true(self):
        """
        full_completion_check_required should default to True
        """
        assessment = AssessmentFactory.create()
        self.assertTrue(assessment.full_completion_check_required)

    def test_with_terms_and_conditions_defaults_to_true(self):
        """
        with_terms_and_conditions should default to True
        """
        assessment = AssessmentFactory.create()
        self.assertTrue(assessment.with_terms_and_conditions)

    def test_is_new_basic_tool_defaults_to_true(self):
        """
        is_new_basic_tool should default to True
        """
        assessment = AssessmentFactory.create()
        self.assertTrue(assessment.is_new_basic_tool)

    def test_with_no_info_reason_defaults_to_true(self):
        """
        with_no_info_reason should default to True
        """
        assessment = AssessmentFactory.create()
        self.assertTrue(assessment.with_no_info_reason)

    def test_full_completion_check_required_visible_in_api(self):
        """
        full_completion_check_required should be visible in the api
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("full_completion_check_required", response_dict)

    def test_with_terms_and_conditions_visible_in_api(self):
        """
        with_terms_and_conditions should be visible in the api
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("with_terms_and_conditions", response_dict)

    def test_is_new_basic_tool_visible_in_api(self):
        """
        is_new_basic_tool should be visible in the api
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("is_new_basic_tool", response_dict)

    def test_with_no_info_reason_visible_in_api(self):
        """
        with_no_info_reason should be visible in the api
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("with_no_info_reason", response_dict)

    def test_full_completion_check_required_not_writable(self):
        """
        full_completion_check_required should not be writable
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {"full_completion_check_required": False}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        assessment = Assessment.objects.get(pk=assessment.pk)
        self.assertTrue(assessment.full_completion_check_required)

    def test_with_terms_and_conditions_not_writable(self):
        """
        with_terms_and_conditions should not be writable
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {"with_terms_and_conditions": False}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        assessment = Assessment.objects.get(pk=assessment.pk)
        self.assertTrue(assessment.with_terms_and_conditions)

    def test_is_new_basic_tool_not_writable(self):
        """
        is_new_basic_tool should not be writable
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {"is_new_basic_tool": False}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        assessment = Assessment.objects.get(pk=assessment.pk)
        self.assertTrue(assessment.is_new_basic_tool)

    def test_with_no_info_reason_not_writable(self):
        """
        with_no_info_reason should not be writable
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {"with_no_info_reason": False}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        assessment = Assessment.objects.get(pk=assessment.pk)
        self.assertTrue(assessment.with_no_info_reason)

    def test_can_set_start_date(self):
        """
        Assessor should be able to set start_date on assessment object
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {"start_date": "2016-10-10"}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        db_assessment = Assessment.objects.get(pk=assessment.pk)
        self.assertEqual("2016-10-10", db_assessment.start_date.isoformat())

    def test_can_patch_empty_purposes(self):
        """
        It should be possible to patch an empty list to assessment.purposes
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {"purposes": []}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_notifications_sent_on_submit(self):
        """
        A notification should be sent to qc on submit with the correct template_body
        """
        self.assessor.user.email_notifications_on = True
        self.assessor.user.save()
        MessageTypeFactory.create(slug="quality_control_pending")
        MessageTypeFactory.create(slug="quality_control_pending_to_assessor")
        employee = EmployeeFactory.create(
            user__email="<EMAIL>",
            user__first_name="Mr",
            user__last_name="QR",
            user__email_notifications_on=True,
            user__language="en",
        )
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor,
            producing_organization__customer__name="Wut",
            quality_reviewer=employee.user,
        )

        assignment_id = assessment.assessmentassignments.all()[0].id
        url = reverse("assessments:assessmentassignment-detail", [assignment_id])
        data = {"locked_for_assessor": True, "locked_for_employee": False}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        self.assertEqual(2, len(mail.outbox))
        ax_message = mail.outbox[1]
        qc_message = mail.outbox[0]
        html = ax_message.alternatives[0][0]
        self.assertIn("Assessment submitted for review", html)
        self.assertIn(
            "You have just submitted your assessment for Wut for quality control. Within 3 business days you will receive feedback from SCOPEinsight.",
            html,
        )
        expected_link = (
            "http://dashboard.testserver/#/assessments/assignment/detail/{}".format(
                assignment_id
            )
        )
        self.assertEqual([employee.user.email], qc_message.to)
        self.assertIn(expected_link, qc_message.body)

    def test_notifications_sent_on_submit_financial_specialist(self):
        """
        A notification should be sent to fiancial qc on submit of Assignment(financial) with the correct template_body
        """
        MessageTypeFactory.create(slug="quality_control_pending")
        MessageTypeFactory.create(slug="quality_control_pending_to_assessor")
        qc = EmployeeFactory.create(
            user__email="<EMAIL>",
            user__first_name="Norman",
            user__last_name="QR",
            user__email_notifications_on=True,
            user__language="en",
        )
        fin_qc = EmployeeFactory.create(
            user__email="<EMAIL>",
            user__first_name="Finn",
            user__last_name="QR",
            user__email_notifications_on=True,
            user__language="en",
        )
        self.assessor.user.email_notifications_on = True
        self.assessor.user.language = "en"
        self.assessor.user.save()
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor,
            assessmentassignments__assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
            producing_organization__customer__name="Wut",
            quality_reviewer=qc.user,
            financial_quality_reviewer=fin_qc.user,
        )

        assignment_id = assessment.assessmentassignments.all()[0].id
        url = reverse("assessments:assessmentassignment-detail", [assignment_id])
        data = {"locked_for_assessor": True, "locked_for_employee": False}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        self.assertEqual(2, len(mail.outbox))
        ax_message = mail.outbox[1]
        fin_qc_message = mail.outbox[0]
        ax_html = ax_message.alternatives[0][0]
        fin_qc_html = fin_qc_message.alternatives[0][0]
        self.assertIn(
            "You have just submitted your financial assessment for Wut for quality control. Within 3 business days you will receive feedback from SCOPEinsight.",
            ax_html,
        )
        self.assertIn("Dear Finn QR,", fin_qc_html)
        expected_link = (
            "http://dashboard.testserver/#/assessments/assignment/detail/{}".format(
                assignment_id
            )
        )
        self.assertEqual([fin_qc.user.email], fin_qc_message.to)
        self.assertIn(expected_link, fin_qc_message.body)

    def test_notifications_sent_on_submit_is_off(self):
        """
        A notification should be sent to qc on submit with the correct template_body
        quality_control_pending is not sent
        quality_control_pending_to_assessor is sent
        """
        self.assessor.user.email_notifications_on = True
        self.assessor.user.save()
        MessageTypeFactory.create(slug="quality_control_pending")
        MessageTypeFactory.create(slug="quality_control_pending_to_assessor")
        employee = EmployeeFactory.create(
            user__email="<EMAIL>",
            user__first_name="Mr",
            user__last_name="QR",
            user__email_notifications_on=False,
        )
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor,
            producing_organization__customer__name="Wut",
            quality_reviewer=employee.user,
        )
        assignment_id = assessment.assessmentassignments.all()[0].id
        url = reverse("assessments:assessmentassignment-detail", [assignment_id])
        data = {"locked_for_assessor": True, "locked_for_employee": False}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        self.assertEqual(1, len(mail.outbox))

    def test_notifications_sent_on_submit_to_correct_recipients_ax_qr(self):
        """
        A notification should be sent to Assessment qc on submit to correct recipients
        Rules:
        If a Quality Reviewer is assigned to the assessment or project, send to the Quality Reviewer assigned
        Else send to the Project Lead of the project the assessment belongs to.
        """
        MessageTypeFactory.create(slug="quality_control_pending")
        MessageTypeFactory.create(slug="quality_control_pending_to_assessor")
        quality_reviewer = EmployeeFactory.create(
            user__email_notifications_on=True, user__language="en"
        )
        self.assessor.user.email_notifications_on = True
        self.assessor.user.language = "en"
        self.assessor.user.save()
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor,
            quality_reviewer=quality_reviewer.user,
        )
        assignment_id = assessment.assessmentassignments.all()[0].id
        url = reverse("assessments:assessmentassignment-detail", [assignment_id])
        data = {"locked_for_assessor": True, "locked_for_employee": False}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        qc_message = mail.outbox[0]
        self.assertEqual(quality_reviewer.user.email, qc_message.to[0])

    def test_notifications_sent_on_submit_to_correct_recipients_fin_qr(self):
        """
        A notification should be sent to Assessment qc on submit to correct recipients
        Rules:
        If a Quality Reviewer is assigned to the assessment or project, send to the Quality Reviewer assigned
        Else send to the Project Lead of the project the assessment belongs to.
        """
        MessageTypeFactory.create(slug="quality_control_pending")
        MessageTypeFactory.create(slug="quality_control_pending_to_assessor")
        qc = EmployeeFactory.create()
        fin_qc = EmployeeFactory.create(
            user__email_notifications_on=True, user__language="en"
        )
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor,
            assessmentassignments__assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
            quality_reviewer=qc.user,
            financial_quality_reviewer=fin_qc.user,
        )
        assignment_id = assessment.assessmentassignments.all()[0].id
        url = reverse("assessments:assessmentassignment-detail", [assignment_id])
        data = {"locked_for_assessor": True, "locked_for_employee": False}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        fin_qc_message = mail.outbox[0]
        self.assertEqual(fin_qc.user.email, fin_qc_message.to[0])

    def test_notifications_sent_on_submit_to_correct_recipients_project_qr(self):
        """
        A notification should be sent to Project qc on submit to correct recipients
        Rules:
        If a Quality Reviewer is assigned to the assessment or project, send to the Quality Reviewer assigned
        Else send to the Project Lead of the project the assessment belongs to.
        """
        MessageTypeFactory.create(slug="quality_control_pending")
        MessageTypeFactory.create(slug="quality_control_pending_to_assessor")
        quality_reviewer = EmployeeFactory.create(
            user__email_notifications_on=True, user__language="en"
        )
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor,
            project__quality_reviewer=quality_reviewer.user,
        )
        assignment_id = assessment.assessmentassignments.all()[0].id
        url = reverse("assessments:assessmentassignment-detail", [assignment_id])
        data = {"locked_for_assessor": True, "locked_for_employee": False}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        qc_message = mail.outbox[0]
        self.assertEqual(quality_reviewer.user.email, qc_message.to[0])

    def test_notifications_sent_on_submit_to_correct_recipients_project_contact(self):
        """
        A notification should be sent to Project lead (project.contact) on submit to correct recipients
        Rules:
        If a Quality Reviewer is assigned to the assessment or project, send to the Quality Reviewer assigned
        Else send to the Project Lead of the project the assessment belongs to.
        """
        MessageTypeFactory.create(slug="quality_control_pending")
        MessageTypeFactory.create(slug="quality_control_pending_to_assessor")
        contact = ContactFactory.create(
            access_to_dashboard="customer_admin",
            user__email_notifications_on=True,
            user__language="en",
        )
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor, project__contact=contact
        )
        assignment_id = assessment.assessmentassignments.all()[0].id
        url = reverse("assessments:assessmentassignment-detail", [assignment_id])
        data = {"locked_for_assessor": True, "locked_for_employee": False}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        qc_message = mail.outbox[0]
        self.assertEqual(contact.user.email, qc_message.to[0])

    def test_purposes_in_patch_response(self):
        """
        Purposes should be in the patch response
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        purposes = AssessmentPurposeFactory.create_batch(3)
        url = reverse("assessments:assessment-detail", [assessment.pk])
        url += "?fields=purposes"
        data = {
            "purposes": [
                reverse("assessments:assessmentpurpose-detail", [purpose.pk])
                for purpose in purposes
            ]
        }
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("purposes", response_dict)
        self.assertIn("objects", response_dict["purposes"])
        self.assertEqual(3, len(response_dict["purposes"]["objects"]))
        data = {
            "purposes": [
                reverse("assessments:assessmentpurpose-detail", [purpose.pk])
                for purpose in purposes[:-1]
            ]
        }
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("purposes", response_dict)
        self.assertIn("objects", response_dict["purposes"])
        self.assertEqual(2, len(response_dict["purposes"]["objects"]))
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertNotIn("purposes", response_dict)

    def test_creating_production_figures_triggers_200(self):
        """
        Creating a new ProductionFigure object should trigger a 200 on
        assessment GET
        """
        create_time = datetime.datetime.now(pytz.utc)
        modified_since_time = create_time + datetime.timedelta(seconds=1)
        fetch_time = create_time + datetime.timedelta(seconds=2)
        refetch_time = create_time + datetime.timedelta(seconds=3)
        with freeze_time(create_time):
            assessment = AssessmentFactory.create(
                assessmentassignments__assessor=self.assessor
            )
        assessment_url = reverse("assessments:assessment-detail", [assessment.pk])
        with freeze_time(modified_since_time):
            modified_since = datetime.datetime.now(pytz.utc).strftime(
                "%a, %d %b %Y %H:%M:%S GMT"
            )
        with freeze_time(fetch_time):
            long_ago = datetime.datetime(2000, 1, 1, 1, 1, 1, 1, pytz.utc)
            with self.settings(LAST_BREAKING_CODE_CHANGE=long_ago):
                assessment_response = self.client.get(
                    assessment_url,
                    content_type="application/json",
                    HTTP_IF_MODIFIED_SINCE=modified_since,
                )
            self.assertEqual(
                status.HTTP_304_NOT_MODIFIED, assessment_response.status_code
            )
        with freeze_time(refetch_time):
            ProductionFigureFactory.create(product__assessment=assessment)
            assessment_response = self.client.get(
                assessment_url,
                content_type="application/json",
                HTTP_IF_MODIFIED_SINCE=modified_since,
            )
            self.assertEqual(status.HTTP_200_OK, assessment_response.status_code)

    def test_patching_po_gps_location_triggers_200(self):
        """
        Patching the PO gps_location field should trigger a 200 on assessment
        GET
        """
        create_time = datetime.datetime.now(pytz.utc)
        modified_since_time = create_time + datetime.timedelta(seconds=1)
        fetch_time = create_time + datetime.timedelta(seconds=2)
        refetch_time = create_time + datetime.timedelta(seconds=3)
        with freeze_time(create_time):
            assessment = AssessmentFactory.create(
                assessmentassignments__assessor=self.assessor
            )
            flush()
        assessment_url = reverse("assessments:assessment-detail", [assessment.pk])
        with freeze_time(modified_since_time):
            modified_since = datetime.datetime.now(pytz.utc).strftime(
                "%a, %d %b %Y %H:%M:%S GMT"
            )
        with freeze_time(fetch_time):
            long_ago = datetime.datetime(2000, 1, 1, 1, 1, 1, 1, pytz.utc)
            with self.settings(LAST_BREAKING_CODE_CHANGE=long_ago):
                assessment_response = self.client.get(
                    assessment_url,
                    content_type="application/json",
                    HTTP_IF_MODIFIED_SINCE=modified_since,
                )
            self.assertEqual(
                status.HTTP_304_NOT_MODIFIED, assessment_response.status_code
            )
        with freeze_time(refetch_time):
            patch_response = self.client.patch(
                reverse(
                    "customers:producingorganization-detail",
                    [assessment.producing_organization_id],
                ),
                json.dumps({"gps_location": "1.1111, 2.1111"}),
                content_type="application/json",
            )
            self.assertEqual(status.HTTP_200_OK, patch_response.status_code)
            assessment_response = self.client.get(
                assessment_url,
                content_type="application/json",
                HTTP_IF_MODIFIED_SINCE=modified_since,
            )
            self.assertEqual(status.HTTP_200_OK, assessment_response.status_code)

    def test_products_no_information_available_default_false(self):
        """
        products_no_information_available should default to False
        """
        assessment = AssessmentFactory.create()
        self.assertFalse(assessment.products_no_information_available)

    def test_products_completed_default_false(self):
        """
        products_completed should default to False
        """
        assessment = AssessmentFactory.create()
        self.assertFalse(assessment.products_completed)

    def test_products_no_information_available_patchable(self):
        """
        products_no_information_available should be patchable
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {"products_no_information_available": True}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        assessment.refresh_from_db()
        self.assertTrue(assessment.products_no_information_available)

    def test_products_completed_default_patchable(self):
        """
        products_completed should be patchable
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {"products_completed": True}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        assessment.refresh_from_db()
        self.assertTrue(assessment.products_completed)

    def test_assessment_completion_and_no_information_default_false(self):
        """
        agent_income_completed
        agent_income_no_information_available
        agent_produce_sold_completed
        agent_produce_sold_no_information_available
        balance_sheet_tab_accepted
        bank_accounts_completed
        bank_accounts_no_information_available
        basic_financial_infos_completed
        basic_financial_infos_no_information_available
        basic_profit_loss_completed
        basic_profit_loss_no_information_available
        capital_requirements_completed
        capital_requirements_no_information_available
        cash_flow_tab_accepted
        collateral_assets_completed
        collateral_assets_no_information_available
        enabling_players_completed
        enabling_players_no_information_available
        executive_set_completed
        executive_set_no_information_available
        finance_performance_tab_accepted
        governance_structure_completed
        governance_structure_no_information_available
        financial_strategy_completed
        financial_strategy_no_information_available
        grant_histories_completed
        grant_histories_no_information_available
        insurances_completed
        insurances_no_information_available
        loan_applications_completed
        loan_applications_no_information_available
        loan_histories_completed
        loan_histories_no_information_available
        pre_finance_histories_completed
        pre_finance_histories_no_information_available
        loan_requirements_completed
        loan_requirements_no_information_available
        nonexecutive_set_completed
        nonexecutive_set_no_information_available
        productionmargins_completed
        productionmargins_no_information_available
        profit_loss_tab_accepted
        shareholders_completed
        shareholders_no_information_available
        monthlyincomeprojection_set_completed
        monthlyincomeprojection_set_no_information_available
        monthlyexpensesprojection_set_completed
        monthlyexpensesprojection_set_no_information_available
        value_chain_players_completed
        value_chain_players_no_information_available
        assessment_tab_completed
        organizational_tab_completed
        agent_tab_completed
        value_chain_tab_completed
        finance_history_tab_completed
        production_tab_completed
        finance_product_tab_completed
        profit_loss_tab_completed
        balance_sheet_tab_completed
        cash_flow_tab_completed
        finance_performance_tab_completed
        observations_tab_completed
        documents_tab_completed
        finance_overview_tab_completed
        data_sharing_consent_tab_completed
        """
        assessment = AssessmentFactory.create()
        self.assertFalse(assessment.agent_income_completed)
        self.assertFalse(assessment.agent_income_no_information_available)
        self.assertFalse(assessment.agent_produce_sold_completed)
        self.assertFalse(assessment.agent_produce_sold_no_information_available)
        self.assertTrue(assessment.balance_sheet_tab_accepted)
        self.assertFalse(assessment.bank_accounts_completed)
        self.assertFalse(assessment.bank_accounts_no_information_available)
        self.assertFalse(assessment.basic_financial_infos_completed)
        self.assertFalse(assessment.basic_financial_infos_no_information_available)
        self.assertFalse(assessment.basic_profit_loss_completed)
        self.assertFalse(assessment.basic_profit_loss_no_information_available)
        self.assertTrue(assessment.cash_flow_tab_accepted)
        self.assertFalse(assessment.capital_requirements_completed)
        self.assertFalse(assessment.capital_requirements_no_information_available)
        self.assertFalse(assessment.collateral_assets_completed)
        self.assertFalse(assessment.collateral_assets_no_information_available)
        self.assertFalse(assessment.enabling_players_completed)
        self.assertFalse(assessment.enabling_players_no_information_available)
        self.assertFalse(assessment.executive_set_completed)
        self.assertFalse(assessment.executive_set_no_information_available)
        self.assertTrue(assessment.finance_performance_tab_accepted)
        self.assertFalse(assessment.governance_structure_completed)
        self.assertFalse(assessment.governance_structure_no_information_available)
        self.assertFalse(assessment.financial_strategy_completed)
        self.assertFalse(assessment.financial_strategy_no_information_available)
        self.assertFalse(assessment.grant_histories_completed)
        self.assertFalse(assessment.grant_histories_no_information_available)
        self.assertFalse(assessment.insurances_completed)
        self.assertFalse(assessment.insurances_no_information_available)
        self.assertFalse(assessment.loan_applications_completed)
        self.assertFalse(assessment.loan_applications_no_information_available)
        self.assertFalse(assessment.loan_histories_completed)
        self.assertFalse(assessment.loan_histories_no_information_available)
        self.assertFalse(assessment.pre_finance_histories_completed)
        self.assertFalse(assessment.pre_finance_histories_no_information_available)
        self.assertFalse(assessment.loan_requirements_completed)
        self.assertFalse(assessment.loan_requirements_no_information_available)
        self.assertFalse(assessment.nonexecutive_set_completed)
        self.assertFalse(assessment.nonexecutive_set_no_information_available)
        self.assertFalse(assessment.productionmargins_completed)
        self.assertFalse(assessment.productionmargins_no_information_available)
        self.assertTrue(assessment.profit_loss_tab_accepted)
        self.assertFalse(assessment.shareholders_completed)
        self.assertFalse(assessment.shareholders_no_information_available)
        self.assertFalse(assessment.monthlyincomeprojection_set_completed)
        self.assertFalse(
            assessment.monthlyincomeprojection_set_no_information_available
        )
        self.assertFalse(assessment.monthlyexpensesprojection_set_completed)
        self.assertFalse(
            assessment.monthlyexpensesprojection_set_no_information_available
        )
        self.assertFalse(assessment.value_chain_players_completed)
        self.assertFalse(assessment.value_chain_players_no_information_available)
        self.assertFalse(assessment.assessment_tab_completed)
        self.assertFalse(assessment.organizational_tab_completed)
        self.assertFalse(assessment.agent_tab_completed)
        self.assertFalse(assessment.value_chain_tab_completed)
        self.assertFalse(assessment.finance_history_tab_completed)
        self.assertFalse(assessment.production_tab_completed)
        self.assertFalse(assessment.finance_product_tab_completed)
        self.assertFalse(assessment.profit_loss_tab_completed)
        self.assertFalse(assessment.balance_sheet_tab_completed)
        self.assertFalse(assessment.cash_flow_tab_completed)
        self.assertFalse(assessment.finance_performance_tab_completed)
        self.assertFalse(assessment.observations_tab_completed)
        self.assertFalse(assessment.documents_tab_completed)
        self.assertFalse(assessment.finance_overview_tab_completed)
        self.assertFalse(assessment.data_sharing_consent_tab_completed)

    def test_assessment_completion_and_no_information_booleans_patchable(self):
        """
        agent_income_completed
        agent_income_no_information_available
        agent_produce_sold_completed
        agent_produce_sold_no_information_available
        balance_sheet_tab_accepted
        bank_accounts_completed
        bank_accounts_no_information_available
        basic_financial_infos_completed
        basic_financial_infos_no_information_available
        basic_profit_loss_completed
        basic_profit_loss_no_information_available
        capital_requirements_completed
        capital_requirements_no_information_available
        cash_flow_tab_accepted
        collateral_assets_completed
        collateral_assets_no_information_available
        enabling_players_completed
        enabling_players_no_information_available
        executive_set_completed
        executive_set_no_information_available
        finance_performance_tab_accepted
        governance_structure_completed
        governance_structure_no_information_available
        financial_strategy_completed
        financial_strategy_no_information_available
        grant_histories_completed
        grant_histories_no_information_available
        insurances_completed
        insurances_no_information_available
        loan_applications_completed
        loan_applications_no_information_available
        loan_histories_completed
        loan_histories_no_information_available
        pre_finance_histories_completed
        pre_finance_histories_no_information_available
        loan_requirements_completed
        loan_requirements_no_information_available
        nonexecutive_set_completed
        nonexecutive_set_no_information_available
        productionmargins_completed
        productionmargins_no_information_available
        profit_loss_tab_accepted
        shareholders_completed
        shareholders_no_information_available
        monthlyincomeprojection_set_completed
        monthlyincomeprojection_set_no_information_available
        monthlyexpensesprojection_set_completed
        monthlyexpensesprojection_set_no_information_available
        value_chain_players_completed
        value_chain_players_no_information_available
        assessment_tab_completed
        organizational_tab_completed
        agent_tab_completed
        value_chain_tab_completed
        finance_history_tab_completed
        production_tab_completed
        finance_product_tab_completed
        profit_loss_tab_completed
        balance_sheet_tab_completed
        cash_flow_tab_completed
        finance_performance_tab_completed
        observations_tab_completed
        documents_tab_completed
        finance_overview_tab_completed
        data_sharing_consent_tab_completed
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {
            "agent_income_completed": True,
            "agent_income_no_information_available": True,
            "agent_produce_sold_completed": True,
            "agent_produce_sold_no_information_available": True,
            "balance_sheet_tab_accepted": True,
            "bank_accounts_completed": True,
            "bank_accounts_no_information_available": True,
            "basic_financial_infos_completed": True,
            "basic_financial_infos_no_information_available": True,
            "basic_profit_loss_completed": True,
            "basic_profit_loss_no_information_available": True,
            "capital_requirements_completed": True,
            "capital_requirements_no_information_available": True,
            "cash_flow_tab_accepted": True,
            "collateral_assets_completed": True,
            "collateral_assets_no_information_available": True,
            "enabling_players_completed": True,
            "enabling_players_no_information_available": True,
            "executive_set_completed": True,
            "executive_set_no_information_available": True,
            "finance_performance_tab_accepted": True,
            "governance_structure_completed": True,
            "governance_structure_no_information_available": True,
            "financial_strategy_completed": True,
            "financial_strategy_no_information_available": True,
            "grant_histories_completed": True,
            "grant_histories_no_information_available": True,
            "insurances_completed": True,
            "insurances_no_information_available": True,
            "loan_applications_completed": True,
            "loan_applications_no_information_available": True,
            "loan_histories_completed": True,
            "loan_histories_no_information_available": True,
            "pre_finance_histories_completed": True,
            "pre_finance_histories_no_information_available": True,
            "loan_requirements_completed": True,
            "loan_requirements_no_information_available": True,
            "nonexecutive_set_completed": True,
            "nonexecutive_set_no_information_available": True,
            "productionmargins_completed": True,
            "productionmargins_no_information_available": True,
            "profit_loss_tab_accepted": True,
            "shareholders_completed": True,
            "shareholders_no_information_available": True,
            "monthlyincomeprojection_set_completed": True,
            "monthlyincomeprojection_set_no_information_available": True,
            "monthlyexpensesprojection_set_completed": True,
            "monthlyexpensesprojection_set_no_information_available": True,
            "value_chain_players_completed": True,
            "value_chain_players_no_information_available": True,
            "assessment_tab_completed": True,
            "organizational_tab_completed": True,
            "agent_tab_completed": True,
            "value_chain_tab_completed": True,
            "finance_history_tab_completed": True,
            "production_tab_completed": True,
            "finance_product_tab_completed": True,
            "profit_loss_tab_completed": True,
            "balance_sheet_tab_completed": True,
            "cash_flow_tab_completed": True,
            "finance_performance_tab_completed": True,
            "observations_tab_completed": True,
            "documents_tab_completed": True,
            "finance_overview_tab_completed": True,
            "data_sharing_consent_tab_completed": True,
        }
        self.client.patch(url, json.dumps(data), content_type="application/json")
        assessment.refresh_from_db()
        self.assertTrue(assessment.agent_income_completed)
        self.assertTrue(assessment.agent_income_no_information_available)
        self.assertTrue(assessment.agent_produce_sold_completed)
        self.assertTrue(assessment.agent_produce_sold_no_information_available)
        self.assertTrue(assessment.balance_sheet_tab_accepted)
        self.assertTrue(assessment.bank_accounts_completed)
        self.assertTrue(assessment.bank_accounts_no_information_available)
        self.assertTrue(assessment.basic_financial_infos_completed)
        self.assertTrue(assessment.basic_financial_infos_no_information_available)
        self.assertTrue(assessment.basic_profit_loss_completed)
        self.assertTrue(assessment.basic_profit_loss_no_information_available)
        self.assertTrue(assessment.capital_requirements_completed)
        self.assertTrue(assessment.capital_requirements_no_information_available)
        self.assertTrue(assessment.cash_flow_tab_accepted)
        self.assertTrue(assessment.collateral_assets_completed)
        self.assertTrue(assessment.collateral_assets_no_information_available)
        self.assertTrue(assessment.enabling_players_completed)
        self.assertTrue(assessment.enabling_players_no_information_available)
        self.assertTrue(assessment.executive_set_completed)
        self.assertTrue(assessment.executive_set_no_information_available)
        self.assertTrue(assessment.finance_performance_tab_accepted)
        self.assertTrue(assessment.governance_structure_completed)
        self.assertTrue(assessment.governance_structure_no_information_available)
        self.assertTrue(assessment.financial_strategy_completed)
        self.assertTrue(assessment.financial_strategy_no_information_available)
        self.assertTrue(assessment.grant_histories_completed)
        self.assertTrue(assessment.grant_histories_no_information_available)
        self.assertTrue(assessment.insurances_completed)
        self.assertTrue(assessment.insurances_no_information_available)
        self.assertTrue(assessment.loan_applications_completed)
        self.assertTrue(assessment.loan_applications_no_information_available)
        self.assertTrue(assessment.loan_histories_completed)
        self.assertTrue(assessment.loan_histories_no_information_available)
        self.assertTrue(assessment.pre_finance_histories_completed)
        self.assertTrue(assessment.pre_finance_histories_no_information_available)
        self.assertTrue(assessment.loan_requirements_completed)
        self.assertTrue(assessment.loan_requirements_no_information_available)
        self.assertTrue(assessment.nonexecutive_set_completed)
        self.assertTrue(assessment.nonexecutive_set_no_information_available)
        self.assertTrue(assessment.productionmargins_completed)
        self.assertTrue(assessment.productionmargins_no_information_available)
        self.assertTrue(assessment.profit_loss_tab_accepted)
        self.assertTrue(assessment.shareholders_completed)
        self.assertTrue(assessment.shareholders_no_information_available)
        self.assertTrue(assessment.monthlyincomeprojection_set_completed)
        self.assertTrue(assessment.monthlyincomeprojection_set_no_information_available)
        self.assertTrue(assessment.monthlyexpensesprojection_set_completed)
        self.assertTrue(
            assessment.monthlyexpensesprojection_set_no_information_available
        )
        self.assertTrue(assessment.value_chain_players_completed)
        self.assertTrue(assessment.value_chain_players_no_information_available)
        self.assertTrue(assessment.assessment_tab_completed)
        self.assertTrue(assessment.organizational_tab_completed)
        self.assertTrue(assessment.agent_tab_completed)
        self.assertTrue(assessment.value_chain_tab_completed)
        self.assertTrue(assessment.finance_history_tab_completed)
        self.assertTrue(assessment.production_tab_completed)
        self.assertTrue(assessment.finance_product_tab_completed)
        self.assertTrue(assessment.profit_loss_tab_completed)
        self.assertTrue(assessment.balance_sheet_tab_completed)
        self.assertTrue(assessment.cash_flow_tab_completed)
        self.assertTrue(assessment.finance_performance_tab_completed)
        self.assertTrue(assessment.observations_tab_completed)
        self.assertTrue(assessment.documents_tab_completed)
        self.assertTrue(assessment.finance_overview_tab_completed)
        self.assertTrue(assessment.data_sharing_consent_tab_completed)

    def test_assessment_completion_and_no_information_booleans_patchable_financial(
        self,
    ):
        """
        agent_income_completed
        agent_income_no_information_available
        agent_produce_sold_completed
        agent_produce_sold_no_information_available
        balance_sheet_tab_accepted
        bank_accounts_completed
        bank_accounts_no_information_available
        basic_financial_infos_completed
        basic_financial_infos_no_information_available
        basic_profit_loss_completed
        basic_profit_loss_no_information_available
        capital_requirements_completed
        capital_requirements_no_information_available
        cash_flow_tab_accepted
        collateral_assets_completed
        collateral_assets_no_information_available
        enabling_players_completed
        enabling_players_no_information_available
        executive_set_completed
        executive_set_no_information_available
        finance_performance_tab_accepted
        governance_structure_completed
        governance_structure_no_information_available
        financial_strategy_completed
        financial_strategy_no_information_available
        grant_histories_completed
        grant_histories_no_information_available
        insurances_completed
        insurances_no_information_available
        loan_applications_completed
        loan_applications_no_information_available
        loan_histories_completed
        loan_histories_no_information_available
        pre_finance_histories_completed
        pre_finance_histories_no_information_available
        loan_requirements_completed
        loan_requirements_no_information_available
        nonexecutive_set_completed
        nonexecutive_set_no_information_available
        productionmargins_completed
        productionmargins_no_information_available
        profit_loss_tab_accepted
        shareholders_completed
        shareholders_no_information_available
        monthlyincomeprojection_set_completed
        monthlyincomeprojection_set_no_information_available
        monthlyexpensesprojection_set_completed
        monthlyexpensesprojection_set_no_information_available
        value_chain_players_completed
        value_chain_players_no_information_available
        assessment_tab_completed
        organizational_tab_completed
        agent_tab_completed
        value_chain_tab_completed
        finance_history_tab_completed
        production_tab_completed
        finance_product_tab_completed
        profit_loss_tab_completed
        balance_sheet_tab_completed
        cash_flow_tab_completed
        finance_performance_tab_completed
        observations_tab_completed
        documents_tab_completed
        finance_overview_tab_completed
        data_sharing_consent_tab_completed
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor,
            assessmentassignments__assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {
            "agent_income_completed": True,
            "agent_income_no_information_available": True,
            "agent_produce_sold_completed": True,
            "agent_produce_sold_no_information_available": True,
            "balance_sheet_tab_accepted": True,
            "bank_accounts_completed": True,
            "bank_accounts_no_information_available": True,
            "basic_financial_infos_completed": True,
            "basic_financial_infos_no_information_available": True,
            "basic_profit_loss_completed": True,
            "basic_profit_loss_no_information_available": True,
            "capital_requirements_completed": True,
            "capital_requirements_no_information_available": True,
            "cash_flow_tab_accepted": True,
            "collateral_assets_completed": True,
            "collateral_assets_no_information_available": True,
            "enabling_players_completed": True,
            "enabling_players_no_information_available": True,
            "executive_set_completed": True,
            "executive_set_no_information_available": True,
            "finance_performance_tab_accepted": True,
            "governance_structure_completed": True,
            "governance_structure_no_information_available": True,
            "financial_strategy_completed": True,
            "financial_strategy_no_information_available": True,
            "grant_histories_completed": True,
            "grant_histories_no_information_available": True,
            "insurances_completed": True,
            "insurances_no_information_available": True,
            "loan_applications_completed": True,
            "loan_applications_no_information_available": True,
            "loan_histories_completed": True,
            "loan_histories_no_information_available": True,
            "pre_finance_histories_completed": True,
            "pre_finance_histories_no_information_available": True,
            "loan_requirements_completed": True,
            "loan_requirements_no_information_available": True,
            "nonexecutive_set_completed": True,
            "nonexecutive_set_no_information_available": True,
            "productionmargins_completed": True,
            "productionmargins_no_information_available": True,
            "profit_loss_tab_accepted": True,
            "shareholders_completed": True,
            "shareholders_no_information_available": True,
            "monthlyincomeprojection_set_completed": True,
            "monthlyincomeprojection_set_no_information_available": True,
            "monthlyexpensesprojection_set_completed": True,
            "monthlyexpensesprojection_set_no_information_available": True,
            "value_chain_players_completed": True,
            "value_chain_players_no_information_available": True,
            "assessment_tab_completed": True,
            "organizational_tab_completed": True,
            "agent_tab_completed": True,
            "value_chain_tab_completed": True,
            "finance_history_tab_completed": True,
            "production_tab_completed": True,
            "finance_product_tab_completed": True,
            "profit_loss_tab_completed": True,
            "balance_sheet_tab_completed": True,
            "cash_flow_tab_completed": True,
            "finance_performance_tab_completed": True,
            "observations_tab_completed": True,
            "documents_tab_completed": True,
            "finance_overview_tab_completed": True,
            "data_sharing_consent_tab_completed": True,
        }
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(200, response.status_code)
        assessment.refresh_from_db()
        self.assertTrue(assessment.agent_income_completed)
        self.assertTrue(assessment.agent_income_no_information_available)
        self.assertTrue(assessment.agent_produce_sold_completed)
        self.assertTrue(assessment.agent_produce_sold_no_information_available)
        self.assertTrue(assessment.balance_sheet_tab_accepted)
        self.assertTrue(assessment.bank_accounts_completed)
        self.assertTrue(assessment.bank_accounts_no_information_available)
        self.assertTrue(assessment.basic_financial_infos_completed)
        self.assertTrue(assessment.basic_financial_infos_no_information_available)
        self.assertTrue(assessment.basic_profit_loss_completed)
        self.assertTrue(assessment.basic_profit_loss_no_information_available)
        self.assertTrue(assessment.capital_requirements_completed)
        self.assertTrue(assessment.capital_requirements_no_information_available)
        self.assertTrue(assessment.cash_flow_tab_accepted)
        self.assertTrue(assessment.collateral_assets_completed)
        self.assertTrue(assessment.collateral_assets_no_information_available)
        self.assertTrue(assessment.enabling_players_completed)
        self.assertTrue(assessment.enabling_players_no_information_available)
        self.assertTrue(assessment.executive_set_completed)
        self.assertTrue(assessment.executive_set_no_information_available)
        self.assertTrue(assessment.finance_performance_tab_accepted)
        self.assertTrue(assessment.governance_structure_completed)
        self.assertTrue(assessment.governance_structure_no_information_available)
        self.assertTrue(assessment.financial_strategy_completed)
        self.assertTrue(assessment.financial_strategy_no_information_available)
        self.assertTrue(assessment.grant_histories_completed)
        self.assertTrue(assessment.grant_histories_no_information_available)
        self.assertTrue(assessment.insurances_completed)
        self.assertTrue(assessment.insurances_no_information_available)
        self.assertTrue(assessment.loan_applications_completed)
        self.assertTrue(assessment.loan_applications_no_information_available)
        self.assertTrue(assessment.loan_histories_completed)
        self.assertTrue(assessment.loan_histories_no_information_available)
        self.assertTrue(assessment.pre_finance_histories_completed)
        self.assertTrue(assessment.pre_finance_histories_no_information_available)
        self.assertTrue(assessment.loan_requirements_completed)
        self.assertTrue(assessment.loan_requirements_no_information_available)
        self.assertTrue(assessment.nonexecutive_set_completed)
        self.assertTrue(assessment.nonexecutive_set_no_information_available)
        self.assertTrue(assessment.productionmargins_completed)
        self.assertTrue(assessment.productionmargins_no_information_available)
        self.assertTrue(assessment.profit_loss_tab_accepted)
        self.assertTrue(assessment.shareholders_completed)
        self.assertTrue(assessment.shareholders_no_information_available)
        self.assertTrue(assessment.monthlyincomeprojection_set_completed)
        self.assertTrue(assessment.monthlyincomeprojection_set_no_information_available)
        self.assertTrue(assessment.monthlyexpensesprojection_set_completed)
        self.assertTrue(
            assessment.monthlyexpensesprojection_set_no_information_available
        )
        self.assertTrue(assessment.value_chain_players_completed)
        self.assertTrue(assessment.value_chain_players_no_information_available)
        self.assertTrue(assessment.assessment_tab_completed)
        self.assertTrue(assessment.organizational_tab_completed)
        self.assertTrue(assessment.agent_tab_completed)
        self.assertTrue(assessment.value_chain_tab_completed)
        self.assertTrue(assessment.finance_history_tab_completed)
        self.assertTrue(assessment.production_tab_completed)
        self.assertTrue(assessment.finance_product_tab_completed)
        self.assertTrue(assessment.profit_loss_tab_completed)
        self.assertTrue(assessment.balance_sheet_tab_completed)
        self.assertTrue(assessment.cash_flow_tab_completed)
        self.assertTrue(assessment.finance_performance_tab_completed)
        self.assertTrue(assessment.observations_tab_completed)
        self.assertTrue(assessment.documents_tab_completed)
        self.assertTrue(assessment.finance_overview_tab_completed)
        self.assertTrue(assessment.data_sharing_consent_tab_completed)

    def test_assessment_get_has_reasonable_amount_of_queries(self):
        """
        Assessment GET should not have an insane amount of queries
        """
        tool = ToolFactory.create()
        sectors = SectorFactory.create_batch(3)
        products = ["product {}".format(i) for i in range(3)]
        for i in range(3):
            section = SectionFactory.create(tool=tool, title="section {}".format(i))
            for j in range(3):
                subsection = SectionFactory.create(
                    parent=section, title="subsection {}.{}".format(i, j)
                )
                for k in range(3):
                    subsubsection = SectionFactory.create(
                        parent=subsection, title="subsection {}.{}.{}".format(i, j, k)
                    )
                    for l in range(3):
                        QuestionFactory.create(
                            section=subsubsection,
                            title="question {}.{}.{}.{}".format(i, j, k, l),
                        )
                for k in range(3):
                    QuestionFactory.create(
                        section=subsection, title="question {}.{}.{}".format(i, j, k)
                    )
        assessment = AssessmentFactory.create(
            tool=tool,
            producing_organization__sectors=sectors,
            products=products,
            assessmentassignments__assessor=self.assessor,
            build_response_tree_completed=False,
        )
        build_response_tree_task(assessment.pk)
        LoanHistoryFactory.create_batch(2, assessment=assessment)
        PreFinanceHistoryFactory.create_batch(2, assessment=assessment)
        LoanRequirementFactory.create_batch(2, assessment=assessment)
        GrantHistoryFactory.create_batch(2, assessment=assessment)
        BankAccountFactory.create_batch(2, assessment=assessment)
        BasicFinancialInfoFactory.create_batch(2, assessment=assessment)
        flush()
        url = reverse("assessments:assessment-detail", [assessment.pk])
        with self.assertNumQueries(152):
            response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code, response.content)

    def test_assessment_get_serializer_data_no_queries(self):
        """
        Assessment GET should not have an insane amount of queries
        """
        tool = ToolFactory.create()
        sectors = SectorFactory.create_batch(3)
        products = ["product {}".format(i) for i in range(3)]
        for i in range(3):
            section = SectionFactory.create(tool=tool, title="section {}".format(i))
            for j in range(3):
                subsection = SectionFactory.create(
                    parent=section, title="subsection {}.{}".format(i, j)
                )
                for k in range(3):
                    subsubsection = SectionFactory.create(
                        parent=subsection, title="subsection {}.{}.{}".format(i, j, k)
                    )
                    for l in range(3):
                        QuestionFactory.create(
                            section=subsubsection,
                            title="question {}.{}.{}.{}".format(i, j, k, l),
                        )
                for k in range(3):
                    QuestionFactory.create(
                        section=subsection, title="question {}.{}.{}".format(i, j, k)
                    )
        assessment = AssessmentFactory.create(
            tool=tool,
            producing_organization__sectors=sectors,
            products=products,
            assessmentassignments__assessor=self.assessor,
            build_response_tree_completed=False,
        )
        build_response_tree_task(assessment.pk)
        LoanHistoryFactory.create_batch(2, assessment=assessment)
        PreFinanceHistoryFactory.create_batch(2, assessment=assessment)
        LoanRequirementFactory.create_batch(2, assessment=assessment)
        GrantHistoryFactory.create_batch(2, assessment=assessment)
        BankAccountFactory.create_batch(2, assessment=assessment)
        BasicFinancialInfoFactory.create_batch(2, assessment=assessment)
        flush()
        url = reverse("assessments:assessment-detail", [assessment.pk])

        def get_serializer(slf, instance):
            serializer = super(AssessmentViewSet, slf).get_serializer(instance)
            with self.assertNumQueries(48):  # FIXME: Should be 0
                serializer.data
            return serializer

        with patch.object(
            AssessmentViewSet, "get_serializer", autospec=True
        ) as mocked_method:
            mocked_method.side_effect = get_serializer
            response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code, response.content)

    def test_assessment_second_get_has_reasonable_amount_of_queries(self):
        """
        Assessment second GET should not have an insane amount of queries
        """
        create_time = datetime.datetime.now(pytz.utc)
        modified_since_time = create_time + datetime.timedelta(seconds=1)
        fetch_time = create_time + datetime.timedelta(seconds=2)
        with freeze_time(create_time):
            tool = ToolFactory.create()
            sectors = SectorFactory.create_batch(3)
            products = ["product {}".format(i) for i in range(3)]
            for i in range(3):
                section = SectionFactory.create(tool=tool, title="section {}".format(i))
                for j in range(3):
                    subsection = SectionFactory.create(
                        parent=section, title="subsection {}.{}".format(i, j)
                    )
                    for k in range(3):
                        subsubsection = SectionFactory.create(
                            parent=subsection,
                            title="subsection {}.{}.{}".format(i, j, k),
                        )
                        for l in range(3):
                            QuestionFactory.create(
                                section=subsubsection,
                                title="question {}.{}.{}.{}".format(i, j, k, l),
                            )
                    for k in range(3):
                        QuestionFactory.create(
                            section=subsection,
                            title="question {}.{}.{}".format(i, j, k),
                        )
            assessment = AssessmentFactory.create(
                tool=tool,
                producing_organization__sectors=sectors,
                products=products,
                assessmentassignments__assessor=self.assessor,
                build_response_tree_completed=False,
            )
            build_response_tree_task(assessment.pk)
            LoanHistoryFactory.create_batch(2, assessment=assessment)
            PreFinanceHistoryFactory.create_batch(2, assessment=assessment)
            LoanRequirementFactory.create_batch(2, assessment=assessment)
            GrantHistoryFactory.create_batch(2, assessment=assessment)
            BankAccountFactory.create_batch(2, assessment=assessment)
            BasicFinancialInfoFactory.create_batch(2, assessment=assessment)
            flush()
        with freeze_time(modified_since_time):
            modified_since = datetime.datetime.now(pytz.utc).strftime(
                "%a, %d %b %Y %H:%M:%S GMT"
            )
        with freeze_time(fetch_time):
            url = reverse("assessments:assessment-detail", [assessment.pk])
            with self.assertNumQueries(153):
                long_ago = datetime.datetime(2000, 1, 1, 1, 1, 1, 1, pytz.utc)
                with self.settings(LAST_BREAKING_CODE_CHANGE=long_ago):
                    response = self.client.get(
                        url,
                        content_type="application/json",
                        HTTP_IF_MODIFIED_SINCE=modified_since,
                    )
            self.assertEqual(status.HTTP_304_NOT_MODIFIED, response.status_code)

    def test_assessment_patch_has_reasonable_amount_of_queries(self):
        """
        Assessment PATCH should not have an insane amount of queries
        """
        tool = ToolFactory.create()
        sectors = SectorFactory.create_batch(3)
        products = ["product {}".format(i) for i in range(3)]
        for i in range(3):
            section = SectionFactory.create(tool=tool, title="section {}".format(i))
            for j in range(3):
                subsection = SectionFactory.create(
                    parent=section, title="subsection {}.{}".format(i, j)
                )
                for k in range(3):
                    subsubsection = SectionFactory.create(
                        parent=subsection, title="subsection {}.{}.{}".format(i, j, k)
                    )
                    for l in range(3):
                        QuestionFactory.create(
                            section=subsubsection,
                            title="question {}.{}.{}.{}".format(i, j, k, l),
                        )
                for k in range(3):
                    QuestionFactory.create(
                        section=subsection, title="question {}.{}.{}".format(i, j, k)
                    )
        assessment = AssessmentFactory.create(
            tool=tool,
            producing_organization__sectors=sectors,
            products=products,
            assessmentassignments__assessor=self.assessor,
            build_response_tree_completed=False,
        )
        build_response_tree_task(assessment.pk)
        LoanHistoryFactory.create_batch(random.randint(1, 3), assessment=assessment)
        PreFinanceHistoryFactory.create_batch(
            random.randint(1, 3), assessment=assessment
        )
        LoanRequirementFactory.create_batch(random.randint(1, 3), assessment=assessment)
        GrantHistoryFactory.create_batch(random.randint(1, 3), assessment=assessment)
        BankAccountFactory.create_batch(random.randint(1, 3), assessment=assessment)
        BasicFinancialInfoFactory.create_batch(
            random.randint(1, 3), assessment=assessment
        )
        flush()
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {"products_completed": True}
        with self.assertNumQueries(59):
            response = self.client.patch(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(status.HTTP_200_OK, response.status_code, response.content)

    def test_with_management_body_defaults_to_false(self):
        """
        with_management_body should default to False
        """
        assessment = AssessmentFactory.create()
        self.assertFalse(assessment.with_management_body)

    def test_with_management_body_visible_in_api(self):
        """
        with_management_body should be visible in the api
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("with_management_body", response_dict)

    def test_with_management_body_not_writable(self):
        """
        with_management_body should not be writable
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {"with_management_body": False}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        assessment = Assessment.objects.get(pk=assessment.pk)
        self.assertFalse(assessment.with_management_body)

    def test_with_margins_raw_materials_defaults_to_false(self):
        """
        with_margins_raw_materials should default to False
        """
        assessment = AssessmentFactory.create()
        self.assertFalse(assessment.with_margins_raw_materials)

    def test_with_margins_raw_materials_visible_in_api(self):
        """
        with_margins_raw_materials should be visible in the api
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("with_margins_raw_materials", response_dict)

    def test_with_margins_raw_materials_not_writable(self):
        """
        with_margins_raw_materials should not be writable
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {"with_margins_raw_materials": False}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        assessment = Assessment.objects.get(pk=assessment.pk)
        self.assertFalse(assessment.with_margins_raw_materials)

    def test_with_general_checks_defaults_to_false(self):
        """
        with_general_checks should default to False
        """
        assessment = AssessmentFactory.create()
        self.assertFalse(assessment.with_general_checks)

    def test_with_general_checks_visible_in_api(self):
        """
        with_general_checks should be visible in the api
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("with_general_checks", response_dict)

    def test_with_general_checks_not_writable(self):
        """
        with_general_checks should not be writable
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {"with_general_checks": False}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        assessment = Assessment.objects.get(pk=assessment.pk)
        self.assertFalse(assessment.with_general_checks)

    def test_submitting_uses_minimal_number_of_queries(self):
        """
        Submitting an assessment should take a minimum amount of queries, for
        speed
        """
        assessment = AssessmentFactory.create()
        assignment = AssessmentAssignmentFactory.create(
            assessment=assessment,
            assessor=self.assessor,
            locked_for_assessor=False,
            locked_for_employee=True,
        )
        flush()
        url = reverse("assessments:assessmentassignment-detail", [assignment.pk])
        data = {"locked_for_assessor": True, "locked_for_employee": False}
        with self.assertNumQueries(87):  # high due to assessmentlog
            response = self.client.patch(
                url, json.dumps(data), content_type="application/json"
            )
        self.assertEqual(status.HTTP_200_OK, response.status_code, response.content)

    def _create_assessments_for_final_filter(self):
        """
        Helper function to create group of assessments for final filter tests
        """
        # in progress
        assessment = AssessmentFactory.create()
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            assessor=self.assessor,
            invitation__assessor=self.assessor,
        )
        # qc quality review
        assessment1 = AssessmentFactory.create()
        AssessmentAssignmentFactory.create(
            assessment=assessment1,
            invitation__assessment=assessment1,
            assessor=self.assessor,
            invitation__assessor=self.assessor,
            submitted_at_least_once=True,
            locked_for_employee=False,
            locked_for_assessor=True,
        )
        # qc assessor
        assessment2 = AssessmentFactory.create()
        AssessmentAssignmentFactory.create(
            assessment=assessment2,
            invitation__assessment=assessment2,
            assessor=self.assessor,
            invitation__assessor=self.assessor,
            submitted_at_least_once=True,
            locked_for_employee=True,
            locked_for_assessor=False,
        )
        # draft
        assessment3 = AssessmentFactory.create()
        AssessmentAssignmentFactory.create(
            assessment=assessment3,
            invitation__assessment=assessment3,
            assessor=self.assessor,
            invitation__assessor=self.assessor,
        )
        ReportFactory.create(assessment=assessment, status=Report.STATUS_DRAFT)
        # final
        assessment4 = AssessmentFactory.create()
        AssessmentAssignmentFactory.create(
            assessment=assessment4,
            invitation__assessment=assessment4,
            assessor=self.assessor,
            invitation__assessor=self.assessor,
        )
        ReportFactory.create(assessment=assessment4, status=Report.STATUS_FINAL)
        flush()
        self.assertEqual(5, Assessment.objects.count())

    def test_final_filter(self):
        """
        final: 0 should return non-final stuff
        final: 1 should return final stuff
        """
        self._create_assessments_for_final_filter()
        url = reverse("assessments:assessment-list")
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual(5, response_dict["count"])
        url = reverse("assessments:assessment-list") + "?final=0"
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual(4, response_dict["count"])
        url = reverse("assessments:assessment-list") + "?final=1"
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual(1, response_dict["count"])

    def test_final_filter_modifies_metadata(self):
        """
        final: 0 should not show final as an option in status filter metadata
        final: 1 should only show final as an option in status filter metadata
        """
        self._create_assessments_for_final_filter()
        url = reverse("assessments:assessment-list")
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual(5, len(response_dict["filters"]["statuses"]))
        url = reverse("assessments:assessment-list") + "?final=0"
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual(4, len(response_dict["filters"]["statuses"]))
        url = reverse("assessments:assessment-list") + "?final=1"
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual(1, len(response_dict["filters"]["statuses"]))

    def test_build_response_tree_task_flips_boolean(self):
        """
        build_response_tree_task needs to flip the
        build_response_tree_completed boolean
        """
        assessment = AssessmentFactory.create(build_response_tree_completed=False)
        self.assertFalse(assessment.build_response_tree_completed)
        build_response_tree_task(assessment.id)
        assessment.refresh_from_db()
        self.assertTrue(assessment.build_response_tree_completed)

    def test_terms_and_conditions_nested_in_detail(self):
        """
        terms_and_conditions should be nested in the detail get
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        with freeze_time("2018-01-01"):
            tac = TermsAndConditionsFactory.create(assessment=assessment)
        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("terms_and_conditions", response_dict)
        self.assertDictEqual(
            {
                "objects": [
                    {
                        "url": "http://testserver/assessments/terms_and_conditions/{}/".format(
                            tac.id
                        ),
                        "file_name": "",
                        "modified_at": "2018-01-01T00:00:00Z",
                        "signed": False,
                        "file_type": None,
                        "file": None,
                        "file_size": None,
                        "assessment": "http://testserver/assessments/assessments/{}/".format(
                            assessment.id
                        ),
                        "id": tac.id,
                        "mime_type": None,
                    }
                ],
                "links": {
                    "create": "http://testserver/assessments/terms_and_conditions/"
                },
            },
            response_dict["terms_and_conditions"],
        )

    def test_supplier_product_types_purchased_in_detail(self):
        """
        supplier product types purchased should be nested in the detail get
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        supplier = SupplierFactory.create(assessment=assessment)
        with freeze_time("2018-01-01"):
            product = ProductTypeOptionFactory.create()
        supplier.product_types_purchased.add(product)
        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("suppliers", response_dict)
        self.assertIn("objects", response_dict["suppliers"])
        self.assertIn(
            "product_types_purchased", response_dict["suppliers"]["objects"][0]
        )
        self.assertDictEqual(
            {
                "objects": [
                    {
                        "url": "http://testserver/products/product_type_options/{}/".format(
                            product.id
                        ),
                        "id": product.id,
                        "display_name": product.name,
                        "modified_at": "2018-01-01T00:00:00Z",
                        "name": product.name,
                        "tool": "http://testserver"
                        + reverse("products:tool-detail", [product.tool_id]),
                        "position": product.position,
                    }
                ],
                "links": {"create": "http://testserver/products/product_type_options/"},
            },
            response_dict["suppliers"]["objects"][0]["product_types_purchased"],
        )

    def test_assignments_status_str_in_list_response(self):
        AssessmentFactory.create(assessmentassignments__assessor=self.assessor)
        url = reverse("assessments:assessment-list")
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("results", response_dict)
        self.assertEqual(1, len(response_dict["results"]))
        result = response_dict["results"][0]
        self.assertIn("assignments", result)
        self.assertEqual(1, len(result["assignments"]))
        assignment = result["assignments"][0]
        self.assertIn("status", assignment)
        self.assertEqual("in_progress", assignment["status"])

    def test_status_str_in_list_response(self):
        AssessmentFactory.create(assessmentassignments__assessor=self.assessor)
        url = reverse("assessments:assessment-list")
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("results", response_dict)
        self.assertEqual(1, len(response_dict["results"]))
        result = response_dict["results"][0]
        self.assertIn("status", result)
        self.assertEqual("in_progress", result["status"])

    def test_status_str_in_detail_response(self):
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("status", response_dict)
        self.assertEqual("in_progress", response_dict["status"])

    def test_balancesheets_not_duplicated_in_response(self):
        """
        When the same user is both financial and non-financial assessor, make
        sure the nested balancesheets are not duplicated in the api response
        """
        assessment = AssessmentFactory.create(build_response_tree_completed=False)
        build_response_tree_task(assessment.pk)
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            assessor=self.assessor,
            assigned_as=AssessmentAssignment.AS_ASSESSOR,
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            assessor=self.assessor,
            assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("balancesheets", response_dict)
        self.assertIn("objects", response_dict["balancesheets"])
        balancesheets = response_dict["balancesheets"]["objects"]
        balancesheet_ids = [item["id"] for item in balancesheets]
        self.assertEqual(len(balancesheet_ids), len(set(balancesheet_ids)))
