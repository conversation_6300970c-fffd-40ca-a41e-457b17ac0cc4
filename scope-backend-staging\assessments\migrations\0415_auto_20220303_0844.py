# Generated by Django 3.1.14 on 2022-03-03 07:44

from django.db import migrations


def convert_negative_numeric_answer_to_zero(apps, schema_editor):
    from assessments.models import SubResponse

    for subresponse in SubResponse.objects.filter(value__lt=0):
        subresponse.value = 0
        subresponse.save()


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0414_certification_percent_certified_crop"),
    ]

    operations = [migrations.RunPython(convert_negative_numeric_answer_to_zero)]
