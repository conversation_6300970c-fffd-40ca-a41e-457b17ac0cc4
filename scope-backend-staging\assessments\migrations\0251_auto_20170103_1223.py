# Generated by Django 1.10.4 on 2017-01-03 12:23


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0250_merge_20161229_1102")]

    operations = [
        migrations.AddField(
            model_name="assessment",
            name="agent_income_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="agent_income_no_information_available",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="agent_produce_sold_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="agent_produce_sold_no_information_available",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="balance_sheet_tab_accepted",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="cash_flow_tab_accepted",
            field=models.<PERSON>oleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="enabling_players_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="enabling_players_no_information_available",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="executive_set_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="executive_set_no_information_available",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="financial_performance_tab_accepted",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="governance_structure_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="governance_structure_no_information_available",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="monthly_production_tab_accepted",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="nonexecutive_set_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="nonexecutive_set_no_information_available",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="productionmargins_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="productionmargins_no_information_available",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="profit_loss_tab_accepted",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="value_chain_players_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="value_chain_players_no_information_available",
            field=models.BooleanField(default=False),
        ),
    ]
