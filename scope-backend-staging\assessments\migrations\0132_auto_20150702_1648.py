from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0131_auto_20150702_1011")]

    operations = [
        migrations.AlterModelOptions(
            name="monthlyexpensesprojection", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="monthlyincomeprojection", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="monthlyproduction", options={"ordering": ("pk",)}
        ),
        migrations.AddField(
            model_name="sectionresponse",
            name="_copy_score_and_weight_from",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                to="assessments.SectionResponse",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="product",
            name="name",
            field=models.TextField(blank=True),
            preserve_default=True,
        ),
    ]
