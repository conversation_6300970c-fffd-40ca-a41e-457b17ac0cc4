from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0109_auto_20150218_0832")]

    operations = [
        migrations.AlterField(
            model_name="balancesheet",
            name="assessor",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="balancesheets",
                blank=True,
                to="hrm.Assessor",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="cashflowstatement",
            name="assessor",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="cashflowstatements",
                blank=True,
                to="hrm.Assessor",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="assessor",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="financial_ratios",
                blank=True,
                to="hrm.Assessor",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="profitlossstatement",
            name="assessor",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="profitlossstatements",
                blank=True,
                to="hrm.Assessor",
                null=True,
            ),
            preserve_default=True,
        ),
    ]
