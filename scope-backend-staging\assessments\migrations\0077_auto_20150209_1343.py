from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0076_delete_producttype")]

    operations = [
        migrations.AddField(
            model_name="assessment",
            name="tool_name",
            field=models.TextField(editable=False, blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="assessment",
            name="tool_version_major",
            field=models.PositiveIntegerField(null=True, editable=False, blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="assessment",
            name="tool_version_minor",
            field=models.PositiveIntegerField(null=True, editable=False, blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="assessment",
            name="tool_version_revision",
            field=models.PositiveIntegerField(null=True, editable=False, blank=True),
            preserve_default=True,
        ),
    ]
