from django.db import migrations


def cleanup_uuid_emails(apps, schema_editor):
    User = apps.get_model("accounts", "User")
    users = User.objects.filter(
        email__regex=r"[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"
    )
    for user in users:
        email = "{}.{}@scopeimport.com".format(user.first_name, user.last_name).lower()
        if not User.objects.filter(email=email).exists():
            user.email = email
            user.save()
        else:
            i = 1
            while True:
                email = "{}.{}.{}@scopeimport.com".format(
                    user.first_name, user.last_name, i
                ).lower()
                if not User.objects.filter(email=email).exists():
                    user.email = email
                    user.save()
                    break
                else:
                    i += 1


class Migration(migrations.Migration):

    dependencies = [("accounts", "0010_auto_20150807_0920")]

    operations = [migrations.RunPython(cleanup_uuid_emails)]
