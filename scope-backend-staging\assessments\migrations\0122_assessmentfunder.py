from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0121_forestryequipment")]

    operations = [
        migrations.CreateModel(
            name="AssessmentFunder",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("name", models.TextField(blank=True)),
                (
                    "percentage",
                    models.DecimalField(
                        null=True, max_digits=5, decimal_places=2, blank=True
                    ),
                ),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="funders",
                        to="assessments.Assessment",
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        )
    ]
