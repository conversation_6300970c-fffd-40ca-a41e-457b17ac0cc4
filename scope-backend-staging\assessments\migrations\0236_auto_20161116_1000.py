# Generated by Django 1.10.3 on 2016-11-16 10:00


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0235_auto_20161111_1110")]

    operations = [
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_active_members",
            field=models.PositiveIntegerField(blank=True, editable=False, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_active_outgrowers",
            field=models.PositiveIntegerField(blank=True, editable=False, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_active_sharecroppers",
            field=models.PositiveIntegerField(blank=True, editable=False, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_advisors",
            field=models.PositiveIntegerField(blank=True, editable=False, null=True),
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name="producingorganizationdetails",
            name="number_of_executives",
            field=models.PositiveIntegerField(blank=True, editable=False, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_female_active_members",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_female_active_outgrowers",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_female_active_sharecroppers",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_female_advisors",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_female_executives",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_female_farmers_under_supervision",
            field=models.PositiveSmallIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_female_full_time_employees",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_female_members",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_female_non_executives",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_female_outgrowers",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_female_part_time_employees",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_female_seasonal_employees",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_female_sharecroppers",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_full_time_employees",
            field=models.PositiveIntegerField(blank=True, editable=False, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_male_active_members",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_male_active_outgrowers",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_male_active_sharecroppers",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_male_advisors",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_male_executives",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_male_farmers_under_supervision",
            field=models.PositiveSmallIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_male_full_time_employees",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_male_members",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_male_non_executives",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_male_outgrowers",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_male_part_time_employees",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_male_seasonal_employees",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_male_sharecroppers",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_member_cooperatives",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_member_unions",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_members",
            field=models.PositiveIntegerField(blank=True, editable=False, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_non_executives",
            field=models.PositiveIntegerField(blank=True, editable=False, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_outgrowers",
            field=models.PositiveIntegerField(blank=True, editable=False, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_part_time_employees",
            field=models.PositiveIntegerField(blank=True, editable=False, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_potential_female_farmers",
            field=models.PositiveSmallIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_potential_male_farmers",
            field=models.PositiveSmallIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_seasonal_employees",
            field=models.PositiveIntegerField(blank=True, editable=False, null=True),
        ),
        migrations.AlterField(
            model_name="producingorganizationdetails",
            name="number_of_sharecroppers",
            field=models.PositiveIntegerField(blank=True, editable=False, null=True),
        ),
    ]
