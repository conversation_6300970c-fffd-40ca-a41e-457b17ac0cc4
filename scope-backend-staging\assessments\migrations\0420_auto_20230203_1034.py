# Generated by Django 3.1.14 on 2023-02-03 09:34

from django.db import migrations


def add_new_validations(apps, schema_editor):
    InputValidation = apps.get_model("assessments", "InputValidationLimits")
    full_time_employees = InputValidation(
        name="full_time_employees",
        value=50,
        id=InputValidation.objects.all().order_by("-id").first().id + 1,
    )
    full_time_employees.save()
    part_time_employees = InputValidation(
        name="part_time_employees",
        value=60,
        id=InputValidation.objects.all().order_by("-id").first().id + 1,
    )
    part_time_employees.save()


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0419_auto_20221026_1505"),
    ]

    operations = [migrations.RunPython(add_new_validations)]
