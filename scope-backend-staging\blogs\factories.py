import factory
from factory.django import DjangoModelFactory

from .models import Blog, BlogImage, BlogSet


class BlogSetFactory(DjangoModelFactory):
    class Meta:
        model = BlogSet


class BlogFactory(DjangoModelFactory):
    blog_set = factory.SubFactory(BlogSetFactory)

    class Meta:
        model = Blog


class BlogImageFactory(DjangoModelFactory):
    blog = factory.SubFactory(BlogFactory)

    class Meta:
        model = BlogImage
