# Generated by Django 1.10.5 on 2017-03-07 13:57
# Generated by Django 1.10.5 on 2017-03-07 13:49
# Generated by Django 1.10.5 on 2017-03-07 10:44


from django.db import migrations


def move_address_from_assessor_to_user(apps, schema_editor):
    Assessor = apps.get_model("hrm", "Assessor")

    for assessor in Assessor.objects.all():
        user = assessor.user
        user.city = assessor.city
        user.country = assessor.country
        user.global_region = assessor.global_region
        user.region = assessor.region
        user.second_region = assessor.second_region
        user.street = assessor.street
        user.street_number = assessor.street_number
        user.zipcode = assessor.zipcode
        user.save()


class Migration(migrations.Migration):

    dependencies = [
        ("hrm", "0022_assessor_address"),
        ("accounts", "0018_auto_20170307_1355"),
    ]

    operations = [migrations.RunPython(move_address_from_assessor_to_user)]
