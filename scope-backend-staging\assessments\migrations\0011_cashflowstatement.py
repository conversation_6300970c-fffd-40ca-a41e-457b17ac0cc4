from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("hrm", "0001_initial"), ("assessments", "0010_auto_20141103_1819")]

    operations = [
        migrations.CreateModel(
            name="CashFlowStatement",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("year", models.PositiveSmallIntegerField()),
                ("audited", models.NullBooleanField()),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="cashflowstatements",
                        to="assessments.Assessment",
                    ),
                ),
                (
                    "assessor",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="cashflowstatements",
                        to="hrm.Assessor",
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        )
    ]
