# Generated by Django 1.11.12 on 2018-06-22 11:14


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0333_assessment_monthly_production_tab_completed")]

    operations = [
        migrations.AddField(
            model_name="assessment",
            name="monthlyexpensesprojection_set_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="monthlyexpensesprojection_set_no_information_available",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="monthlyincomeprojection_set_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="monthlyincomeprojection_set_no_information_available",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="monthlyproduction_set_completed",
            field=models.<PERSON><PERSON>anField(default=False),
        ),
        migrations.Add<PERSON>ield(
            model_name="assessment",
            name="monthlyproduction_set_no_information_available",
            field=models.BooleanField(default=False),
        ),
    ]
