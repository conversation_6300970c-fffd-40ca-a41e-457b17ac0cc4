import json

from rest_framework import status
from rest_framework.reverse import reverse

from accounts.factories import UserFactory
from customers.factories import ContactFactory
from libs.test_helpers import DenormMixin, QualityReviewerJWTTestCase


class UserTestCase(DenormMixin, QualityReviewerJWTTestCase):
    def test_role_booleans_work(self):
        """
        The role booleans should be set correctly
        """
        url = reverse("accounts:user-me")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset(
            {
                "is_assessor": False,
                "is_financial_specialist": <PERSON>alse,
                "is_employee": False,
                "is_quality_reviewer": True,
                "is_contact": False,
            },
            response_dict,
        )


class LoginTestCase(DenormMixin, QualityReviewerJWTTestCase):
    def test_can_not_login_to_app(self):
        """
        It should not be possible to log in to app
        """
        response = self.client.post(
            "/api-token-auth/",
            {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
            HTTP_ORIGIN="http://app.testserver",
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)

    def test_can_login_to_dashboard(self):
        """
        It should be possible to log in to dashboard
        """
        response = self.client.post(
            "/api-token-auth/",
            {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_not_login_to_portal(self):
        """
        It should not be possible to log in to portal
        """
        response = self.client.post(
            "/api-token-auth/",
            {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
            HTTP_ORIGIN="http://portal.testserver",
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)


class ImpersonationTestCase(DenormMixin, QualityReviewerJWTTestCase):
    def test_only_employee_can_impersonate(self):
        """
        QC not allowed to impersonate users
        """
        user = UserFactory.create(email="<EMAIL>")
        ContactFactory.create(user=user, access_to_dashboard="customer_admin")
        response = self.client.post(
            "/accounts/users/impersonate/",
            {"email": "<EMAIL>"},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual("Not allowed to impersonate.", response_dict["detail"])
