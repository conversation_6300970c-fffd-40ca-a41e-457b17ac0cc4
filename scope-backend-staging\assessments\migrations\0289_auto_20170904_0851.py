# Generated by Django 1.10.5 on 2017-09-04 08:51


from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [("assessments", "0288_auto_20170901_1535")]

    operations = [
        migrations.RemoveField(model_name="balancesheet", name="accepted"),
        migrations.RemoveField(model_name="balancesheet", name="quality_controller"),
        migrations.RemoveField(model_name="bankaccount", name="accepted"),
        migrations.RemoveField(model_name="bankaccount", name="quality_controller"),
        migrations.RemoveField(model_name="basicfinancialinfo", name="accepted"),
        migrations.RemoveField(
            model_name="basicfinancialinfo", name="quality_controller"
        ),
        migrations.RemoveField(model_name="capitalrequirement", name="accepted"),
        migrations.RemoveField(
            model_name="capitalrequirement", name="quality_controller"
        ),
        migrations.RemoveField(model_name="cashflowstatement", name="accepted"),
        migrations.RemoveField(
            model_name="cashflowstatement", name="quality_controller"
        ),
        migrations.RemoveField(model_name="certification", name="accepted"),
        migrations.RemoveField(model_name="certification", name="quality_controller"),
        migrations.RemoveField(model_name="collateralasset", name="accepted"),
        migrations.RemoveField(model_name="collateralasset", name="quality_controller"),
        migrations.RemoveField(model_name="enablingplayer", name="accepted"),
        migrations.RemoveField(model_name="enablingplayer", name="quality_controller"),
        migrations.RemoveField(model_name="executive", name="accepted"),
        migrations.RemoveField(model_name="executive", name="quality_controller"),
        migrations.RemoveField(model_name="financialratio", name="accepted"),
        migrations.RemoveField(model_name="financialratio", name="quality_controller"),
        migrations.RemoveField(model_name="financialscores", name="accepted"),
        migrations.RemoveField(model_name="financialscores", name="quality_controller"),
        migrations.RemoveField(model_name="generalchecks", name="accepted"),
        migrations.RemoveField(model_name="generalchecks", name="quality_controller"),
        migrations.RemoveField(model_name="governance", name="accepted"),
        migrations.RemoveField(model_name="governance", name="quality_controller"),
        migrations.RemoveField(model_name="granthistory", name="accepted"),
        migrations.RemoveField(model_name="granthistory", name="quality_controller"),
        migrations.RemoveField(model_name="inputpurchase", name="accepted"),
        migrations.RemoveField(model_name="inputpurchase", name="quality_controller"),
        migrations.RemoveField(model_name="insurance", name="accepted"),
        migrations.RemoveField(model_name="insurance", name="quality_controller"),
        migrations.RemoveField(model_name="loanapplication", name="accepted"),
        migrations.RemoveField(model_name="loanapplication", name="quality_controller"),
        migrations.RemoveField(model_name="loanhistory", name="accepted"),
        migrations.RemoveField(model_name="loanhistory", name="quality_controller"),
        migrations.RemoveField(model_name="loanrequirement", name="accepted"),
        migrations.RemoveField(model_name="loanrequirement", name="quality_controller"),
        migrations.RemoveField(model_name="monthlyexpensesprojection", name="accepted"),
        migrations.RemoveField(
            model_name="monthlyexpensesprojection", name="quality_controller"
        ),
        migrations.RemoveField(model_name="monthlyincomeprojection", name="accepted"),
        migrations.RemoveField(
            model_name="monthlyincomeprojection", name="quality_controller"
        ),
        migrations.RemoveField(model_name="monthlyproduction", name="accepted"),
        migrations.RemoveField(
            model_name="monthlyproduction", name="quality_controller"
        ),
        migrations.RemoveField(
            model_name="netmonthlyincomeprojection", name="accepted"
        ),
        migrations.RemoveField(
            model_name="netmonthlyincomeprojection", name="quality_controller"
        ),
        migrations.RemoveField(model_name="nonexecutive", name="accepted"),
        migrations.RemoveField(model_name="nonexecutive", name="quality_controller"),
        migrations.RemoveField(
            model_name="producingorganizationdetails", name="accepted"
        ),
        migrations.RemoveField(
            model_name="producingorganizationdetails", name="quality_controller"
        ),
        migrations.RemoveField(model_name="product", name="accepted"),
        migrations.RemoveField(model_name="product", name="quality_controller"),
        migrations.RemoveField(model_name="productionfigure", name="accepted"),
        migrations.RemoveField(
            model_name="productionfigure", name="quality_controller"
        ),
        migrations.RemoveField(model_name="productionmargin", name="accepted"),
        migrations.RemoveField(
            model_name="productionmargin", name="quality_controller"
        ),
        migrations.RemoveField(model_name="productionpurchase", name="accepted"),
        migrations.RemoveField(
            model_name="productionpurchase", name="quality_controller"
        ),
        migrations.RemoveField(model_name="productionsale", name="accepted"),
        migrations.RemoveField(model_name="productionsale", name="quality_controller"),
        migrations.RemoveField(model_name="profitlossstatement", name="accepted"),
        migrations.RemoveField(
            model_name="profitlossstatement", name="quality_controller"
        ),
        migrations.RemoveField(model_name="ratioscores", name="accepted"),
        migrations.RemoveField(model_name="ratioscores", name="quality_controller"),
        migrations.RemoveField(model_name="shareholder", name="accepted"),
        migrations.RemoveField(model_name="shareholder", name="quality_controller"),
        migrations.RemoveField(
            model_name="totalmonthlyexpensesprojection", name="accepted"
        ),
        migrations.RemoveField(
            model_name="totalmonthlyexpensesprojection", name="quality_controller"
        ),
        migrations.RemoveField(
            model_name="totalmonthlyincomeprojection", name="accepted"
        ),
        migrations.RemoveField(
            model_name="totalmonthlyincomeprojection", name="quality_controller"
        ),
        migrations.RemoveField(model_name="valuechainplayer", name="accepted"),
        migrations.RemoveField(
            model_name="valuechainplayer", name="quality_controller"
        ),
    ]
