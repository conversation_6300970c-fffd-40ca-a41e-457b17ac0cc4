from django.db import migrations


def convert_available_to_boolean(apps, schema_editor):
    GeneralCheck = apps.get_model("assessments", "GeneralCheck")
    GeneralCheck.objects.filter(available=0).update(temp_available=False)
    GeneralCheck.objects.filter(available__gt=0).update(temp_available=True)


class Migration(migrations.Migration):

    dependencies = [("assessments", "0165_generalcheck_temp_available")]

    operations = [migrations.RunPython(convert_available_to_boolean)]
