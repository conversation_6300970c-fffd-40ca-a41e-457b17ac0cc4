# Generated by Django 1.10.5 on 2017-03-07 13:55


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("accounts", "0017_user_full_name")]

    operations = [
        migrations.AlterModelOptions(name="user", options={}),
        migrations.AddField(
            model_name="user",
            name="address",
            field=models.TextField(blank=True, editable=False),
        ),
        migrations.AddField(
            model_name="user", name="city", field=models.TextField(blank=True)
        ),
        migrations.AddField(
            model_name="user", name="country", field=models.TextField(blank=True)
        ),
        migrations.AddField(
            model_name="user", name="global_region", field=models.TextField(blank=True)
        ),
        migrations.AddField(
            model_name="user", name="region", field=models.TextField(blank=True)
        ),
        migrations.AddField(
            model_name="user", name="second_region", field=models.TextField(blank=True)
        ),
        migrations.AddField(
            model_name="user", name="street", field=models.TextField(blank=True)
        ),
        migrations.AddField(
            model_name="user", name="street_number", field=models.TextField(blank=True)
        ),
        migrations.AddField(
            model_name="user", name="zipcode", field=models.TextField(blank=True)
        ),
    ]
