# Generated by Django 1.10.7 on 2017-05-29 14:28


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("accounts", "0027_auto_20170331_1526")]

    operations = [
        migrations.AddField(
            model_name="user",
            name="is_assessor",
            field=models.BooleanField(default=False, editable=False),
        ),
        migrations.AddField(
            model_name="user",
            name="is_contact",
            field=models.BooleanField(default=False, editable=False),
        ),
        migrations.AddField(
            model_name="user",
            name="is_customer_admin",
            field=models.<PERSON>oleanField(default=False, editable=False),
        ),
        migrations.AddField(
            model_name="user",
            name="is_customer_user",
            field=models.BooleanField(default=False, editable=False),
        ),
        migrations.AddField(
            model_name="user",
            name="is_employee",
            field=models.BooleanField(default=False, editable=False),
        ),
        migrations.Add<PERSON>ield(
            model_name="user",
            name="is_financial_specialist",
            field=models.<PERSON>oleanField(default=False, editable=False),
        ),
        migrations.AddField(
            model_name="user",
            name="is_quality_reviewer",
            field=models.BooleanField(default=False, editable=False),
        ),
    ]
