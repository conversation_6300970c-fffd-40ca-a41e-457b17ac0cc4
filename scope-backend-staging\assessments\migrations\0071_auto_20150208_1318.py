from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0070_auto_20150208_1303")]

    operations = [
        migrations.AddField(
            model_name="generalcheck",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="accepted_format",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks5",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="asset_register",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks9",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
        migrations.Alter<PERSON>ield(
            model_name="generalchecks",
            name="audited",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks6",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="balance_sheet",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks1",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="bank_statements",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks7",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="cash_flow",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks3",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="cash_flow_projections",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks4",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="collateral",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks8",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="depreciation_policy",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks10",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="external_valuation",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks11",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="financial_statements",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks0",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="grant_history",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks17",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="grants_recorded_in_financial_statement",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks18",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="loan_default_known",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks16",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="loan_history",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks14",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="loan_recorded_in_financial_statement",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks15",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="profit_and_loss",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks2",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="record_keeping",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks12",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalchecks",
            name="software_system",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="generalchecks13",
                null=True,
                blank=True,
                to="assessments.GeneralCheck",
            ),
            preserve_default=True,
        ),
    ]
