import json

from moneyed import Money
from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import AssessmentFactory, BasicProfitLossStatementFactory
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class BasicProfitLossStatementTestCase(DenormMixin, AssessorJWTTestCase):
    maxDiff = None

    def test_can_nullify_moneyfield(self):
        """
        Test that the basic profitloss moneyfields can become null
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor, currency="EUR"
        )
        basic_profit_loss_statements = BasicProfitLossStatementFactory.create(
            assessment=assessment, year=2018, turnover=Money("100.50", "USD")
        )
        url = reverse(
            "assessments:basicprofitlossstatement-detail",
            (basic_profit_loss_statements.pk,),
        )
        response = self.client.patch(
            url,
            data=json.dumps({"turnover": {"amount": None, "currency": "EUR"}}),
            content_type="application/json",
        )
        response_dict = json.loads(response.content)
        self.assertEqual(200, response.status_code)
        self.assertEqual(None, response_dict["turnover"])

    def test_default_currency(self):
        """
        Default currency should come from assessment
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor,
            currency="RWF",
        )
        url = reverse("assessments:basicprofitlossstatement-list")
        data = {
            "assessment": reverse("assessments:assessment-detail", [assessment.pk]),
            "year": 2016,
        }
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_201_CREATED, response.status_code)
        detail_url = json.loads(response.content)["url"]
        patch_data = {"turnover": {"amount": 1234}}
        response = self.client.patch(
            detail_url, json.dumps(patch_data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        instance = assessment.basicprofitlossstatements.get()
        assert instance.turnover.currency.code == "RWF"
        assert instance.turnover_currency == "RWF"
