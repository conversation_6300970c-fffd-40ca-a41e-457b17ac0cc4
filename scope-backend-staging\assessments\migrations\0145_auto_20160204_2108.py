from django.db import migrations, models


def fill_assessment_project(apps, schema_editor):
    Assessment = apps.get_model("assessments", "Assessment")
    assessments = Assessment.objects.annotate(
        project_count=models.Count("projects")
    ).filter(project_count__gte=1)
    for assessment in assessments:
        assessment.project = assessment.projects.all()[0]
        assessment.save()


class Migration(migrations.Migration):

    dependencies = [("assessments", "0144_assessment_project")]

    operations = [migrations.RunPython(fill_assessment_project)]
