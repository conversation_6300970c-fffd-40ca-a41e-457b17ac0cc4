name: scopeinsight backend staging actions run
run-name: ${{ github.actor }} is doing scopeinsight staging actions run
on: 
  push:
    branches:
       - staging
jobs:
  build_containers:
    runs-on: self-hosted
    steps:
      - run: echo "The job was automatically triggered by a ${{ github.event_name }} event."
      - run: echo "This job is now running on a ${{ runner.os }} server hosted by scopeinsight!"
      - run: echo "The name of your branch is ${{ github.ref }} and your repository is ${{ github.repository }}."
      - name: Check out repository code
        uses: actions/checkout@v3
      - run: echo "The ${{ github.repository }} repository has been cloned to the runner."
      - run: sudo docker compose -f docker-compose-staging.yml build 
      - run: sudo docker compose -f docker-compose-staging.yml push 


  deploy_containers:
    runs-on: self-hosted
    needs: build_containers
    steps:
      - run: echo "The job was automatically triggered by a ${{ github.event_name }} event."
      - run: echo "This job is now running on a ${{ runner.os }} server hosted by scopeins<PERSON>!"
      - run: echo "The name of your branch is ${{ github.ref }} and your repository is ${{ github.repository }}."
      - run: ssh api.staging.scopeinsight.com 'sudo docker compose -f ~/dcfiles/staging/backend/docker-compose-staging.yml pull && sudo docker compose -f ~/dcfiles/staging/backend/docker-compose-staging.yml up -d && uptime && date'
      - run: ssh api.staging.scopeinsight.com 'sudo docker exec backend-web-1 python manage.py denorm_drop --settings=settings.docker_staging'
      - run: ssh api.staging.scopeinsight.com 'sudo docker exec backend-web-1 python manage.py migrate --settings=settings.docker_staging'
      - run: ssh api.staging.scopeinsight.com 'sudo docker exec backend-web-1 python manage.py denorm_init --settings=settings.docker_staging'
      - run: ssh api.staging.scopeinsight.com 'sudo docker exec backend-trainingweb-1 python manage.py denorm_drop --settings=settings.docker_staging_training'
      - run: ssh api.staging.scopeinsight.com 'sudo docker exec backend-trainingweb-1 python manage.py migrate --settings=settings.docker_staging_training'
      - run: ssh api.staging.scopeinsight.com 'sudo docker exec backend-trainingweb-1 python manage.py denorm_init --settings=settings.docker_staging_training'
