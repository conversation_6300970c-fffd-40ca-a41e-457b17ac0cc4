# Generated by Django 1.10.7 on 2017-08-22 12:57


import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("assessments", "0277_assessment_display_quality_reviewer"),
    ]

    operations = [
        migrations.AddField(
            model_name="assessment",
            name="assessor_full_name",
            field=models.TextField(blank=True, editable=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="assessor_user",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
