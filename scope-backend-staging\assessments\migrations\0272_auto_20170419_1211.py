# Generated by Django 1.10.5 on 2017-04-19 12:11


from django.db import migrations
from django.db.models import Q


def fill_old_assessments_canonical_value(apps, schema_editor):
    Governance = apps.get_model("assessments", "Governance")
    Governance.objects.filter(auto_created=True).filter(
        Q(name="Assemblée générale annuelle")
        | Q(name="Asamblea General Anual")
        | Q(name="Annual general meeting")
    ).update(canonical_name="Annual general meeting")
    Governance.objects.filter(auto_created=True).filter(
        Q(name="Réunion des Actionnaires")
        | Q(name="Reunión de accionistas")
        | Q(name="Shareholder's meeting")
    ).update(canonical_name="Shareholder's meeting")
    Governance.objects.filter(auto_created=True).filter(
        Q(name="Equipe d'encadrement")
        | Q(name="Equipo Directivo")
        | Q(name="Management team")
    ).update(canonical_name="Management team")
    Governance.objects.filter(auto_created=True).filter(
        Q(name="Conseil de Surveillance")
        | Q(name="Consejo Supervisor")
        | Q(name="Supervisory board")
    ).update(canonical_name="Supervisory board")


class Migration(migrations.Migration):

    dependencies = [("assessments", "0271_governance_canonical_name")]

    operations = [migrations.RunPython(fill_old_assessments_canonical_value)]
