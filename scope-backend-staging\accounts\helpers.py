from uuid import uuid4

from django.contrib.auth.models import (
    AbstractBaseUser,
    PermissionsMixin,
    UserManager as DjangoUserManager,
)
from django.core import validators
from django.core.mail import send_mail
from django.db import models
from django.utils import timezone
from django.utils.translation import ugettext_lazy as _

from libs.field_helpers import LowerCaseTextField


class UserManager(DjangoUserManager):
    def create_user(self, email, password=None, **extra_fields):
        username = str(uuid4()).replace("-", "")[:30]
        return super(UserManager, self).create_user(
            username, email, password, **extra_fields
        )

    def create_superuser(self, email, password, **extra_fields):
        username = str(uuid4()).replace("-", "")[:30]
        return super(UserManager, self).create_superuser(
            username, email, password, **extra_fields
        )


class UnlimitedUsernameEmailAbstractUser(AbstractBaseUser, PermissionsMixin):
    """
    An abstract base class implementing a fully featured User model with
    admin-compliant permissions.

    Username, password and email are required. Other fields are optional.

    Copy-pasted from django to turn all CharFields into TextFields
    Log in using email instead of username
    """

    username = models.TextField(
        _("username"),
        blank=True,
        help_text=_(
            "Required. 30 characters or fewer. Letters, digits and " "@/./+/-/_ only."
        ),
        validators=[
            validators.RegexValidator(
                r"^[\w.@+-]+$", _("Enter a valid username."), "invalid"
            )
        ],
    )
    first_name = models.TextField(_("first name"), blank=True)
    last_name = models.TextField(_("last name"), blank=True)
    email = LowerCaseTextField(
        _("email address"), unique=True, validators=[validators.validate_email]
    )
    is_staff = models.BooleanField(
        _("staff status"),
        default=False,
        help_text=_("Designates whether the user can log into this admin " "site."),
    )
    is_active = models.BooleanField(
        _("active"),
        default=True,
        help_text=_(
            "Designates whether this user should be treated as "
            "active. Unselect this instead of deleting accounts."
        ),
    )
    date_joined = models.DateTimeField(_("date joined"), default=timezone.now)

    objects = UserManager()

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = []

    class Meta:
        verbose_name = _("user")
        verbose_name_plural = _("users")
        abstract = True

    def get_full_name(self):
        """
        Returns the first_name plus the last_name, with a space in between.
        """
        full_name = "%s %s" % (self.first_name, self.last_name)
        return full_name.strip()

    def get_short_name(self):
        "Returns the short name for the user."
        return self.first_name

    def email_user(self, subject, message, from_email=None, **kwargs):
        """
        Sends an email to this User.
        """
        send_mail(subject, message, from_email, [self.email], **kwargs)

    def __str__(self):
        return self.email

    def get_username(self):
        return self.email
