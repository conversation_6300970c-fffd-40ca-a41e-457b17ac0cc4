# -*- coding: utf-8 -*-
# Generated by Django 1.11.18 on 2019-02-06 15:24
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0367_auto_20190129_1453")]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="product",
            name="average_age_of_production_unit",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="product",
            name="average_production_unit_lifetime",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="product",
            name="average_yield",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="product",
            name="land_used_for_product",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="product",
            name="production_potential_estimate",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=40, null=True
            ),
        ),
    ]
