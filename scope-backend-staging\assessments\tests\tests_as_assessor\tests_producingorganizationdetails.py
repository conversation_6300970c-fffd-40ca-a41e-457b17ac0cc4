import json

from denorm import flush
from rest_framework.reverse import reverse

from assessments.factories import ProducingOrganizationDetailsFactory
from assessments.models import ProducingOrganizationDetails
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class ProducingOrganizationDetailsTestCase(DenormMixin, AssessorJWTTestCase):
    def test_has_agent_fields(self):
        """
        ProducingOrganizationDetails should have agent fields

        number_of_male_farmers_under_supervision
        number_of_female_farmers_under_supervision
        number_of_potential_male_farmers
        number_of_potential_female_farmers
        """
        obj = ProducingOrganizationDetailsFactory.create()
        self.assertEqual(None, obj.number_of_male_farmers_under_supervision)
        self.assertEqual(None, obj.number_of_female_farmers_under_supervision)
        self.assertEqual(None, obj.number_of_potential_male_farmers)
        self.assertEqual(None, obj.number_of_potential_female_farmers)

    def test_can_see_agent_fields(self):
        """
        agent fields should be visible in api

        number_of_male_farmers_under_supervision
        number_of_female_farmers_under_supervision
        number_of_potential_male_farmers
        number_of_potential_female_farmers
        """
        obj = ProducingOrganizationDetailsFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            number_of_male_farmers_under_supervision=1,
            number_of_female_farmers_under_supervision=2,
            number_of_potential_male_farmers=3,
            number_of_potential_female_farmers=4,
        )
        url = reverse("assessments:producingorganizationdetails-detail", [obj.pk])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertEqual(1, response_dict["number_of_male_farmers_under_supervision"])
        self.assertEqual(2, response_dict["number_of_female_farmers_under_supervision"])
        self.assertEqual(3, response_dict["number_of_potential_male_farmers"])
        self.assertEqual(4, response_dict["number_of_potential_female_farmers"])

    def test_can_patch_agent_fields(self):
        """
        agent fields should be visible in api

        number_of_male_farmers_under_supervision
        number_of_female_farmers_under_supervision
        number_of_potential_male_farmers
        number_of_potential_female_farmers
        """
        obj = ProducingOrganizationDetailsFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:producingorganizationdetails-detail", [obj.pk])
        data = {
            "number_of_male_farmers_under_supervision": 1,
            "number_of_female_farmers_under_supervision": 2,
            "number_of_potential_male_farmers": 3,
            "number_of_potential_female_farmers": 4,
        }
        self.client.patch(url, json.dumps(data), content_type="application/json")
        db_obj = ProducingOrganizationDetails.objects.get(pk=obj.pk)
        self.assertEqual(1, db_obj.number_of_male_farmers_under_supervision)
        self.assertEqual(2, db_obj.number_of_female_farmers_under_supervision)
        self.assertEqual(3, db_obj.number_of_potential_male_farmers)
        self.assertEqual(4, db_obj.number_of_potential_female_farmers)

    def test_can_set_personal_transportation(self):
        """
        Should be able to set personal_transportation with a string value
        """
        obj = ProducingOrganizationDetailsFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:producingorganizationdetails-detail", [obj.pk])
        data = {"personal_transportation": "car"}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        db_obj = ProducingOrganizationDetails.objects.get(pk=obj.pk)
        self.assertEqual("car", db_obj.personal_transportation)

    def test_can_get_personal_transportation(self):
        """
        Should be able to get personal_transportation as a string value
        """
        obj = ProducingOrganizationDetailsFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            personal_transportation="car",
        )
        url = reverse("assessments:producingorganizationdetails-detail", [obj.pk])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("personal_transportation", response_dict)
        self.assertEqual("car", response_dict["personal_transportation"])
        self.assertIn("display_personal_transportation", response_dict)
        self.assertEqual("car", response_dict["display_personal_transportation"])

    def test_can_set_computer_access(self):
        """
        Should be able to set computer_access with a string value
        """
        obj = ProducingOrganizationDetailsFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:producingorganizationdetails-detail", [obj.pk])
        data = {"computer_access": "car"}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        db_obj = ProducingOrganizationDetails.objects.get(pk=obj.pk)
        self.assertEqual("car", db_obj.computer_access)

    def test_can_get_computer_access(self):
        """
        Should be able to get computer_access as a string value
        """
        obj = ProducingOrganizationDetailsFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            computer_access="car",
        )
        url = reverse("assessments:producingorganizationdetails-detail", [obj.pk])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("computer_access", response_dict)
        self.assertEqual("car", response_dict["computer_access"])
        self.assertIn("display_computer_access", response_dict)
        self.assertEqual("car", response_dict["display_computer_access"])

    def test_number_of_people_default_null(self):
        """
        All number_of_* fields should initialize to null
        """
        obj = ProducingOrganizationDetailsFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        flush()
        url = reverse("assessments:producingorganizationdetails-detail", [obj.pk])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        keys_to_check = [
            "number_of_female_active_outgrowers",
            "number_of_female_active_sharecroppers",
            "number_of_male_part_time_employees",
            "number_of_female_seasonal_employees",
            "number_of_female_part_time_employees",
            "number_of_female_outgrowers",
            "number_of_seasonal_employees",
            "number_of_potential_male_farmers",
            "number_of_male_seasonal_employees",
            "number_of_male_active_members",
            "number_of_active_members",
            "number_of_male_full_time_employees",
            "number_of_outgrowers",
            "number_of_male_members",
            "number_of_male_non_executives",
            "number_of_active_outgrowers",
            "number_of_female_non_executives",
            "number_of_sharecroppers",
            "number_of_female_sharecroppers",
            "number_of_active_sharecroppers",
            "number_of_part_time_employees",
            "number_of_male_sharecroppers",
            "number_of_executives",
            "number_of_member_unions",
            "number_of_male_executives",
            "number_of_female_active_members",
            "number_of_female_farmers_under_supervision",
            "number_of_full_time_employees",
            "number_of_female_members",
            "number_of_non_executives",
            "number_of_male_outgrowers",
            "number_of_male_active_sharecroppers",
            "number_of_potential_female_farmers",
            "number_of_male_active_outgrowers",
            "number_of_member_cooperatives",
            "number_of_members",
            "number_of_male_farmers_under_supervision",
            "number_of_female_full_time_employees",
            "number_of_female_executives",
        ]
        for key in keys_to_check:
            self.assertIn(key, response_dict)
            self.assertIsNone(response_dict[key], "{} is not None".format(key))

    def test_values_patchable_with_null(self):
        """
        All values are patchable with null
        """
        prod_org_details = ProducingOrganizationDetailsFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            number_of_male_farmers_under_supervision=2,
        )
        url = reverse(
            "assessments:producingorganizationdetails-detail", [prod_org_details.pk]
        )
        data = {"number_of_male_farmers_under_supervision": None}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        response_dict = json.loads(response.content)
        updated_object = ProducingOrganizationDetails.objects.get(
            pk=prod_org_details.pk
        )
        self.assertEqual(None, updated_object.number_of_male_farmers_under_supervision)
        self.assertSetEqual(
            set(["modified_at", "number_of_male_farmers_under_supervision"]),
            set(response_dict.keys()),
        )
