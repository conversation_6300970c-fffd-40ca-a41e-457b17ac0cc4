import json

from rest_framework.reverse import reverse

from assessments.factories import AssessmentInvitationFactory
from hrm.factories import EmployeeFactory
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class AssessmentInvitationTestCase(DenormMixin, AssessorJWTTestCase):
    def test_can_only_see_own_invitations_when_also_employee(self):
        """
        When the assessor is also an employee, he/she should still only be
        able to see own invitations when using app
        """
        EmployeeFactory.create(user=self.assessor.user)
        AssessmentInvitationFactory.create_batch(2)
        AssessmentInvitationFactory.create_batch(3, assessor=self.assessor)
        url = reverse("assessments:assessmentinvitation-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(3, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(3, len(response_dict["results"]))

    def test_can_see_built_assessments(self):
        """
        Should be able to see assessments with
        build_response_tree_completed == True
        """
        AssessmentInvitationFactory.create(
            assessor=self.assessor, assessment__build_response_tree_completed=True
        )
        url = reverse("assessments:assessmentinvitation-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(1, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(1, len(response_dict["results"]))

    def test_can_not_see_unbuilt_assessments(self):
        """
        Should not be able to see assessments with
        build_response_tree_completed == False
        """
        AssessmentInvitationFactory.create(
            assessor=self.assessor, assessment__build_response_tree_completed=False
        )
        url = reverse("assessments:assessmentinvitation-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(0, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(0, len(response_dict["results"]))
