import datetime
import json

from denorm import flush
from flaky import flaky
from freezegun import freeze_time
from rest_framework import status
from rest_framework.reverse import reverse

from customers.factories import ContactFactory, CustomerFactory
from hrm.factories import (
    AssessorFactory,
    AssessorTrainingFactory,
    EmployeeFactory,
    FinancialSpecialistTrainingFactory,
)
from libs.test_helpers import DenormMixin, EmployeeJWTTestCase
from products.factories import ToolTypeFactory

from .factories import GroupFactory, UserFactory


class DashboardTestCase(DenormMixin, EmployeeJWTTestCase):
    def test_dashboard_endpoint_works(self):
        """
        User should be able to get the dashboard
        """
        url = reverse("accounts:dashboard")
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)


class UserTestCase(DenormMixin, EmployeeJWTTestCase):
    maxDiff = None

    def test_can_get_list(self):
        """
        User list view contains correct results
        """
        UserFactory.create_batch(5, is_superuser=True)
        UserFactory.create(
            email="<EMAIL>",
            display_in_list=False,
        )
        UserFactory.create(email="<EMAIL>", display_in_list=False)
        url = reverse("accounts:user-list")

        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        # expect 6 users, including self
        self.assertEqual(6, len(response_dict["results"]))

    def test_can_get_user_with_if_modified_since(self):
        """
        It should be possible to request info on a user
        with an If-Modified-Since header in place
        """
        user = UserFactory.create()
        modified_since = user.modified_at + datetime.timedelta(seconds=1)
        modified_since = modified_since.strftime("%a, %d %b %Y %H:%M:%S GMT")
        url = reverse("accounts:user-detail", [user.pk])
        self.client.get(
            url, content_type="application/json", HTTP_IF_MODIFIED_SINCE=modified_since
        )

    def test_last_breaking_code_change(self):
        """
        settings.LAST_BREAKING_CODE_CHANGE should override the database and
        always return 200
        """
        user = UserFactory.create(is_superuser=True)
        flush()
        user.refresh_from_db()
        modified_since = user.modified_at + datetime.timedelta(seconds=1)
        modified_since_str = modified_since.strftime("%a, %d %b %Y %H:%M:%S GMT")
        url = reverse("accounts:user-detail", [user.pk])
        response = self.client.get(
            url,
            content_type="application/json",
            HTTP_IF_MODIFIED_SINCE=modified_since_str,
        )
        self.assertEqual(status.HTTP_304_NOT_MODIFIED, response.status_code)
        last_code_change = modified_since + datetime.timedelta(seconds=1)
        with self.settings(LAST_BREAKING_CODE_CHANGE=last_code_change):
            response = self.client.get(
                url,
                content_type="application/json",
                HTTP_IF_MODIFIED_SINCE=modified_since_str,
            )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        modified_since = last_code_change + datetime.timedelta(seconds=1)
        modified_since_str = modified_since.strftime("%a, %d %b %Y %H:%M:%S GMT")
        with self.settings(LAST_BREAKING_CODE_CHANGE=last_code_change):
            response = self.client.get(
                url,
                content_type="application/json",
                HTTP_IF_MODIFIED_SINCE=modified_since_str,
            )
        self.assertEqual(status.HTTP_304_NOT_MODIFIED, response.status_code)

    def test_users_me_works(self):
        """
        User should be able to get users me with fields
        """
        url = reverse("accounts:user-me") + "?fields=employee&fields=assessor"
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_user_detail_nested_roles_works(self):
        """
        It should be possible to get a user detail with nested roles
        """
        user = UserFactory.create(is_superuser=True)
        EmployeeFactory.create(user=user)
        AssessorFactory.create(user=user)
        ContactFactory.create(user=user)
        url = (
            reverse("accounts:user-detail", [user.pk])
            + "?fields=employee"
            + "&fields=assessor"
            + "&fields=customer_contact"
        )
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        expected_keys = {"employee", "assessor", "customer_contact"}
        self.assertSetEqual(expected_keys, set(response_dict.keys()) & expected_keys)
        for key in expected_keys:
            self.assertIsInstance(response_dict[key], dict)

    def test_user_groups_read_only(self):
        """
        User.groups should be a read only attribute
        """
        user = UserFactory.create(groups=GroupFactory.create_batch(3))
        url = reverse("accounts:user-detail", [user.pk])
        data = {"groups": []}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        self.assertEqual(3, user.groups.count())

    def test_languages_spoken_filter(self):
        """
        Filtering by customer_name should work
        """
        self.jwt_user.languages_spoken = []
        self.jwt_user.save()
        users = UserFactory.create_batch(10, is_superuser=True, languages_spoken=None)
        for user in users[1:5]:
            if user.languages_spoken is None:
                user.languages_spoken = []
            user.languages_spoken.append("fr")
            user.save()
        for user in users[4:8]:
            if user.languages_spoken is None:
                user.languages_spoken = []
            user.languages_spoken.append("es")
            user.save()
        for user in users[3:7]:
            if user.languages_spoken is None:
                user.languages_spoken = []
            user.languages_spoken.append("en")
            user.save()
        url = reverse("accounts:user-list") + "?languages_spoken=fr"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)

        self.assertIn("count", response_dict)
        self.assertEqual(4, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(4, len(response_dict["results"]))
        url = (
            reverse("accounts:user-list")
            + "?languages_spoken=fr"
            + "&languages_spoken=es"
        )
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)

        self.assertIn("count", response_dict)
        self.assertEqual(7, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(7, len(response_dict["results"]))

    def test_role_booleans_work(self):
        """
        The role booleans should be set correctly
        """
        url = reverse("accounts:user-me")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset(
            {
                "is_assessor": False,
                "is_financial_specialist": False,
                "is_employee": True,
                "is_quality_reviewer": False,
                "is_contact": False,
            },
            response_dict,
        )

    def test_is_assessor_not_expired(self):
        assessor = AssessorFactory.create(
            is_assessor=True, expiration_date_assessor="2019-01-05"
        )
        flush()
        url = reverse("accounts:user-detail", [assessor.user_id])
        with freeze_time("2019-01-04"):
            response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset({"is_assessor": True}, response_dict)

    def test_is_assessor_expired(self):
        assessor = AssessorFactory.create(
            is_assessor=True, expiration_date_assessor="2019-01-05"
        )
        flush()
        url = reverse("accounts:user-detail", [assessor.user_id])
        with freeze_time("2019-01-05"):
            response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset({"is_assessor": False}, response_dict)

    def test_is_financial_specialist_not_expired(self):
        assessor = AssessorFactory.create(
            financial_specialist=True, expiration_date_financial_specialist="2019-01-05"
        )
        flush()
        url = reverse("accounts:user-detail", [assessor.user_id])
        with freeze_time("2019-01-04"):
            response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset({"is_financial_specialist": True}, response_dict)

    def test_is_financial_specialist_expired(self):
        assessor = AssessorFactory.create(
            financial_specialist=True, expiration_date_financial_specialist="2019-01-05"
        )
        flush()
        url = reverse("accounts:user-detail", [assessor.user_id])
        with freeze_time("2019-01-05"):
            response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset({"is_financial_specialist": False}, response_dict)

    def test_user_with_this_email_exists(self):
        """
        Raise 400 error when email is not available
        """
        UserFactory.create(email="<EMAIL>")
        organization = CustomerFactory.create()
        url = reverse("accounts:user-list")
        data = {
            "email": "<EMAIL>",
            "organization": reverse("customers:customer-detail", [organization.pk]),
        }
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )

        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual(
            ["user with this email address already exists."], response_dict["email"]
        )

    def test_email_available(self):
        """
        Check email availability
        """
        url = reverse("accounts:user-check-email-availability")
        data = {"email": "<EMAIL>"}
        response = self.client.get(url, data)
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertTrue(response_dict["is_available"])
        self.assertFalse(response_dict["dashboard_access"])

    def test_not_email_is_invalid(self):
        """
        Check email availability
        """
        url = reverse("accounts:user-check-email-availability")
        data = {"email": "thisistotallynotanemail"}
        response = self.client.get(url, data)
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual(["Enter a valid email address."], response_dict["email"])

    def test_empty_email_is_invalid(self):
        """
        Check email availability
        """
        url = reverse("accounts:user-check-email-availability")
        data = {"email": ""}
        response = self.client.get(url, data)
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual(["This field may not be blank."], response_dict["email"])

    def test_email_exists(self):
        """
        Check email availability
        """
        UserFactory.create(email="<EMAIL>")
        url = reverse("accounts:user-check-email-availability")
        data = {"email": "<EMAIL>"}
        response = self.client.get(url, data)
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertFalse(response_dict["is_available"])
        self.assertFalse(response_dict["dashboard_access"])

    def test_email_exists_user_can_access_dashboard(self):
        """
        Check email availability
        """
        EmployeeFactory.create(
            user__email="<EMAIL>", qc_only=False, is_active=True
        )
        flush()
        url = reverse("accounts:user-check-email-availability")
        data = {"email": "<EMAIL>"}
        response = self.client.get(url, data)
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertFalse(response_dict["is_available"])
        self.assertTrue(response_dict["dashboard_access"])

    def test_email_exists_user_cannot_access_dashboard(self):
        """
        Check email availability
        """
        AssessorFactory.create(user__email="<EMAIL>")
        flush()
        url = reverse("accounts:user-check-email-availability")
        data = {"email": "<EMAIL>"}
        response = self.client.get(url, data)
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertFalse(response_dict["is_available"])
        self.assertFalse(response_dict["dashboard_access"])

    def test_roles_filter_metadata(self):
        """
        roles filter metadata should not be affected by the application of the
        roles filter
        """
        AssessorFactory.create(is_assessor=True, financial_specialist=False)
        AssessorFactory.create(financial_specialist=True, is_assessor=False)
        EmployeeFactory.create(qc_only=True)
        ContactFactory.create(access_to_dashboard="customer_admin")
        ContactFactory.create(access_to_dashboard="customer_user")
        ContactFactory.create()
        flush()
        url = reverse("accounts:user-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertIn("roles", response_dict["filters"])
        self.assertEqual(7, len(response_dict["filters"]["roles"]))
        roles = [
            "is_assessor",
            "is_financial_specialist",
            "is_employee",
            "is_quality_reviewer",
            "is_customer_admin",
            "is_customer_user",
            "is_contact",
        ]
        for role in roles:
            filtered_url = url + "?roles={}".format(role)
            response = self.client.get(filtered_url, content_type="application/json")
            response_dict = json.loads(response.content)
            self.assertIn("filters", response_dict)
            self.assertIn("roles", response_dict["filters"])
            self.assertEqual(7, len(response_dict["filters"]["roles"]))

    def test_roles_filter_combined(self):
        """
        When selecting multiple roles, the results should be combined
        """
        AssessorFactory.create(is_assessor=True, financial_specialist=False)
        AssessorFactory.create(financial_specialist=True, is_assessor=False)
        EmployeeFactory.create(qc_only=True)
        ContactFactory.create()
        flush()
        url = (
            reverse("accounts:user-list")
            + "?roles=is_employee&roles=is_quality_reviewer"
        )
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(2, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(2, len(response_dict["results"]))

    def test_organization_filter_metadata(self):
        """
        The organization filter metadata should not be affected by the
        application of the organization filter
        """
        UserFactory.create_batch(4, is_superuser=True)
        url = reverse("accounts:user-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertIn("organization", response_dict["filters"])
        self.assertEqual(5, len(response_dict["filters"]["organization"]))
        url += "?organization={}".format(self.employee.user.organization.name)
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertIn("organization", response_dict["filters"])
        self.assertEqual(5, len(response_dict["filters"]["organization"]))

    @flaky(max_runs=5)
    def test_country_filter_metadata(self):
        """
        The country filter metadata should not be affected by the
        application of the country filter
        """
        UserFactory.create_batch(4, is_superuser=True)
        url = reverse("accounts:user-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertIn("country", response_dict["filters"])
        self.assertEqual(5, len(response_dict["filters"]["country"]))
        url += "?country={}".format(self.employee.user.country)
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertIn("country", response_dict["filters"])
        self.assertEqual(5, len(response_dict["filters"]["country"]))

    def test_languages_spoken_filter_metadata(self):
        """
        The languages_spoken filter metadata should not be affected by the
        application of the languages_spoken filter
        """
        self.jwt_user.languages_spoken = ["en"]
        self.jwt_user.save()
        UserFactory.create(languages_spoken=["en"], is_superuser=True)
        UserFactory.create(languages_spoken=["es", "fr"], is_superuser=True)
        UserFactory.create(languages_spoken=["en", "fr"], is_superuser=True)
        UserFactory.create(languages_spoken=["es"], is_superuser=True)
        url = reverse("accounts:user-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertIn("languages_spoken", response_dict["filters"])
        self.assertEqual(3, len(response_dict["filters"]["languages_spoken"]))
        url += "?languages_spoken=en"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertIn("languages_spoken", response_dict["filters"])
        self.assertEqual(3, len(response_dict["filters"]["languages_spoken"]))

    def test_tools_filter_metadata(self):
        """
        The tools filter metadata should not be affected by the
        application of the tools filter
        """
        assessors = AssessorFactory.create_batch(5, is_assessor=True)
        tool_types = ToolTypeFactory.create_batch(5)
        for assessor, tool_type in zip(assessors, tool_types):
            assessor.tools.set([tool_type])
        assessors[0].tools.add(tool_types[-1])
        flush()
        url = reverse("accounts:user-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertIn("tools", response_dict["filters"])
        self.assertEqual(5, len(response_dict["filters"]["tools"]))
        url += "?tools={}".format(tool_types[0].name)
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertIn("tools", response_dict["filters"])
        self.assertEqual(5, len(response_dict["filters"]["tools"]))

    def test_expiration_date_filter_metadata(self):
        """
        The expiration date filter metadata should not be affected by the
        application of the tools filter
        """
        assessors = AssessorFactory.create_batch(
            5, is_assessor=True, financial_specialist=True
        )
        dates = [datetime.date(2017, i, i) for i in range(1, 6)]
        for assessor, date in zip(assessors, dates):
            AssessorTrainingFactory.create(
                assessor=assessor, certification_valid_until=date
            )
        financial_specialists = AssessorFactory.create_batch(
            5, is_assessor=True, financial_specialist=True
        )
        dates = [datetime.date(2016, i, i) for i in range(1, 6)]
        for financial_specialist, date in zip(financial_specialists, dates):
            FinancialSpecialistTrainingFactory.create(
                assessor=financial_specialist, certification_valid_until=date
            )
        flush()
        url = reverse("accounts:user-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertIn("expiration_date", response_dict["filters"])
        self.assertIn("min", response_dict["filters"]["expiration_date"])
        self.assertEqual(
            "2016-01-01", response_dict["filters"]["expiration_date"]["min"]
        )
        self.assertIn("max", response_dict["filters"]["expiration_date"])
        self.assertEqual(
            "2017-05-05", response_dict["filters"]["expiration_date"]["max"]
        )
        url += "?min_date=2017-02-02&max_date=2017-04-04"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertIn("expiration_date", response_dict["filters"])
        self.assertIn("min", response_dict["filters"]["expiration_date"])
        self.assertEqual(
            "2016-01-01", response_dict["filters"]["expiration_date"]["min"]
        )
        self.assertIn("max", response_dict["filters"]["expiration_date"])
        self.assertEqual(
            "2017-05-05", response_dict["filters"]["expiration_date"]["max"]
        )

    def test_expiration_date_filter_metadata_assessor(self):
        """
        The expiration date filter metadata should not be affected by the
        application of the tools filter
        """
        assessors = AssessorFactory.create_batch(
            5, is_assessor=True, financial_specialist=True
        )
        dates = [datetime.date(2017, i, i) for i in range(1, 6)]
        for assessor, date in zip(assessors, dates):
            AssessorTrainingFactory.create(
                assessor=assessor, certification_valid_until=date
            )
        financial_specialists = AssessorFactory.create_batch(
            5, is_assessor=True, financial_specialist=True
        )
        dates = [datetime.date(2016, i, i) for i in range(1, 6)]
        for financial_specialist, date in zip(financial_specialists, dates):
            FinancialSpecialistTrainingFactory.create(
                assessor=financial_specialist, certification_valid_until=date
            )
        flush()
        url = reverse("accounts:user-list") + "?roles=is_assessor"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertIn("expiration_date", response_dict["filters"])
        self.assertIn("min", response_dict["filters"]["expiration_date"])
        self.assertEqual(
            "2017-01-01", response_dict["filters"]["expiration_date"]["min"]
        )
        self.assertIn("max", response_dict["filters"]["expiration_date"])
        self.assertEqual(
            "2017-05-05", response_dict["filters"]["expiration_date"]["max"]
        )
        url += "&min_date=2017-02-02&max_date=2017-04-04"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertIn("expiration_date", response_dict["filters"])
        self.assertIn("min", response_dict["filters"]["expiration_date"])
        self.assertEqual(
            "2017-01-01", response_dict["filters"]["expiration_date"]["min"]
        )
        self.assertIn("max", response_dict["filters"]["expiration_date"])
        self.assertEqual(
            "2017-05-05", response_dict["filters"]["expiration_date"]["max"]
        )

    def test_expiration_date_filter_metadata_financial_specialist(self):
        """
        The expiration date filter metadata should not be affected by the
        application of the tools filter
        """
        assessors = AssessorFactory.create_batch(
            5, is_assessor=True, financial_specialist=True
        )
        dates = [datetime.date(2017, i, i) for i in range(1, 6)]
        for assessor, date in zip(assessors, dates):
            AssessorTrainingFactory.create(
                assessor=assessor, certification_valid_until=date
            )
        financial_specialists = AssessorFactory.create_batch(
            5, is_assessor=True, financial_specialist=True
        )
        dates = [datetime.date(2016, i, i) for i in range(1, 6)]
        for financial_specialist, date in zip(financial_specialists, dates):
            FinancialSpecialistTrainingFactory.create(
                assessor=financial_specialist, certification_valid_until=date
            )
        flush()
        url = reverse("accounts:user-list") + "?roles=is_financial_specialist"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertIn("expiration_date", response_dict["filters"])
        self.assertIn("min", response_dict["filters"]["expiration_date"])
        self.assertEqual(
            "2016-01-01", response_dict["filters"]["expiration_date"]["min"]
        )
        self.assertIn("max", response_dict["filters"]["expiration_date"])
        self.assertEqual(
            "2016-05-05", response_dict["filters"]["expiration_date"]["max"]
        )
        url += "&min_date=2016-02-02&max_date=2016-04-04"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertIn("expiration_date", response_dict["filters"])
        self.assertIn("min", response_dict["filters"]["expiration_date"])
        self.assertEqual(
            "2016-01-01", response_dict["filters"]["expiration_date"]["min"]
        )
        self.assertIn("max", response_dict["filters"]["expiration_date"])
        self.assertEqual(
            "2016-05-05", response_dict["filters"]["expiration_date"]["max"]
        )

    def expiration_date_setup(self):
        """
        Setup assessors to test expiration date filter
        """
        assessors = [
            AssessorFactory.create(
                is_assessor=True,
                financial_specialist=False,
                expiration_date_assessor="2017-01-01",
            ),
            AssessorFactory.create(
                is_assessor=True,
                financial_specialist=False,
                expiration_date_assessor="2018-01-01",
            ),
        ]
        financial_specialists = [
            AssessorFactory.create(
                is_assessor=False,
                financial_specialist=True,
                expiration_date_financial_specialist="2017-01-01",
            ),
            AssessorFactory.create(
                is_assessor=False,
                financial_specialist=True,
                expiration_date_financial_specialist="2018-01-01",
            ),
        ]
        allrounders = [
            AssessorFactory.create(
                is_assessor=True,
                financial_specialist=True,
                expiration_date_assessor="2017-01-01",
                expiration_date_financial_specialist="2017-01-01",
            ),
            AssessorFactory.create(
                is_assessor=True,
                financial_specialist=True,
                expiration_date_assessor="2018-01-01",
                expiration_date_financial_specialist="2018-01-01",
            ),
        ]
        former_allrounders = [
            AssessorFactory.create(
                is_assessor=True,
                financial_specialist=True,
                expiration_date_assessor="2017-01-01",
                expiration_date_financial_specialist="2018-01-01",
            ),
            AssessorFactory.create(
                is_assessor=True,
                financial_specialist=True,
                expiration_date_assessor="2018-01-01",
                expiration_date_financial_specialist="2017-01-01",
            ),
        ]
        flush()
        return (assessors, financial_specialists, allrounders, former_allrounders)

    def test_min_date_filter(self):
        (
            assessors,
            financial_specialists,
            allrounders,
            former_allrounders,
        ) = self.expiration_date_setup()
        url = reverse("accounts:user-list") + "?min_date=2017-02-02"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertSetEqual(
            {
                assessors[1].user_id,
                financial_specialists[1].user_id,
                allrounders[1].user_id,
                former_allrounders[0].user_id,
                former_allrounders[1].user_id,
            },
            set([item["id"] for item in response_dict["results"]]),
        )

    def test_max_date_filter(self):
        (
            assessors,
            financial_specialists,
            allrounders,
            former_allrounders,
        ) = self.expiration_date_setup()
        url = reverse("accounts:user-list") + "?max_date=2017-02-02"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertSetEqual(
            {
                assessors[0].user_id,
                financial_specialists[0].user_id,
                allrounders[0].user_id,
                former_allrounders[0].user_id,
                former_allrounders[1].user_id,
            },
            set([item["id"] for item in response_dict["results"]]),
        )

    def test_min_max_date_filter(self):
        (
            assessors,
            financial_specialists,
            allrounders,
            former_allrounders,
        ) = self.expiration_date_setup()
        url = (
            reverse("accounts:user-list")
            + "?min_date=2017-02-02"
            + "&max_date=2017-02-02"
        )
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertSetEqual(
            set(), set([item["id"] for item in response_dict["results"]])
        )

    def test_min_date_filter_assessor(self):
        (
            assessors,
            financial_specialists,
            allrounders,
            former_allrounders,
        ) = self.expiration_date_setup()
        url = (
            reverse("accounts:user-list")
            + "?min_date=2017-02-02"
            + "&roles=is_assessor"
        )
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertSetEqual(
            {
                assessors[1].user_id,
                allrounders[1].user_id,
                former_allrounders[1].user_id,
            },
            set([item["id"] for item in response_dict["results"]]),
        )

    def test_max_date_filter_assessor(self):
        (
            assessors,
            financial_specialists,
            allrounders,
            former_allrounders,
        ) = self.expiration_date_setup()
        url = (
            reverse("accounts:user-list")
            + "?max_date=2017-02-02"
            + "&roles=is_assessor"
        )
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertSetEqual(
            {
                assessors[0].user_id,
                allrounders[0].user_id,
                former_allrounders[0].user_id,
            },
            set([item["id"] for item in response_dict["results"]]),
        )

    def test_min_max_date_filter_assessor(self):
        (
            assessors,
            financial_specialists,
            allrounders,
            former_allrounders,
        ) = self.expiration_date_setup()
        url = (
            reverse("accounts:user-list")
            + "?min_date=2017-02-02"
            + "&max_date=2017-02-02"
            + "&roles=is_assessor"
        )
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertSetEqual(
            set(), set([item["id"] for item in response_dict["results"]])
        )

    def test_min_date_filter_financial_specialist(self):
        (
            assessors,
            financial_specialists,
            allrounders,
            former_allrounders,
        ) = self.expiration_date_setup()
        url = (
            reverse("accounts:user-list")
            + "?min_date=2017-02-02"
            + "&roles=is_financial_specialist"
        )
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertSetEqual(
            {
                financial_specialists[1].user_id,
                allrounders[1].user_id,
                former_allrounders[0].user_id,
            },
            set([item["id"] for item in response_dict["results"]]),
        )

    def test_max_date_filter_financial_specialist(self):
        (
            assessors,
            financial_specialists,
            allrounders,
            former_allrounders,
        ) = self.expiration_date_setup()
        url = (
            reverse("accounts:user-list")
            + "?max_date=2017-02-02"
            + "&roles=is_financial_specialist"
        )
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertSetEqual(
            {
                financial_specialists[0].user_id,
                allrounders[0].user_id,
                former_allrounders[1].user_id,
            },
            set([item["id"] for item in response_dict["results"]]),
        )

    def test_min_max_date_filter_financial_specialist(self):
        (
            assessors,
            financial_specialists,
            allrounders,
            former_allrounders,
        ) = self.expiration_date_setup()
        url = (
            reverse("accounts:user-list")
            + "?min_date=2017-02-02"
            + "&max_date=2017-02-02"
            + "&roles=is_financial_specialist"
        )
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertSetEqual(
            set(), set([item["id"] for item in response_dict["results"]])
        )

    def test_is_active_filter_metadata(self):
        """
        The is_active metadata should not repeat
        """
        UserFactory.create_batch(2, is_active=True, is_superuser=True)
        UserFactory.create_batch(2, is_active=False, is_superuser=True)
        url = reverse("accounts:user-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertIn("is_active", response_dict["filters"])
        labels = [item["label"] for item in response_dict["filters"]["is_active"]]
        self.assertEqual(2, len(labels))

    def test_can_patch_self_with_portal_and_dashboard(self):
        self.jwt_user.allowed_frontends = '[{"app":"smartclient","name":"SCOPE App"},{"app":"dashboard","name":"SCOPE Dashboard"},{"app":"website","name":"SCOPE Portal"}]'
        self.jwt_user.save()
        url = reverse("accounts:user-detail", [self.jwt_user.id])
        data = {"language": "fr"}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(200, response.status_code)
        self.jwt_user.refresh_from_db()
        self.assertEqual("fr", self.jwt_user.language)

    def test_can_access_trainee_in_api(self):
        url = reverse("accounts:user-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset(
            {"can_access_trainee": False}, response_dict["results"][0]
        )

    def test_can_access_prod_employee(self):
        employee = EmployeeFactory.create(user__is_active=True)
        flush()
        url = reverse("accounts:user-detail", [employee.user_id])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset({"can_access_prod": True}, response_dict)

    def test_can_access_prod_inactive_employee(self):
        employee = EmployeeFactory.create(user__is_active=False)
        flush()
        url = reverse("accounts:user-detail", [employee.user_id])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset({"can_access_prod": False}, response_dict)

    def test_can_access_prod_customer_admin(self):
        contact = ContactFactory.create(
            user__is_active=True, access_to_dashboard="customer_admin"
        )
        flush()
        url = reverse("accounts:user-detail", [contact.user_id])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset({"can_access_prod": True}, response_dict)

    def test_can_access_prod_inactive_customer_admin(self):
        contact = ContactFactory.create(
            user__is_active=False, access_to_dashboard="customer_admin"
        )
        flush()
        url = reverse("accounts:user-detail", [contact.user_id])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset({"can_access_prod": False}, response_dict)

    def test_can_access_prod_customer_user(self):
        contact = ContactFactory.create(
            user__is_active=True, access_to_dashboard="customer_user"
        )
        flush()
        url = reverse("accounts:user-detail", [contact.user_id])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset({"can_access_prod": True}, response_dict)

    def test_can_access_prod_inactive_customer_user(self):
        contact = ContactFactory.create(
            user__is_active=False, access_to_dashboard="customer_user"
        )
        flush()
        url = reverse("accounts:user-detail", [contact.user_id])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset({"can_access_prod": False}, response_dict)

    def test_can_access_prod_assessor(self):
        assessor = AssessorFactory.create(
            is_assessor=True,
            user__is_active=True,
            expiration_date_assessor="2101-01-01",
        )
        flush()
        url = reverse("accounts:user-detail", [assessor.user_id])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset({"can_access_prod": True}, response_dict)

    def test_can_access_prod_inactive_assessor(self):
        assessor = AssessorFactory.create(
            is_assessor=True,
            user__is_active=False,
            expiration_date_assessor="2101-01-01",
        )
        flush()
        url = reverse("accounts:user-detail", [assessor.user_id])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset({"can_access_prod": False}, response_dict)

    def test_can_access_prod_expired_assessor(self):
        assessor = AssessorFactory.create(
            is_assessor=True,
            user__is_active=True,
            expiration_date_assessor="1901-01-01",
        )
        flush()
        url = reverse("accounts:user-detail", [assessor.user_id])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset({"can_access_prod": False}, response_dict)

    def test_can_access_prod_financial_specialist(self):
        financial_specialist = AssessorFactory.create(
            financial_specialist=True,
            user__is_active=True,
            expiration_date_financial_specialist="2101-01-01",
        )
        flush()
        url = reverse("accounts:user-detail", [financial_specialist.user_id])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset({"can_access_prod": True}, response_dict)

    def test_can_access_prod_inactive_financial_specialist(self):
        financial_specialist = AssessorFactory.create(
            financial_specialist=True,
            user__is_active=False,
            expiration_date_financial_specialist="2101-01-01",
        )
        flush()
        url = reverse("accounts:user-detail", [financial_specialist.user_id])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset({"can_access_prod": False}, response_dict)

    def test_can_access_prod_expired_financial_specialist(self):
        financial_specialist = AssessorFactory.create(
            financial_specialist=True,
            user__is_active=True,
            expiration_date_financial_specialist="1901-01-01",
        )
        flush()
        url = reverse("accounts:user-detail", [financial_specialist.user_id])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset({"can_access_prod": False}, response_dict)


class LoginTestCase(DenormMixin, EmployeeJWTTestCase):
    def test_can_not_login_to_app(self):
        """
        It should not be possible to log in to app
        """
        response = self.client.post(
            "/api-token-auth/",
            {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
            HTTP_ORIGIN="http://app.testserver",
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)

    def test_can_login_to_dashboard(self):
        """
        It should be possible to log in to dashboard
        """
        response = self.client.post(
            "/api-token-auth/",
            {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_login_to_portal(self):
        """
        It should be possible to log in to portal
        """
        response = self.client.post(
            "/api-token-auth/",
            {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
            HTTP_ORIGIN="http://portal.testserver",
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)


class ImpersonationTestCase(DenormMixin, EmployeeJWTTestCase):
    def test_valid_token_returned_customer_admin(self):
        """
        A valid token is returned to impersonate a customer admin
        """
        user = UserFactory.create(email="<EMAIL>")
        ContactFactory.create(user=user, access_to_dashboard="customer_admin")
        response = self.client.post(
            "/accounts/users/impersonate/",
            {"email": "<EMAIL>"},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("token", response_dict)

    def test_valid_token_returned_customer_user(self):
        """
        A valid token is returned to impersonate a customer user
        """
        user = UserFactory.create(email="<EMAIL>")
        ContactFactory.create(user=user, access_to_dashboard="customer_user")
        response = self.client.post(
            "/accounts/users/impersonate/",
            {"email": "<EMAIL>"},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("token", response_dict)

    def test_valid_token_returned_employee(self):
        """
        A valid token is returned to impersonate an employee
        """
        user = UserFactory.create(email="<EMAIL>")
        EmployeeFactory.create(user=user)
        response = self.client.post(
            "/accounts/users/impersonate/",
            {"email": "<EMAIL>"},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("token", response_dict)

    def test_valid_token_returned_qc(self):
        """
        A valid token is returned to impersonate an employee
        """
        user = UserFactory.create(email="<EMAIL>")
        EmployeeFactory.create(user=user, qc_only=True)
        response = self.client.post(
            "/accounts/users/impersonate/",
            {"email": "<EMAIL>"},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("token", response_dict)

    def test_cannot_impersonate_regular_contact(self):
        """
        Cannot impersonate user with no access to dashboard
        """
        user = UserFactory.create(email="<EMAIL>")
        ContactFactory.create(user=user)
        response = self.client.post(
            "/accounts/users/impersonate/",
            {"email": "<EMAIL>"},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual(["Invalid user to impersonate"], response_dict["email"])

    def test_cannot_impersonate_assessor(self):
        """
        Cannot impersonate user with no access to dashboard
        """
        user = UserFactory.create(email="<EMAIL>")
        AssessorFactory.create(user=user, is_assessor=True)
        response = self.client.post(
            "/accounts/users/impersonate/",
            {"email": "<EMAIL>"},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual(["Invalid user to impersonate"], response_dict["email"])

    def test_cannot_impersonate_fin_specialist(self):
        """
        Cannot impersonate user with no access to dashboard
        """
        user = UserFactory.create(email="<EMAIL>")
        AssessorFactory.create(user=user, is_assessor=True)
        response = self.client.post(
            "/accounts/users/impersonate/",
            {"email": "<EMAIL>"},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual(["Invalid user to impersonate"], response_dict["email"])

    def test_cannot_impersonate_user_that_does_not_exist(self):
        """
        Cannot impersonate user that does not exist
        """
        response = self.client.post(
            "/accounts/users/impersonate/",
            {"email": "<EMAIL>"},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual(["User does not exist"], response_dict["email"])

    def test_cannot_impersonate_inactive_user(self):
        """
        Cannot impersonate inactive user
        """
        user = UserFactory.create(email="<EMAIL>", is_active=False)
        ContactFactory.create(user=user, access_to_dashboard="customer_user")
        response = self.client.post(
            "/accounts/users/impersonate/",
            {"email": "<EMAIL>"},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual(["Invalid user to impersonate"], response_dict["email"])


class AppImpersonationTestCase(DenormMixin, EmployeeJWTTestCase):
    HTTP_ORIGIN = "http://app.testserver"
    with_assessor = True

    def test_can_impersonate_assessor(self):
        """
        Can impersonate assessor on app
        """
        user = UserFactory.create(email="<EMAIL>")
        AssessorFactory.create(user=user, is_assessor=True)
        response = self.client.post(
            "/accounts/users/impersonate/",
            {"email": "<EMAIL>"},
            HTTP_ORIGIN="http://app.testserver",
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("token", response_dict)

    def test_can_impersonate_fin_specialist(self):
        """
        Can impersonate financial specialist on app
        """
        user = UserFactory.create(email="<EMAIL>")
        AssessorFactory.create(user=user, is_assessor=True)
        response = self.client.post(
            "/accounts/users/impersonate/",
            {"email": "<EMAIL>"},
            HTTP_ORIGIN="http://app.testserver",
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("token", response_dict)
