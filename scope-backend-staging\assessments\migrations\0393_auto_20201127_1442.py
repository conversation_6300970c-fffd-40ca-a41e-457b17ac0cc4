# Generated by Django 2.1.15 on 2020-11-27 14:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0392_backfillproductinfo"),
    ]

    operations = [
        migrations.RenameField(
            model_name="assessment",
            old_name="terms_and_conditions_tab_completed",
            new_name="data_sharing_consent_tab_completed",
        ),
        migrations.RenameField(
            model_name="assessment",
            old_name="finance_tab_completed",
            new_name="finance_history_tab_completed",
        ),
        migrations.RenameField(
            model_name="assessment",
            old_name="financial_performance_tab_completed",
            new_name="finance_performance_tab_completed",
        ),
        migrations.RemoveField(
            model_name="assessment",
            name="governance_tab_completed",
        ),
        migrations.RemoveField(
            model_name="assessment",
            name="monthly_production_tab_completed",
        ),
        migrations.AddField(
            model_name="assessment",
            name="finance_overview_tab_completed",
            field=models.Bo<PERSON>anField(default=False),
        ),
    ]
