from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("hrm", "0013_assessor_second_region"),
        ("assessments", "0129_auto_20150701_1638"),
    ]

    operations = [
        migrations.CreateModel(
            name="Executive",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("accepted", models.BooleanField(default=False)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("name", models.TextField()),
                ("function", models.TextField(blank=True)),
                (
                    "male_female",
                    models.CharField(
                        blank=True,
                        max_length=1,
                        choices=[("m", "male"), ("f", "female")],
                    ),
                ),
                (
                    "years_in_function",
                    models.DecimalField(
                        null=True, max_digits=5, decimal_places=2, blank=True
                    ),
                ),
                (
                    "years_in_organization",
                    models.DecimalField(
                        null=True, max_digits=5, decimal_places=2, blank=True
                    ),
                ),
                (
                    "years_in_sector",
                    models.DecimalField(
                        null=True, max_digits=5, decimal_places=2, blank=True
                    ),
                ),
                (
                    "year_of_birth",
                    models.PositiveSmallIntegerField(null=True, blank=True),
                ),
                (
                    "end_of_term",
                    models.PositiveSmallIntegerField(null=True, blank=True),
                ),
                ("shareholder", models.NullBooleanField()),
                ("qualifications", models.TextField(blank=True)),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE, to="assessments.Assessment"
                    ),
                ),
                (
                    "quality_controller",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="executives_quality_controlled",
                        blank=True,
                        to="hrm.Employee",
                        null=True,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Governance",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("accepted", models.BooleanField(default=False)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("name", models.TextField()),
                ("present", models.NullBooleanField()),
                (
                    "number_of_people",
                    models.PositiveIntegerField(null=True, blank=True),
                ),
                ("description", models.TextField(blank=True)),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="governance_set",
                        to="assessments.Assessment",
                    ),
                ),
                (
                    "quality_controller",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="governances_quality_controlled",
                        blank=True,
                        to="hrm.Employee",
                        null=True,
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="NonExecutive",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("accepted", models.BooleanField(default=False)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("name", models.TextField()),
                ("function", models.TextField(blank=True)),
                (
                    "male_female",
                    models.CharField(
                        blank=True,
                        max_length=1,
                        choices=[("m", "male"), ("f", "female")],
                    ),
                ),
                (
                    "years_in_function",
                    models.DecimalField(
                        null=True, max_digits=5, decimal_places=2, blank=True
                    ),
                ),
                (
                    "years_in_organization",
                    models.DecimalField(
                        null=True, max_digits=5, decimal_places=2, blank=True
                    ),
                ),
                (
                    "years_in_sector",
                    models.DecimalField(
                        null=True, max_digits=5, decimal_places=2, blank=True
                    ),
                ),
                (
                    "year_of_birth",
                    models.PositiveSmallIntegerField(null=True, blank=True),
                ),
                (
                    "end_of_term",
                    models.PositiveSmallIntegerField(null=True, blank=True),
                ),
                ("shareholder", models.NullBooleanField()),
                ("qualifications", models.TextField(blank=True)),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE, to="assessments.Assessment"
                    ),
                ),
                (
                    "quality_controller",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="nonexecutives_quality_controlled",
                        blank=True,
                        to="hrm.Employee",
                        null=True,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.AlterUniqueTogether(
            name="governance", unique_together=set([("assessment", "name")])
        ),
    ]
