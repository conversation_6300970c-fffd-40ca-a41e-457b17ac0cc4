from django.db import migrations


def concat_purposes(apps, schema_editor):
    LoanRequirement = apps.get_model("assessments", "LoanRequirement")
    for loanrequirement in LoanRequirement.objects.all():
        loanrequirement.purpose = ", ".join(
            [item.name for item in loanrequirement.purposes.all()]
        )
        loanrequirement.description = ", ".join(
            [item.description for item in loanrequirement.purposes.all()]
        )
        loanrequirement.save()


class Migration(migrations.Migration):
    initial = True

    dependencies = [("assessments", "0189_auto_20160826_0921")]

    operations = [migrations.RunPython(concat_purposes)]
