# Generated by Django 1.10.5 on 2017-09-01 08:45


import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("assessments", "0285_remove_assessment_display_quality_reviewer"),
    ]

    operations = [
        migrations.AddField(
            model_name="assessment",
            name="display_quality_reviewer",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        )
    ]
