import datetime
import json

import pytz
import requests
import tablib
from deepdiff import DeepDiff
from denorm import denormalized, depend_on
from django.conf import settings
from django.contrib.postgres.fields import ArrayField
from django.core.mail import EmailMultiAlternatives
from django.db import models
from django.db.models import Max, Q
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.template.loader import render_to_string
from django.utils import translation
from httpsig.requests_auth import HTTPSignatureAuth
from rest_framework.exceptions import PermissionDenied
from rest_framework.renderers import J<PERSON><PERSON>enderer

from customers.models import Customer
from libs.frontend_helpers import preferred_frontend
from libs.generic_helpers import rec_merge, uuid4_as_str
from libs.models_helpers import AddressMixin, BaseModel

from .helpers import UnlimitedUsernameEmailAbstractUser


class User(AddressMixin, UnlimitedUsernameEmailAbstractUser):
    language = models.CharField(max_length=2, choices=settings.LANGUAGES, default="en")
    organization = models.ForeignKey(
        Customer, related_name="users", on_delete=models.PROTECT
    )
    phone_number = models.TextField(blank=True)
    modified_at = models.DateTimeField(auto_now=True)
    allowed_frontends = models.TextField(blank=True)
    email_notifications_on = models.BooleanField(default=True)
    languages_spoken = ArrayField(
        models.CharField(max_length=2, choices=settings.LANGUAGES, default="en"),
        blank=True,
        null=True,
    )
    can_access_trainee = models.BooleanField(default=False)
    training_id = models.IntegerField(blank=True, null=True)
    display_in_list = models.BooleanField(default=True)
    is_self_registered = models.BooleanField(default=False)

    @denormalized(models.TextField, blank=True)
    @depend_on("first_name")
    @depend_on("last_name")
    def full_name(self):
        return self.get_full_name()

    @property
    def _groups(self):
        if not hasattr(self, "_cached_groups"):
            self._cached_groups = list(self.groups.all().values_list("name", flat=True))
        return self._cached_groups

    @denormalized(models.BooleanField, default=False)
    @depend_on("employee__qc_only")
    @depend_on("employee__is_active")
    def is_employee(self):
        from hrm.models import Employee

        return Employee.objects.filter(
            user=self, qc_only=False, is_active=True
        ).exists()

    @denormalized(models.BooleanField, default=False)
    @depend_on("employee__qc_only")
    @depend_on("employee__is_active")
    def is_quality_reviewer(self):
        from hrm.models import Employee

        return Employee.objects.filter(user=self, qc_only=True, is_active=True).exists()

    @denormalized(models.BooleanField, default=False)
    @depend_on("customer_contact__access_to_dashboard")
    @depend_on("customer_contact__is_active")
    def is_contact(self):
        from customers.models import Contact

        return (
            Contact.objects.filter(user=self, is_active=True)
            .filter(access_to_dashboard="")
            .exists()
        )

    @denormalized(models.BooleanField, default=False)
    @depend_on("customer_contact__access_to_dashboard")
    @depend_on("customer_contact__is_active")
    def is_customer_admin(self):
        from customers.models import Contact

        return (
            Contact.objects.filter(user=self, is_active=True)
            .filter(access_to_dashboard="customer_admin")
            .exists()
        )

    @denormalized(models.BooleanField, default=False)
    @depend_on("customer_contact__access_to_dashboard")
    @depend_on("customer_contact__is_active")
    def is_customer_user(self):
        from customers.models import Contact

        return (
            Contact.objects.filter(user=self, is_active=True)
            .filter(access_to_dashboard="customer_user")
            .exists()
        )

    @denormalized(models.BooleanField, default=False)
    @depend_on("assessor__is_assessor")
    @depend_on("assessor__is_active")
    def is_assessor(self):
        from hrm.models import Assessor

        return Assessor.objects.filter(
            user=self, is_assessor=True, is_active=True
        ).exists()

    @denormalized(models.BooleanField, default=False)
    @depend_on("assessor__data_collector")
    @depend_on("assessor__is_active")
    def is_data_collector(self):
        from hrm.models import Assessor

        return Assessor.objects.filter(
            user=self, data_collector=True, is_active=True
        ).exists()

    @denormalized(models.BooleanField, default=False)
    @depend_on("assessor__self_assessor")
    @depend_on("assessor__is_active")
    def is_self_assessor(self):
        from hrm.models import Assessor

        return Assessor.objects.filter(
            user=self, self_assessor=True, is_active=True
        ).exists()

    @denormalized(models.BooleanField, default=False)
    @depend_on("assessor__financial_specialist")
    @depend_on("assessor__is_active")
    def is_financial_specialist(self):
        from hrm.models import Assessor

        return Assessor.objects.filter(
            user=self, financial_specialist=True, is_active=True
        ).exists()

    @denormalized(models.BooleanField, default=False)
    @depend_on("is_employee")
    @depend_on("is_customer_admin")
    def can_create_projects(self):
        if self.is_employee or self.is_customer_admin:
            return True
        else:
            return False

    @denormalized(models.DateField, null=True, blank=True)
    @depend_on("portal_licenses__certification_valid_until")
    @depend_on("portal_licenses__user_id")
    def expiration_date_portal_license(self):
        return self.portal_licenses.all().aggregate(
            value=Max("certification_valid_until")
        )["value"]

    class Meta:
        ordering = ("last_name", "first_name")

    def remove_from_training(self):
        if settings.REMOVE_FROM_TRAINING_URL is None:
            raise PermissionDenied(
                "Not allowed to remove from training from this server"
            )

        if not self.training_id:
            return None

        secret = open(
            settings.SIGNATURE_AUTH_PRIVKEYS["UserReceiveViewSet"], "rb"
        ).read()
        auth = HTTPSignatureAuth(
            key_id="default", secret=secret, algorithm="rsa-sha256", headers=("date",)
        )
        return requests.post(
            settings.REMOVE_FROM_TRAINING_URL,
            JSONRenderer().render({"id": self.training_id, "email": self.email}),
            auth=auth,
            headers={
                "date": datetime.datetime.now(pytz.utc).isoformat(),
                "content-type": "application/json",
            },
        )

    def send_to_training(self, overrides=None, only_fields=None):
        from accounts.serializers import (
            SendToTrainingResponseUserSerializer,
            SendToTrainingUserSerializer,
        )

        if settings.SEND_TO_TRAINING_URL is None:
            raise PermissionDenied("Not allowed to send to training from this server")
        data = SendToTrainingUserSerializer(self).data
        if self.is_assessor or self.is_financial_specialist:
            future_date = (
                datetime.date.today() + datetime.timedelta(days=365)
            ).isoformat()
            data = rec_merge(
                data,
                {
                    "assessor": {
                        "expiration_date_assessor": future_date,
                        "expiration_date_financial_specialist": future_date,
                    }
                },
            )
        if overrides:
            data = rec_merge(data, overrides)
        if only_fields:
            data = {key: value for (key, value) in data.items() if key in only_fields}
        secret = open(
            settings.SIGNATURE_AUTH_PRIVKEYS["UserReceiveViewSet"], "rb"
        ).read()
        auth = HTTPSignatureAuth(
            key_id="default", secret=secret, algorithm="rsa-sha256", headers=("date",)
        )
        response = requests.post(
            settings.SEND_TO_TRAINING_URL,
            JSONRenderer().render(data),
            auth=auth,
            headers={
                "date": datetime.datetime.now(pytz.utc).isoformat(),
                "content-type": "application/json",
            },
        )
        if response.status_code in (200, 201):
            response_serializer = SendToTrainingResponseUserSerializer(
                self, json.loads(response.content)
            )
            response_serializer.is_valid(raise_exception=True)
            response_serializer.save()
        return response


User._meta.get_field("password").blank = True


class PasswordResetRequest(BaseModel):
    user = models.OneToOneField(
        User, related_name="password_reset_request", on_delete=models.CASCADE
    )
    uuid = models.CharField(max_length=36, default=uuid4_as_str, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def send_email_notification(self, app=None):
        """
        Send email notification of message
        """
        if app is None or app != settings.FRONTEND_PORTAL:
            app = preferred_frontend(self.user)
            # Text version first
        with translation.override(self.user.language):
            mail = EmailMultiAlternatives(
                subject=render_to_string(
                    "accounts/email_password_reset_request_subject.txt"
                ).strip(),
                body=render_to_string(
                    "accounts/email_password_reset_request.txt",
                    {"instance": self, "frontend_host": app},
                ),
                from_email=settings.MESSAGING_NOTIFICATION_FROM_EMAIL,
                to=[self.user.email],
                headers={"Reply-To": "<EMAIL>"},
            )

        if app == settings.FRONTEND_PORTAL:
            # Attach html version
            mail.attach_alternative(
                render_to_string(
                    "accounts/email_password_reset_request_portal.html",
                    {
                        "instance": self,
                        "host": settings.MESSAGING_NOTIFICATION_STATIC_HOST,
                        "frontend_host": app,
                    },
                ),
                "text/html",
            )
        else:
            # Attach html version
            mail.attach_alternative(
                render_to_string(
                    "accounts/email_password_reset_request.html",
                    {
                        "instance": self,
                        "host": settings.MESSAGING_NOTIFICATION_STATIC_HOST,
                        "frontend_host": app,
                    },
                ),
                "text/html",
            )
        mail.send()


@receiver(pre_save, sender=User)
def auto_sync_user_details(instance, **kwargs):
    from accounts.serializers import SendToTrainingUserSerializer

    if instance.pk:
        db_instance = User.objects.get(pk=instance.pk)
        instance._old_data = SendToTrainingUserSerializer(db_instance).data


@receiver(post_save, sender=User)
def post_save_auto_sync_user_details(instance, **kwargs):
    from accounts.serializers import SendToTrainingUserSerializer

    new_data = SendToTrainingUserSerializer(instance).data
    if settings.ALLOW_SEND_TO_TRAINING and instance.can_access_trainee:
        if (
            DeepDiff(
                getattr(instance, "_old_data", {}),
                new_data,
                ignore_order=True,
                exclude_paths={"root['training_id']"},
            )
            != {}
        ):
            instance.send_to_training(
                only_fields=[
                    "training_id",
                    "email",
                    "password",
                    "first_name",
                    "last_name",
                    "language",
                    "organization",
                    "phone_number",
                    "allowed_frontends",
                    "languages_spoken",
                    "street",
                    "street_number",
                    "zipcode",
                    "city",
                    "region",
                    "region_iso",
                    "country",
                    "global_region",
                    "second_region",
                    "display_in_list",
                ]
            )


@receiver(post_save, sender=User)
def create_password_reset_request(instance, created, **kwargs):
    if created:
        try:
            allowed_frontends = json.loads(instance.allowed_frontends)
        except Exception:
            allowed_frontends = []
        try:
            allowed_frontends = [item["app"] for item in allowed_frontends]
        except Exception:
            allowed_frontends = []

        has_portal_access = "website" in allowed_frontends
        if has_portal_access:
            (PasswordResetRequest.objects.get_or_create(user=instance))
