from django.db import migrations, models

import libs.field_helpers


class Migration(migrations.Migration):

    dependencies = [
        ("customers", "0011_customer_image"),
        ("assessments", "0034_auto_20141231_1310"),
    ]

    operations = [
        migrations.CreateModel(
            name="EnablingPlayer",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("name", models.TextField()),
                ("description_of_relation", models.TextField(blank=True)),
                (
                    "number_of_years_in_relation",
                    models.PositiveSmallIntegerField(null=True, blank=True),
                ),
                ("contract_in_place", models.BooleanField(default=False)),
                (
                    "contract_start_year",
                    models.PositiveSmallIntegerField(null=True, blank=True),
                ),
                (
                    "contract_end_year",
                    models.PositiveSmallIntegerField(null=True, blank=True),
                ),
                ("description_of_agreement", models.TextField(blank=True)),
                (
                    "contact",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="enablingplayers",
                        to="customers.Contact",
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="OrganizationType",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("name", libs.field_helpers.LowerCaseTextField(unique=True)),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="RelationType",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("name", libs.field_helpers.LowerCaseTextField(unique=True)),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="ValueChainPlayer",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("name", models.TextField()),
                ("description_of_relation", models.TextField(blank=True)),
                (
                    "number_of_years_in_relation",
                    models.PositiveSmallIntegerField(null=True, blank=True),
                ),
                ("contract_in_place", models.BooleanField(default=False)),
                (
                    "contract_start_year",
                    models.PositiveSmallIntegerField(null=True, blank=True),
                ),
                (
                    "contract_end_year",
                    models.PositiveSmallIntegerField(null=True, blank=True),
                ),
                ("description_of_agreement", models.TextField(blank=True)),
                (
                    "contact",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="valuechainplayers",
                        to="customers.Contact",
                    ),
                ),
                (
                    "relation_to_producing_organization",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="valuechainplayers",
                        to="assessments.RelationType",
                    ),
                ),
                (
                    "type",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="valuechainplayers",
                        to="assessments.OrganizationType",
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name="enablingplayer",
            name="relation_to_producing_organization",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="enablingplayers",
                to="assessments.RelationType",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="enablingplayer",
            name="type",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="enablingplayers",
                to="assessments.OrganizationType",
            ),
            preserve_default=True,
        ),
    ]
