from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("hrm", "0007_assessor_customer"),
        ("assessments", "0056_auto_20150122_0954"),
    ]

    operations = [
        migrations.CreateModel(
            name="BalanceSheetComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "balancesheet",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.BalanceSheet",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="balancesheetcomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="BankAccountComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "bankaccount",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.BankAccount",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="bankaccountcomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="BasicFinancialInfoComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "basicfinancialinfo",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.BasicFinancialInfo",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="basicfinancialinfocomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="CashFlowStatementComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "cashflowstatement",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.CashFlowStatement",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="cashflowstatementcomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="DocumentAvailabilityResponseComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "documentavailabilityresponse",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.DocumentAvailabilityResponse",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="documentavailabilityresponsecomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="EnablingPlayerComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "enablingplayer",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.EnablingPlayer",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="enablingplayercomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="FinancialRatioComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "financialratio",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.FinancialRatio",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="financialratiocomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="FinancialScoresComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "financialscores",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.FinancialScores",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="financialscorescomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="GeneralChecksComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "generalchecks",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.GeneralChecks",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="generalcheckscomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="GrantHistoryComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "granthistory",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.GrantHistory",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="granthistorycomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="InputPurchaseComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "inputpurchase",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.InputPurchase",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="inputpurchasecomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="LoanHistoryComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "loanhistory",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.LoanHistory",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="loanhistorycomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="ProducingOrganizationDetailsComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "producingorganizationdetails",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.ProducingOrganizationDetails",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="producingorganizationdetailscomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="ProductComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.Product",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="productcomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="ProductionFigureComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "productionfigure",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.ProductionFigure",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="productionfigurecomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="ProductionPurchaseComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "productionpurchase",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.ProductionPurchase",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="productionpurchasecomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="ProductionSaleComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "productionsale",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.ProductionSale",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="productionsalecomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="ProfitLossStatementComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "profitlossstatement",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.ProfitLossStatement",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="profitlossstatementcomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="RatioScoresComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "ratioscores",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.RatioScores",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="ratioscorescomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="ValueChainPlayerComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="valuechainplayercomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "valuechainplayer",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.ValueChainPlayer",
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.RenameField(
            model_name="balancesheet",
            old_name="comments",
            new_name="accountant_comments",
        ),
        migrations.RenameField(
            model_name="cashflowstatement",
            old_name="comments",
            new_name="accountant_comments",
        ),
        migrations.RenameField(
            model_name="profitlossstatement",
            old_name="comments",
            new_name="accountant_comments",
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="balancesheets_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="bankaccount",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="bankaccount",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="bankaccounts_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="basicfinancialinfo",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="basicfinancialinfo",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="basicfinancialinfos_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="cashflowstatements_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="enablingplayer",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="enablingplayer",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="enablingplayers_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="financialratio",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="financialratio",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="financialratios_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="financialscores",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="financialscores",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="financialscoress_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="generalchecks",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="generalchecks",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="generalcheckss_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="granthistory",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="granthistory",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="granthistorys_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="inputpurchase",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="inputpurchase",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="inputpurchases_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="loanhistory",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="loanhistory",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="loanhistorys_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="producingorganizationdetailss_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="product",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="product",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="products_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="productionfigure",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="productionfigure",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="productionfigures_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="productionpurchase",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="productionpurchase",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="productionpurchases_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="productionsale",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="productionsale",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="productionsales_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="profitlossstatements_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="ratioscoress_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="valuechainplayer",
            name="accepted",
            field=models.BooleanField(default=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="valuechainplayer",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="valuechainplayers_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
    ]
