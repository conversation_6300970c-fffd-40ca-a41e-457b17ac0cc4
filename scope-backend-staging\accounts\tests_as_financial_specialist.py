import json
from unittest import skip

from denorm import flush
from django.contrib.auth.models import Group
from django.test import TestCase
from freezegun import freeze_time
from rest_framework import status
from rest_framework.reverse import reverse

from accounts.factories import UserFactory
from customers.factories import ContactFactory, RepresentativeFactory
from hrm.factories import AssessorTrainingFactory, FinancialSpecialistTrainingFactory
from hrm.models import Assessor
from libs.test_helpers import DenormMixin, FinancialSpecialistJWTTestCase


class UserTestCase(DenormMixin, FinancialSpecialistJWTTestCase):
    def test_role_booleans_work(self):
        """
        The role booleans should be set correctly
        """
        url = reverse("accounts:user-me")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset(
            {
                "is_assessor": False,
                "is_financial_specialist": True,
                "is_employee": False,
                "is_quality_reviewer": False,
                "is_contact": False,
            },
            response_dict,
        )


class LoginTestCase(DenormMixin, TestCase):
    USER_EMAIL = "<EMAIL>"
    USER_PASSWORD = "jwt"
    HTTP_ORIGIN = "http://testserver"

    def setUp(self):
        self.jwt_user = UserFactory.create(email=self.USER_EMAIL, language="en")
        self.jwt_user.set_password(self.USER_PASSWORD)
        self.jwt_user.is_superuser = False
        self.jwt_user.save()
        Group.objects.get_or_create(name="assessors")
        self.assessor = Assessor.objects.create(
            user=self.jwt_user, financial_specialist=True
        )
        super().setUp()

    def test_can_login_to_app(self):
        """
        It should be possible to log in to app
        """
        me = self.jwt_user
        assessor = Assessor.objects.get(user=me)
        assessor.financial_specialist = True
        assessor.save()
        FinancialSpecialistTrainingFactory.create(
            assessor=assessor, certification_valid_until="9999-01-01"
        )
        flush()
        me.refresh_from_db()
        assessor.refresh_from_db()
        with freeze_time("2019-01-01"):
            response = self.client.post(
                "/api-token-auth/",
                {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
                HTTP_ORIGIN="http://app.testserver",
            )
            self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_fin_spec_and_not_assessor_can_not_login_to_app_with_expired_account(self):
        """
        User with expired assessor and VALID financial specialist accounts can log in
        """
        me = self.jwt_user
        assessor = Assessor.objects.get(user=me)
        assessor.is_assessor = False
        assessor.financial_specialist = True
        assessor.save()
        AssessorTrainingFactory.create(
            assessor=assessor, certification_valid_until="1900-01-01"
        )
        FinancialSpecialistTrainingFactory.create(
            assessor=assessor, certification_valid_until="9999-01-01"
        )
        flush()
        me.refresh_from_db()
        assessor.refresh_from_db()
        with freeze_time("2019-01-01"):
            response = self.client.post(
                "/api-token-auth/",
                {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
                HTTP_ORIGIN="http://app.testserver",
            )
            self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_not_login_to_dashboard(self):
        """
        It should not be possible to log in to dashboard
        """
        response = self.client.post(
            "/api-token-auth/",
            {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)

    @skip("No portal anymore")
    def test_can_not_login_to_portal(self):
        """
        It should not be possible to log in to portal
        """
        response = self.client.post(
            "/api-token-auth/",
            {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
            HTTP_ORIGIN="http://portal.testserver",
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)


class ImpersonationTestCase(DenormMixin, FinancialSpecialistJWTTestCase):
    def test_only_employee_can_impersonate(self):
        """
        Financial specialist cannot impersonate, because has no access to dashboard
        """
        user = UserFactory.create(email="<EMAIL>")
        ContactFactory.create(user=user, access_to_dashboard="customer_admin")
        response = self.client.post(
            "/accounts/users/impersonate/",
            {"email": "<EMAIL>"},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_401_UNAUTHORIZED, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual("Invalid payload.", response_dict["detail"])
