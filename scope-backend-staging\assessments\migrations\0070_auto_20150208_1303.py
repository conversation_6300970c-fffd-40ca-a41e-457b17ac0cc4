from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0069_auto_20150208_1022")]

    operations = [
        migrations.AddField(
            model_name="financialscore",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="asset_turnover",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="financialscores7",
                null=True,
                blank=True,
                to="assessments.FinancialScore",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="current_ratio",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="financialscores13",
                null=True,
                blank=True,
                to="assessments.FinancialScore",
            ),
            preserve_default=True,
        ),
        migrations.Alter<PERSON>ield(
            model_name="financialscores",
            name="days_inventory_outstanding",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="financialscores15",
                null=True,
                blank=True,
                to="assessments.FinancialScore",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="days_sales_outstanding",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="financialscores16",
                null=True,
                blank=True,
                to="assessments.FinancialScore",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="debt_coverage_ratio",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="financialscores11",
                null=True,
                blank=True,
                to="assessments.FinancialScore",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="debt_servicing",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="financialscores12",
                null=True,
                blank=True,
                to="assessments.FinancialScore",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="debt_to_assets_ratio",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="financialscores9",
                null=True,
                blank=True,
                to="assessments.FinancialScore",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="debt_to_equity_ratio",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="financialscores10",
                null=True,
                blank=True,
                to="assessments.FinancialScore",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="gross_margin",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="financialscores2",
                null=True,
                blank=True,
                to="assessments.FinancialScore",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="net_profit_growth",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="financialscores1",
                null=True,
                blank=True,
                to="assessments.FinancialScore",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="net_profit_margin",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="financialscores4",
                null=True,
                blank=True,
                to="assessments.FinancialScore",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="operating_profit_margin",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="financialscores3",
                null=True,
                blank=True,
                to="assessments.FinancialScore",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="quick_ratio",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="financialscores14",
                null=True,
                blank=True,
                to="assessments.FinancialScore",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="return_on_assets",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="financialscores6",
                null=True,
                blank=True,
                to="assessments.FinancialScore",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="return_on_equity",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="financialscores5",
                null=True,
                blank=True,
                to="assessments.FinancialScore",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="revenue_growth",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="financialscores0",
                null=True,
                blank=True,
                to="assessments.FinancialScore",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialscores",
            name="working_capital_turnover",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="financialscores8",
                null=True,
                blank=True,
                to="assessments.FinancialScore",
            ),
            preserve_default=True,
        ),
    ]
