from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0122_assessmentfunder")]

    operations = [
        migrations.RemoveField(model_name="generalcheck", name="available"),
        migrations.AddField(
            model_name="generalcheck",
            name="available",
            field=models.PositiveSmallIntegerField(
                blank=True,
                null=True,
                choices=[
                    (0, "not available / not good"),
                    (1, "there is a potential issue / pay attention"),
                    (2, "no issue, is available"),
                ],
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="generalcheck",
            name="quality",
            field=models.PositiveSmallIntegerField(
                blank=True,
                null=True,
                choices=[
                    (0, "not available / not good"),
                    (1, "there is a potential issue / pay attention"),
                    (2, "no issue, is available"),
                ],
            ),
            preserve_default=True,
        ),
    ]
