import datetime
import json
import os
import shutil
from tempfile import mkdtemp
from unittest import skip

import factory
import polib
import pytz
from denorm import flush
from django.conf import settings
from django.core import mail
from django.db import transaction
from django.db.models.signals import post_save
from django.db.utils import IntegrityError
from freezegun import freeze_time
from mock import patch
from rest_framework import status
from rest_framework.reverse import reverse

from accounts.factories import UserFactory
from assessments.factories import (
    AssessmentAssignmentFactory,
    AssessmentFactory,
    AssessmentInvitationFactory,
    ResponseFactory,
    SubResponseFactory,
)
from assessments.models import (
    Assessment,
    AssessmentAssignment,
    AssessmentInvitation,
    Response,
    SubResponse,
)
from assessments.serializers import AssessmentInvitationSerializer
from customers.factories import CustomerFactory, ProducingOrganizationFactory
from hrm.factories import AssessorFactory, EmployeeFactory
from libs.test_helpers import DenormMixin, EmployeeJWTTestCase
from messaging.factories import MessageTypeFactory
from products.factories import QuestionFactory, SubQuestionFactory, ToolFactory
from projects.factories import ProjectFactory
from reports.factories import ReportFactory
from reports.models import Report


class AssessmentTestCase(DenormMixin, EmployeeJWTTestCase):
    maxDiff = None

    def test_can_not_edit_tool_field(self):
        """
        It should be impossible to edit the tool field of an existing
        assessment
        """
        assessment = AssessmentFactory.create()
        new_tool = ToolFactory.create()
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {"tool": reverse("products:tool-detail", [new_tool.pk])}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual(["This field is read only"], response_dict["tool"])
        db_assessment = Assessment.objects.get(pk=assessment.pk)
        self.assertEqual(assessment.tool, db_assessment.tool)

    def test_304_works(self):
        """
        If nothing changed, client should get 304 response
        """
        with freeze_time("2016-01-01 00:00:00"):
            AssessmentFactory.create()
        url = reverse("assessments:assessment-list")

        with freeze_time("2016-01-01 00:00:01"):
            modified_since = datetime.datetime.now(pytz.utc).strftime(
                "%a, %d %b %Y %H:%M:%S GMT"
            )
        long_ago = datetime.datetime(2000, 1, 1, 1, 1, 1, 1, pytz.utc)
        with self.settings(LAST_BREAKING_CODE_CHANGE=long_ago):
            response = self.client.get(
                url,
                content_type="application/json",
                HTTP_IF_MODIFIED_SINCE=modified_since,
            )
        self.assertEqual(status.HTTP_304_NOT_MODIFIED, response.status_code)

    def test_304_works_with_create(self):
        """
        If nothing changed, client should get 304 response, when something is
        created, 200 again
        """
        with freeze_time("2016-01-01 00:00:00"):
            AssessmentFactory.create()
        url = reverse("assessments:assessment-list")

        with freeze_time("2016-01-01 00:00:01"):
            modified_since = datetime.datetime.now(pytz.utc).strftime(
                "%a, %d %b %Y %H:%M:%S GMT"
            )
        with freeze_time("2016-01-01 00:00:02"):
            AssessmentFactory.create()
        response = self.client.get(
            url, content_type="application/json", HTTP_IF_MODIFIED_SINCE=modified_since
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_304_works_with_delete(self):
        """
        If nothing changed, client should get 304 response, when something is
        deleted, 200 again
        """
        create_time = settings.LAST_BREAKING_CODE_CHANGE + datetime.timedelta(seconds=1)
        modified_since_time_0 = settings.LAST_BREAKING_CODE_CHANGE + datetime.timedelta(
            seconds=2
        )
        delete_time = settings.LAST_BREAKING_CODE_CHANGE + datetime.timedelta(seconds=3)
        modified_since_time_1 = settings.LAST_BREAKING_CODE_CHANGE + datetime.timedelta(
            seconds=4
        )
        with freeze_time(create_time):
            assessments = AssessmentFactory.create_batch(2)
            flush()
        url = reverse("assessments:assessment-list")

        with freeze_time(create_time):
            modified_since = datetime.datetime.now(pytz.utc).strftime(
                "%a, %d %b %Y %H:%M:%S GMT"
            )

        with freeze_time(modified_since_time_0):
            modified_since = datetime.datetime.now(pytz.utc).strftime(
                "%a, %d %b %Y %H:%M:%S GMT"
            )
        with freeze_time(delete_time):
            detail_url = reverse("assessments:assessment-detail", [assessments[0].pk])
            self.client.delete(detail_url, content_type="application/json")
        response = self.client.get(
            url, content_type="application/json", HTTP_IF_MODIFIED_SINCE=modified_since
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        with freeze_time(modified_since_time_1):
            modified_since = datetime.datetime.now(pytz.utc).strftime(
                "%a, %d %b %Y %H:%M:%S GMT"
            )
        response = self.client.get(
            url, content_type="application/json", HTTP_IF_MODIFIED_SINCE=modified_since
        )
        self.assertEqual(status.HTTP_304_NOT_MODIFIED, response.status_code)

    def test_accept_all_responses_works(self):
        """
        accept_all_responses should set all responses to accepted=True
        """
        tool = ToolFactory.create()
        questions = QuestionFactory.create_batch(2, section__tool=tool)
        for question in questions:
            SubQuestionFactory.create_batch(2, question=question)
        assessment = AssessmentFactory.create(tool=tool)
        for question in questions:
            response = ResponseFactory.create(
                assessment=assessment, question=question, accepted=False
            )
            for subquestion in question.subquestions.all():
                SubResponseFactory.create(
                    assessment=assessment, subquestion=subquestion, _response=response
                )
        url = reverse("assessments:assessment-accept-all-responses", [assessment.pk])
        self.client.post(url, content_type="application/json")
        self.assertListEqual(
            [True],
            list(
                Response.objects.all()
                .order_by()
                .values_list("accepted", flat=True)
                .distinct()
            ),
        )
        self.assertListEqual(
            [True],
            list(
                SubResponse.objects.all()
                .order_by()
                .values_list("accepted", flat=True)
                .distinct()
            ),
        )

    def test_decline_all_responses_works(self):
        """
        decline_all_responses should set all responses to accepted=True
        """
        tool = ToolFactory.create()
        questions = QuestionFactory.create_batch(2, section__tool=tool)
        for question in questions:
            SubQuestionFactory.create_batch(2, question=question)
        assessment = AssessmentFactory.create(tool=tool)
        for question in questions:
            response = ResponseFactory.create(
                assessment=assessment, question=question, accepted=True
            )
            for subquestion in question.subquestions.all():
                SubResponseFactory.create(
                    assessment=assessment, subquestion=subquestion, _response=response
                )
        url = reverse("assessments:assessment-decline-all-responses", [assessment.pk])
        self.client.post(url, content_type="application/json")
        self.assertListEqual(
            [False],
            list(
                Response.objects.all()
                .order_by()
                .values_list("accepted", flat=True)
                .distinct()
            ),
        )
        self.assertListEqual(
            [False],
            list(
                SubResponse.objects.all()
                .order_by()
                .values_list("accepted", flat=True)
                .distinct()
            ),
        )

    def test_all_accepted_in_api(self):
        """
        all_accepted should be True when all responses and subresponses have
        been accepted, False otherwise
        """
        tool = ToolFactory.create()
        questions = QuestionFactory.create_batch(2, section__tool=tool)
        for question in questions:
            SubQuestionFactory.create_batch(2, question=question)
        assessment = AssessmentFactory.create(tool=tool)
        for question in questions:
            response = ResponseFactory.create(
                assessment=assessment, question=question, accepted=True
            )
            for subquestion in question.subquestions.all():
                SubResponseFactory.create(
                    assessment=assessment, subquestion=subquestion, _response=response
                )
        flush()
        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("all_accepted", response_dict)
        self.assertTrue(response_dict["all_accepted"])
        response = Response.objects.all()[0]
        response.accepted = False
        response.save()
        flush()
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("all_accepted", response_dict)
        self.assertFalse(response_dict["all_accepted"])

    def test_all_declined_in_api(self):
        """
        all_declined should be True when all responses and subresponses have
        been declined, False otherwise
        """
        tool = ToolFactory.create()
        questions = QuestionFactory.create_batch(2, section__tool=tool)
        for question in questions:
            SubQuestionFactory.create_batch(2, question=question)
        assessment = AssessmentFactory.create(tool=tool)
        for question in questions:
            response = ResponseFactory.create(
                assessment=assessment, question=question, accepted=False
            )
            for subquestion in question.subquestions.all():
                SubResponseFactory.create(
                    assessment=assessment, subquestion=subquestion, _response=response
                )
        flush()
        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("all_declined", response_dict)
        self.assertTrue(response_dict["all_declined"])
        response = Response.objects.all()[0]
        response.accepted = True
        response.save()
        flush()
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("all_declined", response_dict)
        self.assertFalse(response_dict["all_declined"])

    def test_assessment_status_update(self):
        """
        When assessment status changes "assessment.status" has to change
        accordingly
        """
        assessment = AssessmentFactory.create()
        self.assertEqual(Assessment.STATUS_CREATED, assessment.status)

        assignment = AssessmentAssignmentFactory.create(assessment=assessment)
        flush()
        assessment.refresh_from_db()
        self.assertEqual(assessment.STATUS_IN_PROGRESS, assessment.status)

        assignment.locked_for_employee = False
        assignment.locked_for_assessor = True
        assignment.save()
        flush()
        assessment.refresh_from_db()
        self.assertEqual(Assessment.STATUS_QC_QUALITY_REVIEW, assessment.status)

        assignment.locked_for_employee = True
        assignment.locked_for_assessor = False
        assignment.save()
        flush()
        assessment.refresh_from_db()
        self.assertEqual(Assessment.STATUS_QC_ASSESSOR, assessment.status)

        report = ReportFactory.create(assessment=assessment, status=Report.STATUS_DRAFT)
        assignment.locked_for_employee = True
        assignment.locked_for_assessor = True
        assignment.save()
        flush()
        assessment.refresh_from_db()
        self.assertEqual(Assessment.STATUS_HAS_DRAFT, assessment.status)

        report.status = Report.STATUS_FINAL
        report.save()
        flush()
        assessment.refresh_from_db()
        self.assertEqual(Assessment.STATUS_HAS_FINAL, assessment.status)

    def test_assessment_status_update_on_unassigned_assessor(self):
        """
        When assessment has no assignment, assessment status changes to
        STATUS_CREATED
        """
        assessment = AssessmentFactory.create()
        assignment = AssessmentAssignmentFactory.create(assessment=assessment)
        flush()
        assessment.refresh_from_db()
        self.assertEqual(assessment.STATUS_IN_PROGRESS, assessment.status)

        assignment.submitted_at_least_once = True
        assignment.locked_for_employee = False
        assignment.locked_for_assessor = True
        assignment.save()
        flush()
        assessment.refresh_from_db()
        self.assertEqual(Assessment.STATUS_QC_QUALITY_REVIEW, assessment.status)
        url = reverse("assessments:assessmentassignment-detail", [assignment.pk])
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(
            status.HTTP_204_NO_CONTENT, response.status_code, response.content
        )

        flush()
        assessment.refresh_from_db()

        self.assertEqual(Assessment.STATUS_CREATED, assessment.status)

    def test_send_message_to_unassigned_assessor(self):
        """
        When unassigned, send message to assessor
        """
        MessageTypeFactory.create(slug="unassignment")
        assessment = AssessmentFactory.create()
        assessor = AssessorFactory.create(
            user__first_name="Bertje",
            user__last_name="Klaassen",
            user__email_notifications_on=True,
            user__language="en",
        )
        assignment = AssessmentAssignmentFactory.create(
            assessment=assessment, assessor=assessor
        )
        url = reverse(
            "assessments:assessmentinvitation-detail", [assignment.invitation_id]
        )
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(
            status.HTTP_204_NO_CONTENT, response.status_code, response.content
        )
        self.assertEqual(len(mail.outbox), 1)
        self.assertEqual(
            mail.outbox[0].body,
            '\n\n\n\nDear Bertje Klaassen,\n\n\nYou have been unassigned from the {tool_type} assessment of {customer} in {city}, {country} which is scheduled to take place on {date}.\n\nIf you have any questions or believe this to be a mistake, please contact <a href=\\"mailto:<EMAIL>\\"><EMAIL></a> immediately.\n\nKind regards\nSCOPEinsight\n\n'.format(
                tool_type=str(assessment.tool.type),
                customer=assessment.producing_organization.customer.name,
                city=assessment.producing_organization.city,
                country=assessment.producing_organization.country,
                date=assessment.date.strftime("%b. %d, %Y"),
            ),
        )

    def test_can_create_assessment(self):
        """
        It should be possible to create an assessment (with a tool selected)
        """
        tool = ToolFactory.create()
        producing_organization = ProducingOrganizationFactory.create()
        url = reverse("assessments:assessment-list")
        data = {
            "tool": reverse("products:tool-detail", [tool.pk]),
            "producing_organization": reverse(
                "customers:producingorganization-detail", [producing_organization.pk]
            ),
            "date": "2016-01-01",
        }
        with freeze_time("2015-01-01"):
            response = self.client.post(
                url, json.dumps(data), content_type="application/json"
            )
        self.assertEqual(
            status.HTTP_201_CREATED, response.status_code, response.content
        )
        self.assertEqual(1, Assessment.objects.count())

    def test_get_previous_year(self):
        """
        Year 0 should be _previous for the lowest in range
        """
        tool = ToolFactory.create()
        producing_organization = ProducingOrganizationFactory.create()
        url = reverse("assessments:assessment-list")
        data = {
            "tool": reverse("products:tool-detail", [tool.pk]),
            "producing_organization": reverse(
                "customers:producingorganization-detail", [producing_organization.pk]
            ),
            "date": "2018-05-05",
        }
        with freeze_time("2018-05-04"):
            response = self.client.post(
                url, json.dumps(data), content_type="application/json"
            )
        self.assertEqual(
            response.status_code, status.HTTP_201_CREATED, response.content
        )
        response_dict = json.loads(response.content)
        assessment = Assessment.objects.get(pk=response_dict["id"])
        years = list(
            assessment.balancesheets.all().values_list("year", "_previous__year")
        )
        self.assertListEqual(
            [
                (2018, 2017),
                (2017, 2016),
                (2016, 2015),
                (2015, 2014),
                (2014, 0),
                (0, None),
            ],
            years,
        )

    def test_insert_low_year(self):
        """
        It should be possible to insert a low year
        """
        tool = ToolFactory.create()
        producing_organization = ProducingOrganizationFactory.create()
        url = reverse("assessments:assessment-list")
        data = {
            "tool": reverse("products:tool-detail", [tool.pk]),
            "producing_organization": reverse(
                "customers:producingorganization-detail", [producing_organization.pk]
            ),
            "date": "2018-05-05",
        }
        with freeze_time("2018-05-04"):
            response = self.client.post(
                url, json.dumps(data), content_type="application/json"
            )
        response_dict = json.loads(response.content)
        assessment = Assessment.objects.get(pk=response_dict["id"])
        url = reverse("assessments:balancesheet-list")
        data = {
            "assessment": reverse("assessments:assessment-detail", [assessment.pk]),
            "year": 2013,
        }
        self.client.post(url, json.dumps(data), content_type="application/json")
        years = list(
            assessment.balancesheets.all().values_list("year", "_previous__year")
        )
        self.assertListEqual(
            [
                (2018, 2017),
                (2017, 2016),
                (2016, 2015),
                (2015, 2014),
                (2014, 2013),
                (2013, 0),
                (0, None),
            ],
            years,
        )

    def test_tab_accepted_default_true(self):
        """
        The *_tab_accepted booleans should default to True

        assessment_tab_accepted
        organizational_tab_accepted
        agent_tab_accepted
        value_chain_tab_accepted
        finance_history_tab_accepted
        production_tab_accepted
        finance_product_tab_accepted
        observations_tab_accepted
        documents_tab_accepted
        data_sharing_consent_tab_accepted
        finance_overview_tab_accepted
        """
        assessment = AssessmentFactory.create()
        self.assertTrue(assessment.assessment_tab_accepted)
        self.assertTrue(assessment.organizational_tab_accepted)
        self.assertTrue(assessment.agent_tab_accepted)
        self.assertTrue(assessment.value_chain_tab_accepted)
        self.assertTrue(assessment.finance_history_tab_accepted)
        self.assertTrue(assessment.production_tab_accepted)
        self.assertTrue(assessment.finance_product_tab_accepted)
        self.assertTrue(assessment.observations_tab_accepted)
        self.assertTrue(assessment.documents_tab_accepted)
        self.assertTrue(assessment.data_sharing_consent_tab_accepted)
        self.assertTrue(assessment.finance_overview_tab_accepted)

    def test_can_patch_tab_accepted(self):
        """
        The *_tab_accepted booleans should be patchable

        assessment_tab_accepted
        organizational_tab_accepted
        agent_tab_accepted
        value_chain_tab_accepted
        finance_history_tab_accepted
        production_tab_accepted
        finance_product_tab_accepted
        observations_tab_accepted
        documents_tab_accepted
        data_sharing_consent_tab_accepted
        finance_overview_tab_accepted
        """
        assessment = AssessmentFactory.create()
        data = {
            "assessment_tab_accepted": False,
            "organizational_tab_accepted": False,
            "agent_tab_accepted": False,
            "value_chain_tab_accepted": False,
            "finance_history_tab_accepted": False,
            "production_tab_accepted": False,
            "finance_product_tab_accepted": False,
            "observations_tab_accepted": False,
            "documents_tab_accepted": False,
            "data_sharing_consent_tab_accepted": False,
            "finance_overview_tab_accepted": False,
        }
        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code, response.content)
        assessment.refresh_from_db()
        self.assertFalse(assessment.assessment_tab_accepted)
        self.assertFalse(assessment.organizational_tab_accepted)
        self.assertFalse(assessment.agent_tab_accepted)
        self.assertFalse(assessment.value_chain_tab_accepted)
        self.assertFalse(assessment.finance_history_tab_accepted)
        self.assertFalse(assessment.production_tab_accepted)
        self.assertFalse(assessment.finance_product_tab_accepted)
        self.assertFalse(assessment.observations_tab_accepted)
        self.assertFalse(assessment.documents_tab_accepted)
        self.assertFalse(assessment.data_sharing_consent_tab_accepted)
        self.assertFalse(assessment.finance_overview_tab_accepted)

    def test_assessment_status_in_api(self):
        """
        Get assessment status
        """
        AssessmentFactory.create()
        url = reverse("assessments:assessment-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("status", response_dict["results"][0])

    def test_assessment_status_read_only(self):
        """
        Assessment.status is read only.
        """
        assessment = AssessmentFactory.create()
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {"status": "has_final"}
        self.client.patch(url, data, content_type="application/json")
        self.assertEqual(assessment.status, Assessment.STATUS_CREATED)

    def test_assessment_status_created_capitalised(self):
        """
        Status "Created" is capitalised on assessment create
        """
        tool = ToolFactory.create()
        producing_organization = ProducingOrganizationFactory.create()
        url = reverse("assessments:assessment-list")
        data = {
            "tool": reverse("products:tool-detail", [tool.pk]),
            "producing_organization": reverse(
                "customers:producingorganization-detail", [producing_organization.pk]
            ),
            "date": "2016-01-01",
        }
        with freeze_time("2015-01-01"):
            response = self.client.post(
                url, json.dumps(data), content_type="application/json"
            )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset({"display_status": "Created"}, response_dict)

    def test_assessment_list_has_status_counts(self):
        """
        The assessment list view should have a top-level status_counts key
        that gives information based on the queryset
        """
        assessments = AssessmentFactory.create_batch(15)
        for assessment in assessments[5:9]:
            AssessmentAssignmentFactory.create(
                assessment=assessment,
                invitation__assessment=assessment,
                assigned_as=AssessmentAssignment.AS_ASSESSOR,
            )
        for assessment in assessments[7:9]:
            assignment = AssessmentAssignmentFactory.create(
                assessment=assessment,
                invitation__assessment=assessment,
                assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
            )
        for assessment in assessments[9:12]:
            assignment = AssessmentAssignmentFactory.create(
                assessment=assessment,
                invitation__assessment=assessment,
                assigned_as=AssessmentAssignment.AS_ASSESSOR,
            )
            assignment.submitted_at_least_once = True
            assignment.save()
        for assessment in assessments[11:12]:
            assignment = AssessmentAssignmentFactory.create(
                assessment=assessment,
                invitation__assessment=assessment,
                assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
            )
            assignment.submitted_at_least_once = True
            assignment.save()
        for assessment in assessments[12:14]:
            with factory.django.mute_signals(post_save):
                AssessmentAssignmentFactory.create(
                    assessment=assessment,
                    invitation__assessment=assessment,
                    assigned_as=AssessmentAssignment.AS_ASSESSOR,
                )
                ReportFactory.create(assessment=assessment, status=Report.STATUS_DRAFT)
        for assessment in assessments[14:15]:
            with factory.django.mute_signals(post_save):
                AssessmentAssignmentFactory.create(
                    assessment=assessment, invitation__assessment=assessment
                )
                ReportFactory.create(assessment=assessment, status=Report.STATUS_FINAL)
        flush()
        url = reverse("assessments:assessment-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("status_counts", response_dict)
        self.assertDictEqual(
            {
                "created": 5,
                "in_progress": 4,
                "qc_assessor": 3,
                "has_draft": 2,
                "has_final": 1,
            },
            response_dict["status_counts"],
        )

    def test_status_counts_no_invite(self):
        """
        When there is no invite, the created count should be 1
        """
        AssessmentFactory.create()
        url = reverse("assessments:assessment-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("status_counts", response_dict)
        self.assertDictEqual({"created": 1}, response_dict["status_counts"])

    def test_status_counts_one_invite(self):
        """
        When there is one invite, the created count should be 1
        """
        AssessmentInvitationFactory.create()
        url = reverse("assessments:assessment-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("status_counts", response_dict)
        self.assertDictEqual({"created": 1}, response_dict["status_counts"])

    def test_status_counts_two_invite(self):
        """
        When there are two invites, the created count should be 1
        """
        assessment = AssessmentFactory.create()
        AssessmentInvitationFactory.create_batch(2, assessment=assessment)
        url = reverse("assessments:assessment-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("status_counts", response_dict)
        self.assertDictEqual({"created": 1}, response_dict["status_counts"])

    def test_status_counts_one_invite_one_assignment(self):
        """
        When there is one invite and one assignment, the created count should
        be 1 and the in progress count should be 1
        """
        assessment = AssessmentFactory.create()
        AssessmentInvitationFactory.create(assessment=assessment)
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            invitation__status=AssessmentInvitation.STATUS_ACCEPTED,
        )
        url = reverse("assessments:assessment-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("status_counts", response_dict)
        self.assertDictEqual(
            {"created": 1, "in_progress": 1}, response_dict["status_counts"]
        )

    def test_assessment_search_by_assessor_invitation_new(self):
        """
        Assessment search should return assessors name if status of invitation is new
        """
        assessor = AssessorFactory.create(user__first_name="Inessa")
        AssessmentInvitationFactory.create(
            assessor=assessor, status=AssessmentInvitation.STATUS_NEW
        )
        AssessmentInvitationFactory.create(
            assessor=assessor, status=AssessmentInvitation.STATUS_DECLINED
        )
        AssessmentInvitationFactory.create(status=AssessmentInvitation.STATUS_NEW)

        flush()
        url = reverse("assessments:assessment-list") + "?search=Inessa"
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code, response.content)
        response_dict = json.loads(response.content)
        self.assertEqual(response_dict["count"], 1)

    def test_assessment_search_by_quality_reviewer(self):
        """
        Assessment search for quality reviewer should return assessments that have the quality_reviewer set or
        the assessments of which the project has the quality_reviewer set.
        """
        quality_reviewer = EmployeeFactory.create(user__first_name="Inessa")
        AssessmentFactory.create()
        AssessmentFactory.create(project__quality_reviewer=quality_reviewer.user)
        AssessmentFactory.create(quality_reviewer=quality_reviewer.user)
        flush()
        url = reverse("assessments:assessment-list") + "?search=Inessa"
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code, response.content)
        response_dict = json.loads(response.content)
        self.assertEqual(response_dict["count"], 2)

    def test_assessment_search_by_assessor_invitation(self):
        """
        Assessment search for quality reviewer should return assessments that have the quality_reviewer set or
        the assessments of which the project has the quality_reviewer set.
        """
        AssessmentFactory.create()
        AssessmentInvitationFactory(assessor__user__last_name="Clinton")
        flush()
        url = reverse("assessments:assessment-list") + "?search=Clinton"
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code, response.content)
        response_dict = json.loads(response.content)
        self.assertEqual(response_dict["count"], 1)

    def test_assessment_list_metadata_assessor_finspec(self):
        """
        Assessor and financial specialist metadata should be independent
        """
        a1, a2, a3 = AssessmentFactory.create_batch(3)
        assessor_1, assessor_2 = AssessorFactory.create_batch(2)
        AssessmentAssignmentFactory.create(
            assessment=a1,
            assessor=assessor_1,
            assigned_as=AssessmentAssignment.AS_ASSESSOR,
        )
        AssessmentAssignmentFactory.create(
            assessment=a1,
            assessor=assessor_2,
            assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        AssessmentAssignmentFactory.create(
            assessment=a2,
            assessor=assessor_1,
            assigned_as=AssessmentAssignment.AS_ASSESSOR,
        )
        AssessmentAssignmentFactory.create(
            assessment=a2,
            assessor=assessor_1,
            assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        AssessmentAssignmentFactory.create(
            assessment=a2,
            assessor=assessor_2,
            assigned_as=AssessmentAssignment.AS_ASSESSOR,
        )
        url = reverse(
            "assessments:assessment-list"
        ) + "?financial_specialist={}".format(assessor_2.user.full_name)
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        expected = {
            "assessors": [
                {
                    "label": assessor_1.user.full_name,
                    "value": assessor_1.user.full_name,
                },
                {"label": "Not assigned", "value": "not_set"},
            ],
            "financial_specialists": [
                {"label": assessor.user.full_name, "value": assessor.user.full_name}
                for assessor in sorted(
                    [assessor_1, assessor_2], key=lambda a: a.user.full_name
                )
            ]
            + [{"label": "Not assigned", "value": "not_set"}],
        }
        self.assertIn("filters", response_dict)
        self.assertDictContainsSubset(expected, response_dict["filters"])

    def test_assessment_list_view_has_filter_metadata(self):
        """
        The assessment list view should present metadata for filters
        """
        employee1 = EmployeeFactory.create(
            user__first_name="Quality 1", user__last_name="Controller 1"
        )
        employee2 = EmployeeFactory.create(
            user__first_name="Quality 2", user__last_name="Controller 2"
        )
        AssessmentFactory.create(
            tool__type__name="type 0",
            date="2017-01-01",
            language="fr",
            producing_organization__country="Kenya",
            project__quality_reviewer=employee1.user,
        )
        AssessmentFactory.create(
            tool__type__name="type 1",
            date="2017-01-01",
            language="fr",
            producing_organization__country="Kenya",
        )
        assessment = AssessmentFactory.create(
            tool__type__name="type 2",
            date="2017-12-01",
            language="es",
            producing_organization__country="Ecuador",
            quality_reviewer=employee2.user,
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            invitation__assessor__user__first_name="John",
            invitation__assessor__user__last_name="Smith",
            assessor__user__first_name="John",
            assessor__user__last_name="Smith",
            assigned_as="assessor",
        )
        AssessmentInvitationFactory.create(
            assessment=assessment,
            assessor__user__first_name="Ernie",
            assessor__user__last_name="Jeweetwel",
            assigned_as="assessor",
        )
        AssessmentInvitationFactory.create(
            assessment=assessment,
            assessor__user__first_name="Ernie",
            assessor__user__last_name="Jeweetniet",
            assigned_as="assessor",
            status=AssessmentInvitation.STATUS_DECLINED,
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            invitation__assessor__user__first_name="Jane",
            invitation__assessor__user__last_name="Doe",
            assessor__user__first_name="Jane",
            assessor__user__last_name="Doe",
            assigned_as="assessor",
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            invitation__assessor__user__first_name="John FS",
            invitation__assessor__user__last_name="Smith FS",
            invitation__assigned_as="financial_specialist",
            assessor__user__first_name="John FS",
            assessor__user__last_name="Smith FS",
            assigned_as="financial_specialist",
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            invitation__assessor__user__first_name="Jane FS",
            invitation__assessor__user__last_name="Doe FS",
            invitation__assigned_as="financial_specialist",
            assessor__user__first_name="Jane FS",
            assessor__user__last_name="Doe FS",
            assigned_as="financial_specialist",
        )
        flush()
        url = reverse("assessments:assessment-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertDictEqual(
            {
                "tool_types": [
                    {"label": "type 0", "value": "type 0"},
                    {"label": "type 1", "value": "type 1"},
                    {"label": "type 2", "value": "type 2"},
                ],
                "assessors": [
                    {"label": "Jane Doe", "value": "Jane Doe"},
                    {"label": "John Smith", "value": "John Smith"},
                    {"label": "Ernie Jeweetwel", "value": "Ernie Jeweetwel"},
                    {"label": "Not assigned", "value": "not_set"},
                ],
                "financial_specialists": [
                    {"label": "Jane FS Doe FS", "value": "Jane FS Doe FS"},
                    {"label": "John FS Smith FS", "value": "John FS Smith FS"},
                    {"label": "Not assigned", "value": "not_set"},
                ],
                "financial_quality_reviewers": [
                    {"label": "Not assigned", "value": "not_set"}
                ],
                "quality_reviewers": [
                    {
                        "label": "Quality 1 Controller 1",
                        "value": "Quality 1 Controller 1",
                    },
                    {
                        "label": "Quality 2 Controller 2",
                        "value": "Quality 2 Controller 2",
                    },
                    {"label": "Not assigned", "value": "not_set"},
                ],
                "countries": [
                    {"label": "Ecuador", "value": "Ecuador"},
                    {"label": "Kenya", "value": "Kenya"},
                ],
                "date": {"min": "2017-01-01", "max": "2017-12-01"},
                "statuses": [
                    {"label": "Created", "value": "created"},
                    {"label": "In progress", "value": "in_progress"},
                ],
                "languages": [
                    {"label": "Spanish", "value": "es"},
                    {"label": "French", "value": "fr"},
                ],
                "project": [{"label": "", "value": ""}],
            },
            response_dict["filters"],
        )

    def test_filter_metadata_with_project_filter(self):
        """
        When applying a project filter, the metadata for project should not be
        modified. Everything else should be.
        """
        employee1 = EmployeeFactory.create(
            user__first_name="Quality 1", user__last_name="Controller 1"
        )
        employee2 = EmployeeFactory.create(
            user__first_name="Quality 2", user__last_name="Controller 2"
        )
        AssessmentFactory.create(
            tool__type__name="type 0",
            date="2017-01-01",
            language="fr",
            producing_organization__country="Kenya",
            project__name="project 0",
            project__quality_reviewer=employee1.user,
        )
        AssessmentFactory.create(
            tool__type__name="type 8",
            date="2017-01-01",
            language="fr",
            producing_organization__country="Kenya",
            project__name="project 0",
            project__quality_reviewer=employee1.user,
            quality_reviewer=employee2.user,
        )
        AssessmentFactory.create(
            tool__type__name="type 1",
            date="2017-01-01",
            language="fr",
            producing_organization__country="Kenya",
            project__name="project 1",
        )
        assessment = AssessmentFactory.create(
            tool__type__name="type 2",
            date="2017-12-01",
            language="es",
            producing_organization__country="Ecuador",
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            assessor__user__first_name="John",
            assessor__user__last_name="Smith",
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            assessor__user__first_name="Jane",
            assessor__user__last_name="Doe",
        )
        flush()
        url = reverse("assessments:assessment-list") + "?project_name=project 0"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertDictEqual(
            {
                "tool_types": [
                    {"label": "type 0", "value": "type 0"},
                    {"label": "type 8", "value": "type 8"},
                ],
                "assessors": [{"label": "Not assigned", "value": "not_set"}],
                "countries": [{"label": "Kenya", "value": "Kenya"}],
                "date": {"min": "2017-01-01", "max": "2017-01-01"},
                "statuses": [{"label": "Created", "value": "created"}],
                "languages": [{"label": "French", "value": "fr"}],
                "project": [
                    {"label": "", "value": ""},
                    {"label": "project 0", "value": "project 0"},
                    {"label": "project 1", "value": "project 1"},
                ],
                "financial_specialists": [
                    {"label": "Not assigned", "value": "not_set"}
                ],
                "financial_quality_reviewers": [
                    {"label": "Not assigned", "value": "not_set"}
                ],
                "quality_reviewers": [
                    {
                        "label": "Quality 1 Controller 1",
                        "value": "Quality 1 Controller 1",
                    },
                    {
                        "label": "Quality 2 Controller 2",
                        "value": "Quality 2 Controller 2",
                    },
                ],
            },
            response_dict["filters"],
        )

    def test_filter_metadata_with_country_filter(self):
        """
        When applying a country filter, the metadata for country should not be
        modified. Everything else should be.
        """
        AssessmentFactory.create(
            tool__type__name="type 0",
            date="2017-01-01",
            language="fr",
            producing_organization__country="Kenya",
            project__name="project 0",
        )
        AssessmentFactory.create(
            tool__type__name="type 1",
            date="2017-01-01",
            language="fr",
            producing_organization__country="Kenya",
            project__name="project 1",
        )
        assessment = AssessmentFactory.create(
            tool__type__name="type 2",
            date="2017-12-01",
            language="es",
            producing_organization__country="Ecuador",
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            assessor__user__first_name="John",
            assessor__user__last_name="Smith",
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            assessor__user__first_name="Jane",
            assessor__user__last_name="Doe",
        )
        flush()
        url = reverse("assessments:assessment-list") + "?countries=Kenya"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertDictEqual(
            {
                "tool_types": [
                    {"label": "type 0", "value": "type 0"},
                    {"label": "type 1", "value": "type 1"},
                ],
                "assessors": [{"label": "Not assigned", "value": "not_set"}],
                "countries": [
                    {"label": "Ecuador", "value": "Ecuador"},
                    {"label": "Kenya", "value": "Kenya"},
                ],
                "date": {"min": "2017-01-01", "max": "2017-01-01"},
                "statuses": [{"label": "Created", "value": "created"}],
                "languages": [{"label": "French", "value": "fr"}],
                "project": [
                    {"label": "project 0", "value": "project 0"},
                    {"label": "project 1", "value": "project 1"},
                ],
                "financial_specialists": [
                    {"label": "Not assigned", "value": "not_set"}
                ],
                "financial_quality_reviewers": [
                    {"label": "Not assigned", "value": "not_set"}
                ],
                "quality_reviewers": [{"label": "Not assigned", "value": "not_set"}],
            },
            response_dict["filters"],
        )

    def test_filter_metadata_with_tool_type_filter(self):
        """
        When applying a tool type filter, the metadata for tool type should
        not be modified. Everything else should be.
        """
        AssessmentFactory.create(
            tool__type__name="type 0",
            date="2017-01-01",
            language="fr",
            producing_organization__country="Kenya",
            project__name="project 0",
        )
        AssessmentFactory.create(
            tool__type__name="type 1",
            date="2017-01-01",
            language="fr",
            producing_organization__country="Kenya",
            project__name="project 1",
        )
        assessment = AssessmentFactory.create(
            tool__type__name="type 2",
            date="2017-12-01",
            language="es",
            producing_organization__country="Ecuador",
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            assessor__user__first_name="John",
            assessor__user__last_name="Smith",
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            assessor__user__first_name="Jane",
            assessor__user__last_name="Doe",
        )
        flush()
        url = reverse("assessments:assessment-list") + "?tool_type=type 0"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertDictEqual(
            {
                "tool_types": [
                    {"label": "type 0", "value": "type 0"},
                    {"label": "type 1", "value": "type 1"},
                    {"label": "type 2", "value": "type 2"},
                ],
                "assessors": [{"label": "Not assigned", "value": "not_set"}],
                "countries": [{"label": "Kenya", "value": "Kenya"}],
                "date": {"min": "2017-01-01", "max": "2017-01-01"},
                "statuses": [{"label": "Created", "value": "created"}],
                "languages": [{"label": "French", "value": "fr"}],
                "project": [{"label": "project 0", "value": "project 0"}],
                "financial_specialists": [
                    {"label": "Not assigned", "value": "not_set"}
                ],
                "financial_quality_reviewers": [
                    {"label": "Not assigned", "value": "not_set"}
                ],
                "quality_reviewers": [{"label": "Not assigned", "value": "not_set"}],
            },
            response_dict["filters"],
        )

    def test_filter_metadata_with_assessor_filter(self):
        """
        When applying a assessor filter, the metadata for assessor should not
        be modified. Everything else should be.
        """
        AssessmentFactory.create(
            tool__type__name="type 0",
            date="2017-01-01",
            language="fr",
            producing_organization__country="Kenya",
            project__name="project 0",
        )
        AssessmentFactory.create(
            tool__type__name="type 1",
            date="2017-01-01",
            language="fr",
            producing_organization__country="Kenya",
            project__name="project 1",
        )
        assessment = AssessmentFactory.create(
            tool__type__name="type 2",
            date="2017-12-01",
            language="es",
            producing_organization__country="Ecuador",
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            invitation__assessor__user__first_name="John",
            invitation__assessor__user__last_name="Smith",
            assessor__user__first_name="John",
            assessor__user__last_name="Smith",
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            invitation__assessor__user__first_name="Jane",
            invitation__assessor__user__last_name="Doe",
            assessor__user__first_name="Jane",
            assessor__user__last_name="Doe",
        )
        AssessmentInvitationFactory.create(
            assessment=assessment,
            assessor__user__first_name="Bert",
            assessor__user__last_name="van Sesamstraat",
        )
        flush()
        url = reverse("assessments:assessment-list") + "?assessor_name=John Smith"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertDictEqual(
            {
                "tool_types": [{"label": "type 2", "value": "type 2"}],
                "assessors": [
                    {"label": "Jane Doe", "value": "Jane Doe"},
                    {"label": "John Smith", "value": "John Smith"},
                    {"label": "Bert van Sesamstraat", "value": "Bert van Sesamstraat"},
                    {"label": "Not assigned", "value": "not_set"},
                ],
                "countries": [{"label": "Ecuador", "value": "Ecuador"}],
                "date": {"min": "2017-12-01", "max": "2017-12-01"},
                "statuses": [{"label": "In progress", "value": "in_progress"}],
                "languages": [{"label": "Spanish", "value": "es"}],
                "project": [{"label": "", "value": ""}],
                "financial_specialists": [
                    {"label": "Not assigned", "value": "not_set"}
                ],
                "financial_quality_reviewers": [
                    {"label": "Not assigned", "value": "not_set"}
                ],
                "quality_reviewers": [{"label": "Not assigned", "value": "not_set"}],
            },
            response_dict["filters"],
        )

    @skip("Skipped until check for validity")
    def test_filter_metadata_with_status_filter(self):
        """
        When applying a status filter, the metadata for status should not be
        modified. Everything else should be.
        """
        AssessmentFactory.create(
            tool__type__name="type 0",
            date="2017-01-01",
            language="fr",
            producing_organization__country="Kenya",
            project__name="project 0",
        )
        AssessmentFactory.create(
            tool__type__name="type 1",
            date="2017-01-01",
            language="fr",
            producing_organization__country="Kenya",
            project__name="project 1",
        )
        assessment = AssessmentFactory.create(
            tool__type__name="type 2",
            date="2017-12-01",
            language="es",
            producing_organization__country="Ecuador",
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            assessor__user__first_name="John",
            assessor__user__last_name="Smith",
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            assessor__user__first_name="Jane",
            assessor__user__last_name="Doe",
        )
        flush()
        url = (
            reverse("assessments:assessment-list")
            + "?status={}".format("created")
            + "&ordering=-date"
        )
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertDictEqual(
            {
                "tool_types": [
                    {"label": "type 0", "value": "type 0"},
                    {"label": "type 1", "value": "type 1"},
                ],
                "assessors": [{"label": "Not assigned", "value": "not_set"}],
                "countries": [{"label": "Kenya", "value": "Kenya"}],
                "date": {"min": "2017-01-01", "max": "2017-01-01"},
                "statuses": [
                    {"label": "Created", "value": "created"},
                    {"label": "In progress", "value": "in_progress"},
                ],
                "languages": [{"label": "French", "value": "fr"}],
                "project": [
                    {"label": "project 0", "value": "project 0"},
                    {"label": "project 1", "value": "project 1"},
                ],
                "financial_specialists": [
                    {"label": "Not assigned", "value": "not_set"}
                ],
                "financial_quality_reviewers": [
                    {"label": "Not assigned", "value": "not_set"}
                ],
                "quality_reviewers": [{"label": "Not assigned", "value": "not_set"}],
            },
            response_dict["filters"],
        )

    def test_filter_metadata_with_language_filter(self):
        """
        When applying a language filter, the metadata for language should not
        be modified. Everything else should be.
        """
        AssessmentFactory.create(
            tool__type__name="type 0",
            date="2017-01-01",
            language="fr",
            producing_organization__country="Kenya",
            project__name="project 0",
        )
        AssessmentFactory.create(
            tool__type__name="type 1",
            date="2017-01-01",
            language="fr",
            producing_organization__country="Kenya",
            project__name="project 1",
        )
        assessment = AssessmentFactory.create(
            tool__type__name="type 2",
            date="2017-12-01",
            language="es",
            producing_organization__country="Ecuador",
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            assessor__user__first_name="John",
            assessor__user__last_name="Smith",
            invitation__assessor__user__first_name="John",
            invitation__assessor__user__last_name="Smith",
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            assessor__user__first_name="Jane",
            assessor__user__last_name="Doe",
            invitation__assessor__user__first_name="Jane",
            invitation__assessor__user__last_name="Doe",
        )
        flush()
        url = reverse("assessments:assessment-list") + "?language=es"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertDictEqual(
            {
                "tool_types": [{"label": "type 2", "value": "type 2"}],
                "assessors": [
                    {"label": "Jane Doe", "value": "Jane Doe"},
                    {"label": "John Smith", "value": "John Smith"},
                    {"label": "Not assigned", "value": "not_set"},
                ],
                "countries": [{"label": "Ecuador", "value": "Ecuador"}],
                "date": {"min": "2017-12-01", "max": "2017-12-01"},
                "statuses": [{"label": "In progress", "value": "in_progress"}],
                "languages": [
                    {"label": "Spanish", "value": "es"},
                    {"label": "French", "value": "fr"},
                ],
                "project": [{"label": "", "value": ""}],
                "financial_specialists": [
                    {"label": "Not assigned", "value": "not_set"}
                ],
                "financial_quality_reviewers": [
                    {"label": "Not assigned", "value": "not_set"}
                ],
                "quality_reviewers": [{"label": "Not assigned", "value": "not_set"}],
            },
            response_dict["filters"],
        )

    def test_filter_metadata_with_date_filter(self):
        """
        When applying a date filter, the metadata for date should not
        be modified. Everything else should be.
        """
        AssessmentFactory.create(
            tool__type__name="type 0",
            date="2017-01-01",
            language="fr",
            producing_organization__country="Kenya",
            project__name="project 0",
        )
        AssessmentFactory.create(
            tool__type__name="type 1",
            date="2017-01-01",
            language="fr",
            producing_organization__country="Kenya",
            project__name="project 1",
        )
        assessment = AssessmentFactory.create(
            tool__type__name="type 2",
            date="2017-12-01",
            language="es",
            producing_organization__country="Ecuador",
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            assessor__user__first_name="John",
            assessor__user__last_name="Smith",
            invitation__assessor__user__first_name="John",
            invitation__assessor__user__last_name="Smith",
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            assessor__user__first_name="Jane",
            assessor__user__last_name="Doe",
            invitation__assessor__user__first_name="Jane",
            invitation__assessor__user__last_name="Doe",
        )
        flush()
        url = reverse("assessments:assessment-list") + "?min_date=2017-06-01"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        self.assertDictEqual(
            {
                "tool_types": [{"label": "type 2", "value": "type 2"}],
                "assessors": [
                    {"label": "Jane Doe", "value": "Jane Doe"},
                    {"label": "John Smith", "value": "John Smith"},
                    {"label": "Not assigned", "value": "not_set"},
                ],
                "countries": [{"label": "Ecuador", "value": "Ecuador"}],
                "date": {"min": "2017-01-01", "max": "2017-12-01"},
                "statuses": [{"label": "In progress", "value": "in_progress"}],
                "languages": [{"label": "Spanish", "value": "es"}],
                "project": [{"label": "", "value": ""}],
                "financial_specialists": [
                    {"label": "Not assigned", "value": "not_set"}
                ],
                "financial_quality_reviewers": [
                    {"label": "Not assigned", "value": "not_set"}
                ],
                "quality_reviewers": [{"label": "Not assigned", "value": "not_set"}],
            },
            response_dict["filters"],
        )

    def test_filter_metadata_is_translated(self):
        """
        The metadata for filters should be translated properly
        """
        AssessmentFactory.create()
        assessment = AssessmentFactory.create()
        AssessmentAssignmentFactory.create(
            assessment=assessment, invitation__assessment=assessment
        )
        flush()
        translations_dir = mkdtemp()
        try:
            po_dir = os.path.join(translations_dir, "fr", "LC_MESSAGES")
            os.makedirs(po_dir)
            pofile = polib.POFile()
            pofile.append(polib.POEntry(msgid="Created", msgstr="Created (fr)"))
            pofile.append(polib.POEntry(msgid="In progress", msgstr="In progress (fr)"))
            pofile.metadata = {
                **pofile.metadata,
                "Content-Type": "text/plain; charset=utf-8",
                "Content-Transfer-Encoding": "8bit",
            }
            pofile.save(os.path.join(po_dir, "django.po"))
            pofile.save_as_mofile(os.path.join(po_dir, "django.mo"))
            self.employee.user.language = "fr"
            self.employee.user.save()
            url = reverse("assessments:assessment-list")
            with self.settings(LOCALE_PATHS=[translations_dir]):
                response = self.client.get(url, content_type="application/json")
            response_dict = json.loads(response.content)
            self.assertIn("filters", response_dict)
            self.assertIn("statuses", response_dict["filters"])
            self.assertListEqual(
                [
                    {"label": "Created (fr)", "value": "created"},
                    {"label": "In progress (fr)", "value": "in_progress"},
                ],
                response_dict["filters"]["statuses"],
            )
        finally:
            shutil.rmtree(translations_dir)

    def test_can_filter_by_tool_type(self):
        """
        Should be able to filter assessment list by tool type name
        """
        AssessmentFactory.create()
        AssessmentFactory.create(tool__type__name="type 1")
        flush()
        url = reverse("assessments:assessment-list") + "?tool_type=type 1"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(1, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(1, len(response_dict["results"]))

    def test_can_filter_by_assessor_name(self):
        """
        Should be able to filter assessment list by assessor name
        """
        AssessmentFactory.create()
        assessment = AssessmentFactory.create()
        assessor = AssessorFactory.create(
            user__first_name="John", user__last_name="Smith"
        )
        invitation = AssessmentInvitationFactory.create(
            assessor=assessor, assessment=assessment, assigned_as="assessor"
        )
        AssessmentAssignmentFactory.create(
            assessor=assessor,
            assessment=assessment,
            assigned_as="assessor",
            invitation=invitation,
        )
        flush()
        self.assertEqual(2, Assessment.objects.count())
        url = reverse("assessments:assessment-list") + "?assessor_name=John Smith"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(1, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(1, len(response_dict["results"]))

    def test_can_filter_by_null_assessor(self):
        """
        Should be able to filter assessment list by empty assessor name
        """
        AssessmentFactory.create()
        assessment = AssessmentFactory.create()
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            assigned_as=AssessmentAssignment.AS_ASSESSOR,
        )
        AssessmentInvitationFactory.create(status=AssessmentInvitation.STATUS_DECLINED)
        flush()
        url = reverse("assessments:assessment-list") + "?assessor_name=not_set"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(2, response_dict["count"])

    def test_can_filter_by_null_financial_specialist(self):
        """
        Should be able to filter assessment list by empty financial specialist
        name
        """
        AssessmentFactory.create(tool__type__requires_accountant=True)
        assessment = AssessmentFactory.create(tool__type__requires_accountant=True)
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        AssessmentInvitationFactory.create(
            assessment__tool__type__requires_accountant=True,
            status=AssessmentInvitation.STATUS_DECLINED,
        )
        AssessmentFactory.create(tool__type__requires_accountant=False)
        flush()
        url = reverse("assessments:assessment-list") + "?financial_specialist=not_set"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(2, response_dict["count"])

    def test_can_filter_by_invited_assessor_name(self):
        """
        Should be able to filter assessment list by invitation assessor name
        """
        AssessmentFactory.create()
        assessment = AssessmentFactory.create()
        assessor = AssessorFactory.create(
            user__first_name="John", user__last_name="Smith"
        )
        AssessmentInvitationFactory.create(
            assessor=assessor, assessment=assessment, assigned_as="assessor"
        )
        flush()
        self.assertEqual(2, Assessment.objects.count())
        url = reverse("assessments:assessment-list") + "?assessor_name=John Smith"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(1, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(1, len(response_dict["results"]))

    def test_can_filter_by_financial_specialist(self):
        """
        Should be able to filter assessment list by financial specialist
        """
        AssessmentFactory.create()
        assessment = AssessmentFactory.create()
        assessor = AssessorFactory.create(
            user__first_name="John", user__last_name="Smith"
        )
        invitation = AssessmentInvitationFactory.create(
            assessor=assessor, assessment=assessment, assigned_as="financial_specialist"
        )
        AssessmentAssignmentFactory.create(
            assessor=assessor,
            assessment=assessment,
            assigned_as="financial_specialist",
            invitation=invitation,
        )
        flush()
        self.assertEqual(2, Assessment.objects.count())
        url = (
            reverse("assessments:assessment-list") + "?financial_specialist=John Smith"
        )
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(1, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(1, len(response_dict["results"]))
        expected_id = assessment.id
        response_id = response_dict["results"][0]["id"]
        self.assertEqual(expected_id, response_id)

    def test_can_filter_by_invited_financial_specialist(self):
        """
        Should be able to filter assessment list by financial specialist invitation
        """
        AssessmentFactory.create()
        assessment = AssessmentFactory.create()
        assessor = AssessorFactory.create(
            user__first_name="John", user__last_name="Smith"
        )
        AssessmentInvitationFactory.create(
            assessor=assessor, assessment=assessment, assigned_as="financial_specialist"
        )
        flush()
        self.assertEqual(2, Assessment.objects.count())
        url = (
            reverse("assessments:assessment-list") + "?financial_specialist=John Smith"
        )
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(1, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(1, len(response_dict["results"]))
        expected_id = assessment.id
        response_id = response_dict["results"][0]["id"]
        self.assertEqual(expected_id, response_id)

    def test_can_filter_by_quality_reviewer_on_project(self):
        """
        Should be able to filter assessment list by quality_reviewer defined on a project
        """
        employee = EmployeeFactory.create(
            user__first_name="Test", user__last_name="Last"
        )
        AssessmentFactory.create()
        AssessmentFactory.create(project__quality_reviewer=employee.user)
        flush()
        url = reverse("assessments:assessment-list") + "?quality_reviewer=Test Last"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(1, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(1, len(response_dict["results"]))

    def test_can_filter_by_quality_reviewer_on_assessment(self):
        """
        Should be able to filter assessment list by quality_reviewer defined on the assessments
        """
        employee = EmployeeFactory.create(
            user__first_name="Test", user__last_name="Last"
        )
        AssessmentFactory.create()
        AssessmentFactory.create(quality_reviewer=employee.user)
        flush()
        url = reverse("assessments:assessment-list") + "?quality_reviewer=Test Last"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(1, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(1, len(response_dict["results"]))

    def test_can_filter_by_date(self):
        """
        Should be able to filter assessment list by date
        """
        AssessmentFactory.create(date="2017-01-01")
        AssessmentFactory.create(date="2017-01-02")
        flush()
        url = (
            reverse("assessments:assessment-list")
            + "?min_date=2017-01-02"
            + "&max_date=2017-01-02"
        )
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(1, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(1, len(response_dict["results"]))

    def test_can_filter_by_status(self):
        """
        Should be able to filter assessment list by status
        """
        AssessmentFactory.create()
        AssessmentAssignmentFactory.create()
        flush()
        url = reverse("assessments:assessment-list") + "?status={}".format(
            "in_progress"
        )
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(1, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(1, len(response_dict["results"]))

    @skip("Skipped until check for validity")
    def test_filter_by_status_multiple_assignment(self):
        """
        When filtering assessments with multiple assignments, return the
        assessment if any of the assignments match.
        """
        assessment = AssessmentFactory.create()
        AssessmentAssignmentFactory.create(assessment=assessment)
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            submitted_at_least_once=True,
            locked_for_employee=False,
            locked_for_assessor=True,
        )
        flush()
        url = reverse("assessments:assessment-list") + "?status={}".format(
            "in_progress"
        )
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(1, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(1, len(response_dict["results"]))
        url = reverse("assessments:assessment-list") + "?status={}".format(
            "qc_quality_review"
        )
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(1, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(1, len(response_dict["results"]))
        url = reverse("assessments:assessment-list") + "?status={}".format(
            "qc_assessor"
        )
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(0, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(0, len(response_dict["results"]))

    def test_status_filter_metadata_multiple(self):
        """
        When an assessment has multiple assignments with multiple statuses,
        show multiple filter options
        """
        assessment = AssessmentFactory.create()
        AssessmentAssignmentFactory.create(
            assessment=assessment, invitation__assessment=assessment
        )
        AssessmentAssignmentFactory.create(
            assessment=assessment,
            invitation__assessment=assessment,
            submitted_at_least_once=True,
            locked_for_employee=False,
            locked_for_assessor=True,
        )
        flush()
        url = reverse("assessments:assessment-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("filters", response_dict)
        filters = response_dict["filters"]
        self.assertIn("statuses", filters)
        self.assertListEqual(
            [
                {"label": "In progress", "value": "in_progress"},
                {"label": "QC Quality Review", "value": "qc_quality_review"},
            ],
            filters["statuses"],
        )

    def test_can_filter_by_language(self):
        """
        Should be able to filter assessment list by language
        """
        AssessmentFactory.create(language="es")
        AssessmentFactory.create(language="fr")
        flush()
        url = reverse("assessments:assessment-list") + "?language=fr"
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(1, response_dict["count"])
        self.assertIn("results", response_dict)
        self.assertEqual(1, len(response_dict["results"]))

    def test_can_update_date_and_language_of_locked_assessment(self):
        """
        Should be able to update date and language of assessment when it's locked
        """
        assessment = AssessmentFactory.create(
            language="en",
            assessmentassignments__locked_for_employee=True,
            assessmentassignments__locked_for_assessor=False,
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        qr = UserFactory.create()
        data = {
            "language": "fr",
            "date": "2016-01-01",
            "quality_reviewer": reverse("accounts:user-detail", [qr.pk]),
            "financial_quality_reviewer": reverse("accounts:user-detail", [qr.pk]),
            "url": url,
        }
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        assessment.refresh_from_db()

        self.assertEqual("fr", assessment.language)
        self.assertEqual(qr, assessment.quality_reviewer)
        self.assertEqual(qr, assessment.financial_quality_reviewer)
        self.assertEqual(datetime.date(2016, 1, 1), assessment.date)
        assignment = assessment.assessmentassignments.get()
        self.assertTrue(assignment.locked_for_employee)

    def test_employees_can_add_quality_reviewer(self):
        """
        Employee can add quality reviewer
        """
        my_project = ProjectFactory.create()
        assessment = AssessmentFactory.create(project=my_project)
        employee = EmployeeFactory.create()

        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {"quality_reviewer": reverse("accounts:user-detail", [employee.user.pk])}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_see_project_qr_as_display_quality_reviewer(self):
        """
        Can see project QR as display_quality_reviewer
        """
        employee = EmployeeFactory.create(
            user__first_name="Quality", user__last_name="Reviewer"
        )
        my_project = ProjectFactory.create(quality_reviewer=employee.user)
        assessment = AssessmentFactory.create(project=my_project)
        url = (
            reverse("assessments:assessment-detail", [assessment.pk])
            + "?fields=display_quality_reviewer"
        )
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertIn("display_quality_reviewer", response_dict)
        self.assertEqual(
            employee.user.pk, response_dict["display_quality_reviewer"]["id"]
        )
        self.assertEqual(
            employee.user.first_name,
            response_dict["display_quality_reviewer"]["first_name"],
        )
        self.assertEqual(
            employee.user.last_name,
            response_dict["display_quality_reviewer"]["last_name"],
        )
        self.assertEqual(
            employee.user.email, response_dict["display_quality_reviewer"]["email"]
        )
        self.assertEqual(
            "http://testserver" + reverse("accounts:user-detail", [employee.user.pk]),
            response_dict["display_quality_reviewer"]["url"],
        )

    def test_can_see_assessment_qr_as_display_quality_reviewer(self):
        """
        Can see assessment QR as display_quality_reviewer
        """
        employee = EmployeeFactory.create(
            user__first_name="Quality", user__last_name="Reviewer"
        )
        project_employee = EmployeeFactory.create(
            user__first_name="Meeeh", user__last_name="Whatever"
        )
        my_project = ProjectFactory.create(quality_reviewer=project_employee.user)
        assessment = AssessmentFactory.create(
            project=my_project, quality_reviewer=employee.user
        )
        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        expected_data = {
            "id": employee.user.pk,
            "first_name": employee.user.first_name,
            "last_name": employee.user.last_name,
            "email": employee.user.email,
            "url": (
                "http://testserver"
                + reverse("accounts:user-detail", [employee.user.pk])
            ),
        }
        self.assertIn("display_quality_reviewer", response_dict)
        self.assertDictContainsSubset(
            expected_data, response_dict["display_quality_reviewer"]
        )

    def test_QR_added_triggers_notification_to_qr(self):
        """
        Project quality reviewer added triggers email notification to quality reviewer
        """
        MessageTypeFactory.create(slug="quality_reviewer_added_to_assessment")
        customer = CustomerFactory.create(name="Anonymus")
        producing_organization = ProducingOrganizationFactory.create(
            customer__name="Kingpin INC."
        )
        new_quality_reviewer = EmployeeFactory.create(
            user__first_name="Totally not",
            user__last_name="Kingpin",
            user__email_notifications_on=True,
            user__language="en",
        )
        project = ProjectFactory.create(name="HYDRA", customer=customer)
        assessment = AssessmentFactory.create(
            project=project, producing_organization=producing_organization
        )

        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {
            "quality_reviewer": reverse(
                "accounts:user-detail", [new_quality_reviewer.user.pk]
            )
        }
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual(1, len(mail.outbox))
        message = mail.outbox[0]
        self.assertEqual(
            "\n\n\nDear Totally not Kingpin,\n\nThis automated email is to notify you that you have been assigned to do Quality Control for the assessment Kingpin INC. for the project HYDRA of Anonymus.\n\nKind regards,\nSCOPEinsight\n",
            message.body,
        )

    def test_FQR_added_triggers_notification_to_qr(self):
        """
        Project quality reviewer added triggers email notification to quality reviewer
        """
        MessageTypeFactory.create(slug="quality_reviewer_added_to_assessment")
        customer = CustomerFactory.create(name="Anonymus")
        producing_organization = ProducingOrganizationFactory.create(
            customer__name="Kingpin INC."
        )
        new_quality_reviewer = EmployeeFactory.create(
            user__first_name="Totally not",
            user__last_name="Kingpin",
            user__email_notifications_on=True,
            user__language="en",
        )
        project = ProjectFactory.create(name="HYDRA", customer=customer)
        assessment = AssessmentFactory.create(
            project=project, producing_organization=producing_organization
        )

        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {
            "financial_quality_reviewer": reverse(
                "accounts:user-detail", [new_quality_reviewer.user.pk]
            )
        }
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual(1, len(mail.outbox))
        message = mail.outbox[0]
        self.assertEqual(
            "\n\n\nDear Totally not Kingpin,\n\nThis automated email is to notify you that you have been assigned to do Quality Control for the assessment Kingpin INC. for the project HYDRA of Anonymus.\n\nKind regards,\nSCOPEinsight\n",
            message.body,
        )

    def test_new_QR_triggers_notification_to_new_qr(self):
        """
        Assessment quality reviewer changed triggers email notification to new quality reviewer
        """
        MessageTypeFactory.create(slug="quality_reviewer_added_to_assessment")
        customer = CustomerFactory.create(name="Anonymus")
        producing_organization = ProducingOrganizationFactory.create(
            customer__name="Kingpin INC."
        )
        quality_reviewer = EmployeeFactory.create(
            user__first_name="Mr.", user__last_name="Kingpin"
        )
        new_quality_reviewer = EmployeeFactory.create(
            user__first_name="Totally not",
            user__last_name="Kingpin",
            user__email_notifications_on=True,
            user__language="en",
        )
        project = ProjectFactory.create(name="HYDRA", customer=customer)
        assessment = AssessmentFactory.create(
            project=project,
            producing_organization=producing_organization,
            quality_reviewer=quality_reviewer.user,
        )

        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {
            "quality_reviewer": reverse(
                "accounts:user-detail", [new_quality_reviewer.user.pk]
            )
        }
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual(1, len(mail.outbox))
        message = mail.outbox[0]
        self.assertEqual(
            "\n\n\nDear Totally not Kingpin,\n\nThis automated email is to notify you that you have been assigned to do Quality Control for the assessment Kingpin INC. for the project HYDRA of Anonymus.\n\nKind regards,\nSCOPEinsight\n",
            message.body,
        )

    def test_new_FQR_triggers_notification_to_new_qr(self):
        """
        Assessment quality reviewer changed triggers email notification to new quality reviewer
        """
        MessageTypeFactory.create(slug="quality_reviewer_added_to_assessment")
        customer = CustomerFactory.create(name="Anonymus")
        producing_organization = ProducingOrganizationFactory.create(
            customer__name="Kingpin INC."
        )
        financial_quality_reviewer = EmployeeFactory.create(
            user__first_name="Mr.", user__last_name="Kingpin"
        )
        new_financial_quality_reviewer = EmployeeFactory.create(
            user__first_name="Totally not",
            user__last_name="Kingpin",
            user__email_notifications_on=True,
            user__language="en",
        )
        project = ProjectFactory.create(name="HYDRA", customer=customer)
        assessment = AssessmentFactory.create(
            project=project,
            producing_organization=producing_organization,
            financial_quality_reviewer=financial_quality_reviewer.user,
        )

        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {
            "financial_quality_reviewer": reverse(
                "accounts:user-detail", [new_financial_quality_reviewer.user.pk]
            )
        }
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual(1, len(mail.outbox))
        message = mail.outbox[0]
        self.assertEqual(
            "\n\n\nDear Totally not Kingpin,\n\nThis automated email is to notify you that you have been assigned to do Quality Control for the assessment Kingpin INC. for the project HYDRA of Anonymus.\n\nKind regards,\nSCOPEinsight\n",
            message.body,
        )

    def test_new_QR_triggers_notification_to_old_qr(self):
        """
        Assessment quality reviewer changed triggers email notification to old quality reviewer
        """
        MessageTypeFactory.create(slug="quality_reviewer_removed_from_assessment")
        customer = CustomerFactory.create(name="Anonymus")
        producing_organization = ProducingOrganizationFactory.create(
            customer__name="Kingpin INC."
        )
        quality_reviewer = EmployeeFactory.create(
            user__first_name="Mr.",
            user__last_name="Kingpin",
            user__email_notifications_on=True,
            user__language="en",
        )
        new_quality_reviewer = EmployeeFactory.create(
            user__first_name="Totally not", user__last_name="Kingpin"
        )
        project = ProjectFactory.create(
            name="HYDRA", customer=customer, quality_reviewer=quality_reviewer.user
        )
        assessment = AssessmentFactory.create(
            project=project,
            producing_organization=producing_organization,
            quality_reviewer=quality_reviewer.user,
        )

        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {
            "quality_reviewer": reverse(
                "accounts:user-detail", [new_quality_reviewer.user.pk]
            )
        }
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual(1, len(mail.outbox))
        message = mail.outbox[0]
        self.assertEqual(
            "\n\n\nDear Mr. Kingpin,\n\nThis automated email is to notify you that you have been unassigned to do Quality Control for the assessment Kingpin INC. for the project HYDRA of Anonymus.\n\nKind regards,\nSCOPEinsight\n",
            message.body,
        )

    def test_new_FQR_triggers_notification_to_old_qr(self):
        """
        Assessment quality reviewer changed triggers email notification to old quality reviewer
        """
        MessageTypeFactory.create(slug="quality_reviewer_removed_from_assessment")
        customer = CustomerFactory.create(name="Anonymus")
        producing_organization = ProducingOrganizationFactory.create(
            customer__name="Kingpin INC."
        )
        financial_quality_reviewer = EmployeeFactory.create(
            user__first_name="Mr.",
            user__last_name="Kingpin",
            user__email_notifications_on=True,
            user__language="en",
        )
        new_financial_quality_reviewer = EmployeeFactory.create(
            user__first_name="Totally not", user__last_name="Kingpin"
        )
        project = ProjectFactory.create(
            name="HYDRA",
            customer=customer,
            financial_quality_reviewer=financial_quality_reviewer.user,
        )
        assessment = AssessmentFactory.create(
            project=project,
            producing_organization=producing_organization,
            financial_quality_reviewer=financial_quality_reviewer.user,
        )

        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {
            "financial_quality_reviewer": reverse(
                "accounts:user-detail", [new_financial_quality_reviewer.user.pk]
            )
        }
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual(1, len(mail.outbox))
        message = mail.outbox[0]
        self.assertEqual(
            "\n\n\nDear Mr. Kingpin,\n\nThis automated email is to notify you that you have been unassigned to do Quality Control for the assessment Kingpin INC. for the project HYDRA of Anonymus.\n\nKind regards,\nSCOPEinsight\n",
            message.body,
        )

    def test_new_AQR_triggers_notification_to_old_PQR_when_no_old_AQR(self):
        """
        Assessment quality reviewer changed triggers email notification to old quality reviewer
        """
        MessageTypeFactory.create(slug="quality_reviewer_removed_from_assessment")
        customer = CustomerFactory.create(name="Anonymus")
        producing_organization = ProducingOrganizationFactory.create(
            customer__name="Kingpin INC."
        )
        quality_reviewer = EmployeeFactory.create(
            user__first_name="Mr.",
            user__last_name="Kingpin",
            user__email="<EMAIL>",
            user__email_notifications_on=True,
            user__language="en",
        )
        new_quality_reviewer = EmployeeFactory.create(
            user__first_name="Totally not",
            user__last_name="Kingpin",
            user__email="<EMAIL>",
        )
        project = ProjectFactory.create(
            name="HYDRA", customer=customer, quality_reviewer=quality_reviewer.user
        )
        assessment = AssessmentFactory.create(
            project=project, producing_organization=producing_organization
        )

        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {
            "quality_reviewer": reverse(
                "accounts:user-detail", [new_quality_reviewer.user.pk]
            )
        }
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual(1, len(mail.outbox))
        message = mail.outbox[0]
        self.assertEqual(
            "\n\n\nDear Mr. Kingpin,\n\nThis automated email is to notify you that you have been unassigned to do Quality Control for the assessment Kingpin INC. for the project HYDRA of Anonymus.\n\nKind regards,\nSCOPEinsight\n",
            message.body,
        )

    def test_new_AFQR_triggers_notification_to_old_PFQR_when_no_old_AFQR(self):
        """
        Assessment quality reviewer changed triggers email notification to old quality reviewer
        """
        MessageTypeFactory.create(slug="quality_reviewer_removed_from_assessment")
        customer = CustomerFactory.create(name="Anonymus")
        producing_organization = ProducingOrganizationFactory.create(
            customer__name="Kingpin INC."
        )
        financial_quality_reviewer = EmployeeFactory.create(
            user__first_name="Mr.",
            user__last_name="Kingpin",
            user__email="<EMAIL>",
            user__email_notifications_on=True,
            user__language="en",
        )
        new_financial_quality_reviewer = EmployeeFactory.create(
            user__first_name="Totally not",
            user__last_name="Kingpin",
            user__email="<EMAIL>",
            user__email_notifications_on=True,
            user__language="en",
        )
        project = ProjectFactory.create(
            name="HYDRA",
            customer=customer,
            financial_quality_reviewer=financial_quality_reviewer.user,
        )
        assessment = AssessmentFactory.create(
            project=project, producing_organization=producing_organization
        )

        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {
            "financial_quality_reviewer": reverse(
                "accounts:user-detail", [new_financial_quality_reviewer.user.pk]
            )
        }
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual(1, len(mail.outbox))
        message = mail.outbox[0]
        self.assertEqual(
            "\n\n\nDear Mr. Kingpin,\n\nThis automated email is to notify you that you have been unassigned to do Quality Control for the assessment Kingpin INC. for the project HYDRA of Anonymus.\n\nKind regards,\nSCOPEinsight\n",
            message.body,
        )

    def test_can_order_by_language(self):
        """
        It should be possible to order assessments by language
        """
        AssessmentFactory.create(language="en")
        AssessmentFactory.create(language="es")
        AssessmentFactory.create(language="fr")
        url = reverse("assessments:assessment-list") + "?ordering=display_language"
        languages_and_expectations = [
            ["en", ["en", "fr", "es"]],  # English, French, Spanish
            ["es", ["es", "fr", "en"]],  # Español, Francés, Ingles
            ["fr", ["en", "es", "fr"]],  # Anglais, Espagnol, Français
        ]
        user = self.employee.user
        for language, expected_languages in languages_and_expectations:
            user.language = language
            user.save()
            response = self.client.get(url, content_type="application/json")
            response_dict = json.loads(response.content)
            self.assertIn("results", response_dict)
            languages = [item["language"] for item in response_dict["results"]]
            self.assertListEqual(expected_languages, languages)
        url = reverse("assessments:assessment-list") + "?ordering=-display_language"
        for language, expected_languages in languages_and_expectations:
            user.language = language
            user.save()
            response = self.client.get(url, content_type="application/json")
            response_dict = json.loads(response.content)
            self.assertIn("results", response_dict)
            languages = [item["language"] for item in response_dict["results"]]
            self.assertListEqual(expected_languages[::-1], languages)

    def test_ax_can_view_assignment_read_only_without_project(self):
        assessment = AssessmentFactory.create(project=None)
        AssessmentAssignmentFactory.create(assessment=assessment)
        url = reverse("assessments:assessment-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("results", response_dict)
        self.assertEqual(1, len(response_dict["results"]))
        result = response_dict["results"][0]
        self.assertIn("assignments", result)
        self.assertEqual(1, len(result["assignments"]))
        assignment = result["assignments"][0]
        self.assertDictContainsSubset(
            {"ax_can_view_assignment_read_only": False}, assignment
        )


class AssessmentAssignmentTestCase(DenormMixin, EmployeeJWTTestCase):
    def test_only_show_assignments_when_submitted_at_least_once(self):
        """
        Employees should only see assignments
        that have been submitted at least once
        """
        assignment = AssessmentAssignmentFactory.create()
        url = reverse("assessments:assessmentassignment-list")
        data = {"only_submitted": "true"}
        response = self.client.get(url, data, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(0, response_dict["count"])
        assignment.locked_for_assessor = True
        assignment.save()
        response = self.client.get(url, data, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(1, response_dict["count"])

    def test_can_see_assessments_when_also_assessor(self):
        """
        It should be possible for an employee that's also an assessor to see
        assignments
        """
        AssessorFactory.create(user=self.jwt_user)
        AssessmentAssignmentFactory.create(submitted_at_least_once=True)
        url = reverse("assessments:assessmentassignment-list")
        data = {"only_submitted": "true"}
        response = self.client.get(url, data, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(1, response_dict["count"])

    def test_can_delete_assignment_of_locked_assessment(self):
        """
        Should be able to delete assignment when assessment is locked
        """
        assignment = AssessmentAssignmentFactory.create(
            locked_for_employee=True, locked_for_assessor=False
        )
        url = reverse("assessments:assessmentassignment-detail", [assignment.pk])
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(
            status.HTTP_204_NO_CONTENT, response.status_code, response.content
        )
        self.assertEqual(0, AssessmentAssignment.objects.count())

    @skip("This might not be necessary soon")
    def test_on_delete_of_assignment_assessment_stays_locked(self):
        """
        Assessment should stay locked on delete of an assignment
        """
        assessment = AssessmentFactory.create(
            locked_for_employee=True, locked_for_assessor=False
        )
        assignment = AssessmentAssignmentFactory.create(assessment=assessment)
        assignment_1 = AssessmentAssignmentFactory.create(assessment=assessment)
        url = reverse("assessments:assessmentassignment-detail", [assignment.pk])
        url_1 = reverse("assessments:assessmentassignment-detail", [assignment_1.pk])

        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(status.HTTP_204_NO_CONTENT, response.status_code)
        assessment.refresh_from_db()
        self.assertEqual(1, AssessmentAssignment.objects.count())

        response = self.client.get(url_1, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        assessment.refresh_from_db()
        self.assertTrue(assessment.locked_for_employee)
        self.assertFalse(assessment.locked_for_assessor)

    def test_on_delete_of_all_assignments_assessment_unlocks(self):
        """
        Assessment should unlock on delete of both assignments
        """
        assessment = AssessmentFactory.create()
        assignment = AssessmentAssignmentFactory.create(
            assessment=assessment, locked_for_employee=True, locked_for_assessor=False
        )
        assignment_1 = AssessmentAssignmentFactory.create(
            assessment=assessment, locked_for_employee=True, locked_for_assessor=False
        )
        url = reverse("assessments:assessmentassignment-detail", [assignment.pk])
        url_1 = reverse("assessments:assessmentassignment-detail", [assignment_1.pk])
        self.client.delete(url, content_type="application/json")
        self.client.delete(url_1, content_type="application/json")
        self.assertFalse(
            AssessmentAssignment.objects.filter(assessment=assessment.pk).exists()
        )

        assessment.refresh_from_db()

        self.assertFalse(assessment.locked_from_dashboard(self.jwt_user))

    def test_can_invite_new_assessor_after_deleting_assignment(self):
        """
        Should be possible to remove one assignment and create new assignment
        """
        assessment = AssessmentFactory.create()
        assignment_1 = AssessmentAssignmentFactory.create(assessment=assessment)
        AssessmentAssignmentFactory.create(assessment=assessment)

        url = reverse("assessments:assessmentassignment-detail", [assignment_1.pk])
        self.client.delete(url, content_type="application/json")
        assessment.refresh_from_db()

        self.assertEqual(
            1, AssessmentAssignment.objects.filter(assessment=assessment.pk).count()
        )

        assessor = AssessorFactory.create()
        data = {
            "assessment": reverse("assessments:assessment-detail", [assessment.pk]),
            "assessor": reverse("hrm:assessor-detail", [assessor.pk]),
        }
        invite_url = reverse("assessments:assessmentinvitation-list")
        response = self.client.post(
            invite_url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_201_CREATED, response.status_code)

    def test_invite_create_response_has_details(self):
        """
        The invite create response should have the appropriate amount of
        details
        """
        assessment = AssessmentFactory.create()
        assessor = AssessorFactory.create()
        data = {
            "assessment": reverse("assessments:assessment-detail", [assessment.pk]),
            "assessor": reverse("hrm:assessor-detail", [assessor.pk]),
        }
        invite_url = reverse("assessments:assessmentinvitation-list")
        response = self.client.post(
            invite_url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_201_CREATED, response.status_code)
        response_dict = json.loads(response.content)
        expected_dict = {
            "assessment": "http://testserver"
            + reverse("assessments:assessment-detail", [assessment.pk]),
            "assessor": "http://testserver"
            + reverse("hrm:assessor-detail", [assessor.pk]),
            "skill_types": [],
            "status": "new",
            "status_reason": "",
            "assigned_as": "assessor",
        }
        self.assertDictContainsSubset(expected_dict, response_dict)

    def test_can_invite_same_user_as_assessor_and_fin_specialist(self):
        """
        Should be possible to invite same user for both assessor and fin specialist roles
        """
        assessment = AssessmentFactory.create()
        user = AssessorFactory.create(financial_specialist=True, is_assessor=True)
        invitation_assessor = AssessmentInvitationFactory.create(
            assessor=user, assessment=assessment, assigned_as="assessor"
        )
        url = reverse(
            "assessments:assessmentinvitation-accept", [invitation_assessor.id]
        )
        response_assesor_accept = self.client.post(url, content_type="application/json")
        self.assertEqual(status.HTTP_201_CREATED, response_assesor_accept.status_code)

        invitation_financial_specialist = AssessmentInvitationFactory.create(
            assessor=user, assessment=assessment, assigned_as="financial_specialist"
        )
        url = reverse(
            "assessments:assessmentinvitation-accept",
            [invitation_financial_specialist.id],
        )
        response_accept_financial_specialist = self.client.post(
            url, content_type="application/json"
        )

        self.assertEqual(
            status.HTTP_201_CREATED, response_accept_financial_specialist.status_code
        )

    def test_raise_error_if_fin_specialist_invited(self):
        """
        Error raised when same user is invited twice to the same role
        """
        assessment = AssessmentFactory.create()
        user = AssessorFactory.create(financial_specialist=True, is_assessor=True)
        AssessmentInvitationFactory.create(
            assessor=user, assessment=assessment, assigned_as="financial_specialist"
        )

        with self.assertRaises(IntegrityError):
            with transaction.atomic():
                AssessmentInvitationFactory.create(
                    assessor=user,
                    assessment=assessment,
                    assigned_as="financial_specialist",
                )

    def test_can_search_assignments(self):
        """
        It should be possible to search assignments
        """
        # This test doesn't need asserts, it's checking against an Exception
        url = reverse("assessments:assessmentassignment-list")
        url += "?search=blub"
        self.client.get(url, content_type="application/json")


class ResponseTestCase(DenormMixin, EmployeeJWTTestCase):
    def test_is_completed_false_when_any_subresponse_empty(self):
        """
        When any subresponse is empty, is_completed should be False
        """
        response = ResponseFactory.create()
        SubResponseFactory.create(  # This is empty
            _response=response, subquestion__type="numeric", value=None
        )
        SubResponseFactory.create(  # This is not empty
            _response=response, subquestion__type="checkbox"
        )
        flush()
        response.refresh_from_db()
        self.assertFalse(response.is_completed)

    def test_is_completed_true_when_all_subresponses_not_empty(self):
        """
        When all subresponses are not empty, is_completed should be True
        """
        response = ResponseFactory.create()
        SubResponseFactory.create(  # This is not empty
            _response=response,
            subquestion__type="numeric",
            value=None,
            not_relevant=True,
        )
        SubResponseFactory.create(  # This is not empty
            _response=response, subquestion__type="checkbox"
        )
        flush()
        response.refresh_from_db()
        self.assertTrue(response.is_completed)

    def test_is_completed_flips_true_to_false(self):
        """
        When any subresponse becomes incomplete, the is_completed boolean
        should flip from True to False
        """
        response = ResponseFactory.create()
        subresponse = SubResponseFactory.create(  # This is not empty
            _response=response,
            subquestion__type="numeric",
            value=None,
            not_relevant=True,
        )
        SubResponseFactory.create(  # This is not empty
            _response=response, subquestion__type="checkbox"
        )
        flush()
        response.refresh_from_db()
        self.assertTrue(response.is_completed)
        subresponse.not_relevant = False
        subresponse.save()
        flush()
        response.refresh_from_db()
        self.assertFalse(response.is_completed)

    def test_is_completed_flips_false_to_true(self):
        """
        When the final subresponse becomes complete, the is_completed boolean
        should flip from False to True
        """
        response = ResponseFactory.create()
        subresponse = SubResponseFactory.create(  # This is empty
            _response=response, subquestion__type="numeric", value=None
        )
        SubResponseFactory.create(  # This is not empty
            _response=response, subquestion__type="checkbox"
        )
        flush()
        response.refresh_from_db()
        self.assertFalse(response.is_completed)
        subresponse.not_relevant = True
        subresponse.save()
        flush()
        response.refresh_from_db()
        self.assertTrue(response.is_completed)

    def test_can_not_accept_single_response_when_incomplete(self):
        """
        It should not be possible to accept a response when it is incomplete
        """
        response = ResponseFactory.create(accepted=False)
        SubResponseFactory.create(  # This is empty
            _response=response, subquestion__type="numeric", value=None
        )
        flush()
        url = reverse("assessments:response-detail", [response.pk])
        data = {"accepted": True}
        api_response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, api_response.status_code)
        response_dict = json.loads(api_response.content)
        self.assertEqual(
            ["An incomplete response can't be accepted"], response_dict["accepted"]
        )
        response.refresh_from_db()
        self.assertFalse(response.accepted)

    def test_can_not_accept_all_responses_when_any_incomplete(self):
        """
        It should not be possible to accept all responses when any are
        incomplete
        """
        response = ResponseFactory.create(accepted=False)
        SubResponseFactory.create(  # This is empty
            _response=response, subquestion__type="numeric", value=None
        )
        flush()
        url = reverse(
            "assessments:assessment-accept-all-responses", [response.assessment.pk]
        )
        api_response = self.client.post(url, content_type="application/json")
        self.assertEqual(status.HTTP_412_PRECONDITION_FAILED, api_response.status_code)
        response_dict = json.loads(api_response.content)
        self.assertEqual(
            (
                "One or more responses are incomplete, so it's not possible "
                "to accept all responses"
            ),
            response_dict,
        )
        response.refresh_from_db()
        self.assertFalse(response.accepted)


class AssessmentInvitationTestCase(DenormMixin, EmployeeJWTTestCase):
    @skip("Invitation doesn't flush in the celery anymore")
    def test_invitation_post_delays_flush(self):
        """
        AssessmentInvitation POST should use delayed flush
        """
        assessment = AssessmentFactory.create()
        assessor = AssessorFactory.create()
        data = {
            "assessment": reverse("assessments:assessment-detail", [assessment.pk]),
            "assessor": reverse("hrm:assessor-detail", [assessor.pk]),
            "assigned_as": "assessor",
        }
        url = reverse("assessments:assessmentinvitation-list")
        with patch("denorm.middleware.flush") as patched_flush:
            with patch("libs.middleware.delayed_flush") as patched_d_flush:

                def save(slf, *args, **kwargs):
                    with self.assertNumQueries(3):
                        response = super(AssessmentInvitationSerializer, slf).save(
                            *args, **kwargs
                        )
                    return response

                with patch.object(
                    AssessmentInvitationSerializer, "save", autospec=True
                ) as mocked_method:
                    mocked_method.side_effect = save
                    self.client.post(
                        url, json.dumps(data), content_type="application/json"
                    )
                    patched_flush.assert_not_called()
                    patched_d_flush.assert_not_called()
                    patched_d_flush.delay.assert_called_once()
