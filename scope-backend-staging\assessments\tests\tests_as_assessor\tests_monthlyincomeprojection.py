import json

from denorm import flush
from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import (
    MonthlyExpensesProjectionFactory,
    MonthlyIncomeProjectionFactory,
    NetMonthlyIncomeProjectionFactory,
    TotalMonthlyExpensesProjectionFactory,
    TotalMonthlyIncomeProjectionFactory,
)
from assessments.models import (
    NetMonthlyIncomeProjection,
    TotalMonthlyExpensesProjection,
    TotalMonthlyIncomeProjection,
)
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class MonthlyIncomeProjectionTestCase(DenormMixin, AssessorJWTTestCase):
    maxDiff = None

    def test_totals_work(self):
        net_income = NetMonthlyIncomeProjectionFactory.create()
        assessment = net_income.assessment
        total_income = TotalMonthlyIncomeProjectionFactory.create(assessment=assessment)
        self.assertIsNone(total_income.january)
        MonthlyIncomeProjectionFactory.create(assessment=assessment, january=10)
        flush()
        total_income = TotalMonthlyIncomeProjection.objects.get(pk=total_income.pk)
        self.assertEqual(10, total_income.january.amount)

        total_expenses = TotalMonthlyExpensesProjectionFactory.create(
            assessment=assessment
        )
        self.assertEqual(0, total_expenses.january.amount)
        MonthlyExpensesProjectionFactory.create(assessment=assessment, january=6)
        flush()
        total_expenses = TotalMonthlyExpensesProjection.objects.get(
            pk=total_expenses.pk
        )
        self.assertEqual(6, total_expenses.january.amount)
        net_income = NetMonthlyIncomeProjection.objects.get(pk=net_income.pk)
        self.assertEqual(4, net_income.january.amount)

    def test_totals_work_through_api(self):
        net_income = NetMonthlyIncomeProjectionFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        assessment = net_income.assessment
        total_income = TotalMonthlyIncomeProjectionFactory.create(assessment=assessment)
        total_expenses = TotalMonthlyExpensesProjectionFactory.create(
            assessment=assessment
        )
        data = {
            "assessment": reverse("assessments:assessment-detail", [assessment.pk]),
            "january": 10,
        }
        url = reverse("assessments:monthlyincomeprojection-list")
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_201_CREATED, response.status_code)
        url = reverse(
            "assessments:totalmonthlyincomeprojection-detail", [total_income.pk]
        )
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        total_income = json.loads(response.content)
        self.assertEqual("10", total_income["january"]["amount"])

        data = {
            "assessment": reverse("assessments:assessment-detail", [assessment.pk]),
            "january": 6,
        }
        url = reverse("assessments:monthlyexpensesprojection-list")
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_201_CREATED, response.status_code)
        url = reverse(
            "assessments:totalmonthlyexpensesprojection-detail", [total_expenses.pk]
        )
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        total_expenses = json.loads(response.content)
        self.assertEqual("6", total_expenses["january"]["amount"])

        url = reverse("assessments:netmonthlyincomeprojection-detail", [net_income.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        net_income = json.loads(response.content)
        self.assertEqual("4", net_income["january"]["amount"])
