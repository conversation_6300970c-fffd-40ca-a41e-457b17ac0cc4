import json
import shutil
from tempfile import mkdtemp

from django.conf import settings
from mock import patch
from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import DocumentAvailabilityResponseFactory
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class DocumentAvailabilityResponseTestCase(DenormMixin, AssessorJWTTestCase):
    def test_filename_is_preserved_when_creating_document(self):
        temp_media_dir = mkdtemp(dir=settings.BASE_DIR)
        with self.settings(MEDIA_ROOT=temp_media_dir):
            obj = DocumentAvailabilityResponseFactory.create(
                assessment__assessmentassignments__assessor=self.assessor
            )
            url = reverse("assessments:documentavailabilityresponsedocument-list")
            data = {
                "documentavailabilityresponse": reverse(
                    "assessments:documentavailabilityresponse-detail", [obj.pk]
                ),
                "file": "data:text/plain;base64,Ygo=",
                "file_name": "a.txt",
            }
            response = self.client.post(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(
                status.HTTP_201_CREATED, response.status_code, response.content
            )
            response_dict = json.loads(response.content)
            self.assertEqual("a.txt", response_dict["file_name"])
            self.assertEqual(
                "http://testserver/media/assessments/documentavailabilityresponsedocument/a.txt",
                response_dict["file"],
            )
        shutil.rmtree(temp_media_dir)

    def test_filename_can_be_long(self):
        """
        filename can be under 500 characters and not return 500
        """
        temp_media_dir = mkdtemp(dir=settings.BASE_DIR)
        with patch.multiple(settings, MEDIA_ROOT=temp_media_dir):
            obj = DocumentAvailabilityResponseFactory.create(
                assessment__assessmentassignments__assessor=self.assessor
            )
            url = reverse("assessments:documentavailabilityresponsedocument-list")
            data = {
                "documentavailabilityresponse": reverse(
                    "assessments:documentavailabilityresponse-detail", [obj.pk]
                ),
                "file": "data:text/plain;base64,Ygo=",
                "file_name": "The_Dark_Lord_has_Nine_But_we_have_One,_mightier_than_they:_the_White_Rider_He_has_passed_through_the_fire_and_the_abyss,_and_they_shall_fear_him_We_will_go_where_he_leads.txt",
            }
            response = self.client.post(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(status.HTTP_201_CREATED, response.status_code)
        shutil.rmtree(temp_media_dir)

    def test_available_defaults_to_blank(self):
        """
        available should default to blank
        """
        obj = DocumentAvailabilityResponseFactory()
        self.assertEqual("", obj.available)

    def test_available_accepts_yes(self):
        """
        Available should accept the string 'yes'
        """
        obj = DocumentAvailabilityResponseFactory(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:documentavailabilityresponse-detail", [obj.pk])
        data = {"available": "yes"}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code, response.content)
        obj.refresh_from_db()
        self.assertEqual("yes", obj.available)

    def test_available_accepts_no(self):
        """
        Available should accept the string 'no'
        """
        obj = DocumentAvailabilityResponseFactory(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:documentavailabilityresponse-detail", [obj.pk])
        data = {"available": "no"}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code, response.content)
        obj.refresh_from_db()
        self.assertEqual("no", obj.available)

    def test_available_accepts_not_relevant(self):
        """
        Available should accept the string 'not relevant'
        """
        obj = DocumentAvailabilityResponseFactory(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:documentavailabilityresponse-detail", [obj.pk])
        data = {"available": "not relevant"}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code, response.content)
        obj.refresh_from_db()
        self.assertEqual("not relevant", obj.available)

    def test_available_does_not_accept_other_strings(self):
        """
        Available should not accept other strings, such as 'true'
        """
        obj = DocumentAvailabilityResponseFactory(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:documentavailabilityresponse-detail", [obj.pk])
        data = {"available": "true"}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(
            status.HTTP_400_BAD_REQUEST, response.status_code, response.content
        )
        response_dict = json.loads(response.content)
        self.assertIn("available", response_dict)
        self.assertEqual(['"true" is not a valid choice.'], response_dict["available"])
        obj.refresh_from_db()
        self.assertEqual("", obj.available)
