import json

from denorm import flush
from rest_framework.reverse import reverse

from assessments.factories import (
    AssessmentAssignmentFactory,
    AssessmentFactory,
    BalanceSheetFactory,
)
from assessments.models import AssessmentAssignment, BalanceSheet
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class BalanceSheetTestCase(DenormMixin, AssessorJWTTestCase):
    def test_patching_inventories_returns_next_year_as_well(self):
        """
        When patching inventories, averages are computed
        and should be returned for next year as well
        all objects need id
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor,
            assessmentassignments__assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
            currency="USD",
        )
        for year in range(2011, 2016):
            BalanceSheetFactory.create(assessment=assessment, year=year)
        (
            bs_future,
            bs_next,
            bs_current,
            bs_previous,
        ) = assessment.balancesheets.all().order_by("-year")[:4]
        bs_future.inventories = 200
        bs_future.save()
        bs_next.inventories = 200
        bs_next.save()
        bs_current.inventories = 200
        bs_current.save()
        bs_previous.inventories = 200
        bs_previous.save()
        flush()
        url = reverse("assessments:balancesheet-detail", [bs_current.pk])
        data = {"inventories": 100}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        response_dict = json.loads(response.content)
        self.assertIn("average_inventory", response_dict)
        self.assertIn("amount", response_dict["average_inventory"])
        self.assertEqual("150", response_dict["average_inventory"]["amount"])
        self.assertIn("next", response_dict)
        self.assertIn("average_inventory", response_dict["next"])
        self.assertIn("amount", response_dict["next"]["average_inventory"])
        self.assertEqual("150", response_dict["next"]["average_inventory"]["amount"])

    def test_manual_total_assets_default(self):
        """
        Manual total assets should default to None
        """
        balance_sheet = BalanceSheetFactory.create()
        self.assertIsNone(balance_sheet.manual_total_assets)

    def test_get_total_assets(self):
        """
        It should be possible to get total assets from api
        """
        balance_sheet = BalanceSheetFactory.create(
            manual_total_assets=100,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        url = reverse("assessments:balancesheet-detail", [balance_sheet.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(200, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("manual_total_assets", response_dict)
        self.assertEqual(
            {"currency": "XYZ", "amount": "100"}, response_dict["manual_total_assets"]
        )

    def test_set_total_assets(self):
        """
        It should be possible to set total assets through api
        """
        balance_sheet = BalanceSheetFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            assessment__assessmentassignments__assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        data = {"manual_total_assets": 100}
        url = reverse("assessments:balancesheet-detail", [balance_sheet.pk])
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(200, response.status_code)
        balance_sheet.refresh_from_db()
        self.assertEqual(100, balance_sheet.manual_total_assets.amount)

    def test_can_delete(self):
        """
        Fin Assessor should be able to delete the BS
        """
        balance_sheet = BalanceSheetFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            assessment__assessmentassignments__assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        url = reverse("assessments:balancesheet-detail", [balance_sheet.pk])
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(204, response.status_code)
        self.assertEqual(0, BalanceSheet.objects.count())

    def test_can_delete_double_role(self):
        """
        Assessor should be able to delete the BS, when the assessor
        is both financial assessor and regular assessor and the regular kind
        has been submitted.
        """
        balance_sheet = BalanceSheetFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            assessment__assessmentassignments__assigned_as=AssessmentAssignment.AS_ASSESSOR,
        )
        assignment = balance_sheet.assessment.assessmentassignments.get()
        AssessmentAssignmentFactory.create(
            assessment=assignment.assessment,
            assessor=assignment.assessor,
            assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        assignment.locked_for_assessor = True
        assignment.locked_for_employee = False
        assignment.save()
        url = reverse("assessments:balancesheet-detail", [balance_sheet.pk])
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(204, response.status_code)
        self.assertEqual(0, BalanceSheet.objects.count())

    def test_can_not_delete(self):
        """
        Regular Assessor should not be able to delete the BS
        """
        balance_sheet = BalanceSheetFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            assessment__assessmentassignments__assigned_as=AssessmentAssignment.AS_ASSESSOR,
        )
        url = reverse("assessments:balancesheet-detail", [balance_sheet.pk])
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(403, response.status_code)
        self.assertEqual(1, BalanceSheet.objects.count())

    def test_can_not_delete_double_role(self):
        """
        Assessor should not be able to delete the BS, when the assessor
        is both financial assessor and regular assessor and the financial kind
        has been submitted.
        """
        balance_sheet = BalanceSheetFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            assessment__assessmentassignments__assigned_as=AssessmentAssignment.AS_ASSESSOR,
        )
        assignment = balance_sheet.assessment.assessmentassignments.get()
        fin_assignment = AssessmentAssignmentFactory.create(
            assessment=assignment.assessment,
            assessor=assignment.assessor,
            assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        fin_assignment.locked_for_assessor = True
        fin_assignment.locked_for_employee = False
        fin_assignment.save()
        url = reverse("assessments:balancesheet-detail", [balance_sheet.pk])
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(403, response.status_code)
        self.assertEqual(1, BalanceSheet.objects.count())
