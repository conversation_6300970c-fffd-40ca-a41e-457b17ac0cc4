from django.db import migrations


def back_and_forth(apps, schema):
    Product = apps.get_model("assessments", "Product")

    # Flip name and type fields
    for item in Product.objects.all():
        _type = item.type
        item.type = item.name
        item.name = _type
        item.save()


class Migration(migrations.Migration):

    dependencies = [("assessments", "0182_auto_20160613_0705")]

    operations = [migrations.RunPython(back_and_forth, back_and_forth)]
