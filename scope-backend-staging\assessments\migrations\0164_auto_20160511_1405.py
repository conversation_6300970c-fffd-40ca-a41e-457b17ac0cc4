from django.db import migrations


def make_sure_monthly_projections_exist(apps, schema_editor):
    Assessment = apps.get_model("assessments", "Assessment")
    TotalMonthlyIncomeProjection = apps.get_model(
        "assessments", "TotalMonthlyIncomeProjection"
    )
    TotalMonthlyExpensesProjection = apps.get_model(
        "assessments", "TotalMonthlyExpensesProjection"
    )
    NetMonthlyIncomeProjection = apps.get_model(
        "assessments", "NetMonthlyIncomeProjection"
    )
    assessments_missing_total_income = Assessment.objects.exclude(
        id__in=(
            TotalMonthlyIncomeProjection.objects.all().values_list(
                "assessment", flat=True
            )
        )
    )
    for assessment in assessments_missing_total_income:
        TotalMonthlyIncomeProjection.objects.create(assessment=assessment)
    assessments_missing_total_expenses = Assessment.objects.exclude(
        id__in=(
            TotalMonthlyExpensesProjection.objects.all().values_list(
                "assessment", flat=True
            )
        )
    )
    for assessment in assessments_missing_total_expenses:
        TotalMonthlyExpensesProjection.objects.create(assessment=assessment)
    assessments_missing_net_income = Assessment.objects.exclude(
        id__in=(
            NetMonthlyIncomeProjection.objects.all().values_list(
                "assessment", flat=True
            )
        )
    )
    for assessment in assessments_missing_net_income:
        NetMonthlyIncomeProjection.objects.create(assessment=assessment)


class Migration(migrations.Migration):

    dependencies = [("assessments", "0163_auto_20160509_1554")]

    operations = [migrations.RunPython(make_sure_monthly_projections_exist)]
