# Generated by Django 1.11.12 on 2018-04-30 15:47


from django.db import migrations


def copy_locks_from_assessment_to_assignment(apps, schema_editor):
    AssessmentAssignment = apps.get_model("assessments", "AssessmentAssignment")
    for aa in AssessmentAssignment.objects.all():
        aa.locked_for_employee = aa.assessment.locked_for_employee
        aa.locked_for_assessor = aa.assessment.locked_for_assessor
        aa.save()


class Migration(migrations.Migration):

    dependencies = [("assessments", "0314_auto_20180430_1547")]

    operations = [migrations.RunPython(copy_locks_from_assessment_to_assignment)]
