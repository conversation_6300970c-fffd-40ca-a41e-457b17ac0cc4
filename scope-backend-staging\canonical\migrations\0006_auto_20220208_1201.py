# Generated by Django 3.1.14 on 2022-02-08 11:01

from django.db import migrations


def update_question_type(apps, schema_editor):
    CanonicalSubQuestion = apps.get_model("canonical", "CanonicalSubQuestion")

    for subquestion in CanonicalSubQuestion.objects.filter(
        title="Which part of the objectives has been achieved in the past 12 months?"
    ):
        CanonicalSubQuestion.objects.filter(id=subquestion.id).update(type="mpc")


class Migration(migrations.Migration):

    dependencies = [
        ("canonical", "0005_auto_20220208_1157"),
    ]

    operations = [migrations.RunPython(update_question_type)]
