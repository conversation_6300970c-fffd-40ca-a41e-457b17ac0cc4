from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0056_tooltype_description"),
        ("hrm", "0013_assessor_second_region"),
        ("projects", "0012_project_submitted_for_approval"),
        ("assessments", "0145_auto_20160204_2108"),
    ]

    operations = [
        migrations.CreateModel(
            name="DraftAssessment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("producing_organization_name", models.TextField()),
                ("producing_organization_contact_first_name", models.TextField()),
                ("producing_organization_contact_last_name", models.TextField()),
                ("producing_organization_contact_phone_number", models.TextField()),
                ("producing_organization_contact_email", models.TextField()),
                ("global_region", models.TextField()),
                ("country", models.TextField()),
                ("local_region", models.TextField()),
                ("deadline", models.DateField()),
                (
                    "accountant",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="draft_assessments_as_accountant",
                        blank=True,
                        to="hrm.Assessor",
                        null=True,
                    ),
                ),
                (
                    "assessor",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="draft_assessments_as_assessor",
                        to="hrm.Assessor",
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(on_delete=models.CASCADE, to="projects.Project"),
                ),
                (
                    "tool_type",
                    models.ForeignKey(on_delete=models.CASCADE, to="products.ToolType"),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        )
    ]
