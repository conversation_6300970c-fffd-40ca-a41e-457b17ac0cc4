from django.db import migrations, models

import libs.field_helpers


class Migration(migrations.Migration):

    dependencies = [("assessments", "0082_auto_20150210_1607")]

    operations = [
        migrations.CreateModel(
            name="AssessmentPurpose",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("name", libs.field_helpers.LowerCaseTextField(unique=True)),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name="assessment",
            name="purposes",
            field=models.ManyToManyField(to="assessments.AssessmentPurpose"),
            preserve_default=True,
        ),
    ]
