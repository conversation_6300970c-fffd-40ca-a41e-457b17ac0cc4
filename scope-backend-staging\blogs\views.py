import datetime

import pytz
from django.db.models import Max
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.filters import SearchFilter
from rest_framework.response import Response

from libs.generic_helpers import string_to_boolean
from libs.rest_framework_helpers.view_mixins import ViewSetMixins

from .models import Blog, BlogSet
from .serializers import BlogSerializer, BlogSetSerializer


class BlogViewSet(ViewSetMixins, viewsets.ModelViewSet):
    queryset = Blog.objects.all()
    serializer_class = BlogSerializer


class BlogSetViewSet(ViewSetMixins, viewsets.ModelViewSet):
    """
    This endpoint provides information on blogs.
    """

    queryset = BlogSet.objects.all()
    serializer_class = BlogSetSerializer
    filter_backends = (SearchFilter,)
    search_fields = ("subject", "body", "language")

    def get_queryset(self):
        queryset = super(BlogSetViewSet, self).get_queryset()
        queryset = self.apply_read_filter(queryset)
        queryset = self.apply_archived_filter(queryset)
        return queryset

    def apply_read_filter(self, queryset):
        """
        Apply read filter
        """
        filter_value = string_to_boolean(self.request.query_params.get("read_by", None))
        if filter_value is not None:
            queryset = queryset.filter(read_by=self.request.user)
        return queryset

    def apply_archived_filter(self, queryset):
        """
        Apply archived filter
        """
        filter_value = string_to_boolean(
            self.request.query_params.get("archived_by", None)
        )
        if filter_value is not None:
            queryset = queryset.filter(archived_by=self.request.user)
        return queryset

    @action(detail=True, methods=["post"])
    def mark_as_read(self, request, pk):
        """
        Mark as read
        """
        blog_set = self.get_object()
        read = blog_set.read_by
        read.add(self.request.user)
        blog_set.modified_at = datetime.datetime.now(pytz.utc)
        blog_set.save()
        return Response({"success": True})

    @action(detail=True, methods=["post"])
    def mark_as_unread(self, request, pk):
        """
        Mark as read
        """
        blog_set = self.get_object()
        read = blog_set.read_by
        read.remove(self.request.user)
        blog_set.modified_at = datetime.datetime.now(pytz.utc)
        blog_set.save()
        return Response({"success": True})

    @action(detail=True, methods=["post"])
    def mark_as_archived(self, request, pk):
        """
        Mark as archived
        """
        blog_set = self.get_object()
        archived = blog_set.archived_by
        archived.add(self.request.user)
        blog_set.modified_at = datetime.datetime.now(pytz.utc)
        blog_set.save()
        return Response({"success": True})

    @action(detail=True, methods=["post"])
    def mark_as_unarchived(self, request, pk):
        """
        Mark as unarchived
        """
        blog_set = self.get_object()
        archived = blog_set.archived_by
        archived.remove(self.request.user)
        blog_set.modified_at = datetime.datetime.now(pytz.utc)
        blog_set.save()
        return Response({"success": True})

    def get_most_recent_deletion(self, related_models):
        """
        Count archived blog as deleted for the sake of 304 responses
        """
        max_date = super(BlogSetViewSet, self).get_most_recent_deletion(related_models)
        archived_blogs = BlogSet.objects.filter(archived_by=self.request.user)
        max_archival_date = archived_blogs.aggregate(Max("modified_at"))[
            "modified_at__max"
        ]
        if max_date is None and max_archival_date is None:
            return None
        elif max_date is None:
            return max_archival_date
        elif max_archival_date is None:
            return max_date
        else:
            return max(max_date, max_archival_date)
