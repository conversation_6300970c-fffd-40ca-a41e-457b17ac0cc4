from django.test import TestCase

from assessments.factories import ProductFactory
from libs.test_helpers import DenormMixin


class FaoItemCodeTestCase(DenormMixin, TestCase):
    def test_item_code_autofilled(self):
        product = ProductFactory.create(name="Palm kernels")
        self.assertEqual("0256", product.fao_item_code)

    def test_unknown_name(self):
        product = ProductFactory.create(name="Not a fao name")
        self.assertEqual("", product.fao_item_code)
