from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0117_response_question_display_position")]

    operations = [
        migrations.CreateModel(
            name="LandUseForestry",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("area", models.DecimalField(max_digits=10, decimal_places=2)),
                ("unit", models.TextField()),
                (
                    "_assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        editable=False,
                        to="assessments.Assessment",
                        null=True,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="TotalLandUseForestry",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "total_used_area",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=10,
                        decimal_places=2,
                        blank=True,
                    ),
                ),
                ("unit", models.TextField(editable=False, blank=True)),
                (
                    "_assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        editable=False,
                        to="assessments.Assessment",
                        null=True,
                    ),
                ),
                (
                    "agriculture",
                    models.OneToOneField(
                        on_delete=models.CASCADE,
                        related_name="totallanduse2",
                        null=True,
                        blank=True,
                        to="assessments.LandUseForestry",
                    ),
                ),
                (
                    "cultural",
                    models.OneToOneField(
                        on_delete=models.CASCADE,
                        related_name="totallanduse4",
                        null=True,
                        blank=True,
                        to="assessments.LandUseForestry",
                    ),
                ),
                (
                    "forestry",
                    models.OneToOneField(
                        on_delete=models.CASCADE,
                        related_name="totallanduse1",
                        null=True,
                        blank=True,
                        to="assessments.LandUseForestry",
                    ),
                ),
                (
                    "infrastructure",
                    models.OneToOneField(
                        on_delete=models.CASCADE,
                        related_name="totallanduse3",
                        null=True,
                        blank=True,
                        to="assessments.LandUseForestry",
                    ),
                ),
                (
                    "not_used",
                    models.OneToOneField(
                        on_delete=models.CASCADE,
                        related_name="totallanduse7",
                        null=True,
                        blank=True,
                        to="assessments.LandUseForestry",
                    ),
                ),
                (
                    "other",
                    models.OneToOneField(
                        on_delete=models.CASCADE,
                        related_name="totallanduse6",
                        null=True,
                        blank=True,
                        to="assessments.LandUseForestry",
                    ),
                ),
                (
                    "water",
                    models.OneToOneField(
                        on_delete=models.CASCADE,
                        related_name="totallanduse5",
                        null=True,
                        blank=True,
                        to="assessments.LandUseForestry",
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.AlterModelOptions(
            name="basicfinancialinfo", options={"ordering": ("-year",)}
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="land_used_for_forestry",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="producingorganizationdetails",
                null=True,
                blank=True,
                to="assessments.TotalLandUseForestry",
            ),
            preserve_default=True,
        ),
    ]
