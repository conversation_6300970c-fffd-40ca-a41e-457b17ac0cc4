from django.db import migrations


def set_submitted_at_least_once_to_true(apps, schema_editor):
    Assessment = apps.get_model("assessments", "Assessment")
    Assessment.objects.all().update(submitted_at_least_once=True)


class Migration(migrations.Migration):

    dependencies = [("assessments", "0213_assessment_submitted_at_least_once")]

    operations = [migrations.RunPython(set_submitted_at_least_once_to_true)]
