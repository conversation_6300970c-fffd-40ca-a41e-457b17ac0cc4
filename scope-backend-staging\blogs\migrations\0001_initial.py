# Generated by Django 1.11.9 on 2018-03-22 14:40


import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [migrations.swappable_dependency(settings.AUTH_USER_MODEL)]

    operations = [
        migrations.CreateModel(
            name="Blog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("subject", models.TextField()),
                ("body", models.TextField()),
                (
                    "language",
                    models.CharField(
                        choices=[
                            ("en", "English"),
                            ("nl", "Dutch"),
                            ("fr", "French"),
                            ("es", "Spanish"),
                        ],
                        default="en",
                        max_length=2,
                    ),
                ),
            ],
            options={"ordering": ("pk",)},
        ),
        migrations.CreateModel(
            name="BlogSet",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "archived_by",
                    models.ManyToManyField(
                        blank=True,
                        related_name="archived_blogs",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "read_by",
                    models.ManyToManyField(
                        blank=True,
                        related_name="read_blogs",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"ordering": ("-created_at",)},
        ),
        migrations.AddField(
            model_name="blog",
            name="blog_set",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="blogs",
                to="blogs.BlogSet",
            ),
        ),
    ]
