import json

from django.core.management.base import BaseCommand

from accounts.models import User


class Command(BaseCommand):
    help = "Fill training_id with user id's with matching emails from prod. First run `get_trainee_users_id_and_email.py`"

    def handle(self, *args, **options):
        users_not_found_on_prod = []
        with open("trainee_user_details.json") as json_file:
            trainee_user_details = json.load(json_file)
            for trainee_user in trainee_user_details["trainee_users"]:
                try:
                    prod_user = User.objects.get(email=trainee_user["email"])
                except User.DoesNotExist:
                    users_not_found_on_prod.append(trainee_user)
                prod_user.training_id = trainee_user["id"]
                prod_user.can_access_trainee = True
                prod_user.save()

        with open("users_not_found_on_prod.json", "w") as json_file:
            json.dump(users_not_found_on_prod, json_file)
