import mptt.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0018_auto_20150102_1526"),
        ("assessments", "0044_assessment_value_chain_financial_info_comment"),
    ]

    operations = [
        migrations.CreateModel(
            name="SectionResponse",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "score",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=4,
                        decimal_places=3,
                        blank=True,
                    ),
                ),
                (
                    "weight",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=4,
                        blank=True,
                    ),
                ),
                ("lft", models.PositiveIntegerField(editable=False, db_index=True)),
                ("rght", models.PositiveIntegerField(editable=False, db_index=True)),
                ("tree_id", models.PositiveIntegerField(editable=False, db_index=True)),
                ("level", models.PositiveIntegerField(editable=False, db_index=True)),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="section_responses",
                        to="assessments.Assessment",
                    ),
                ),
                (
                    "parent",
                    mptt.fields.TreeForeignKey(
                        on_delete=models.CASCADE,
                        related_name="children",
                        blank=True,
                        to="assessments.SectionResponse",
                        null=True,
                    ),
                ),
                (
                    "section",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="section_responses",
                        to="products.Section",
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name="assessment",
            name="score",
            field=models.DecimalField(
                null=True, editable=False, max_digits=4, decimal_places=3, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="response",
            name="_section_response",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="responses",
                blank=True,
                to="assessments.SectionResponse",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="response",
            name="weight",
            field=models.DecimalField(
                null=True, editable=False, max_digits=6, decimal_places=4, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="response",
            name="assessor",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="responses",
                blank=True,
                to="hrm.Assessor",
                null=True,
            ),
            preserve_default=True,
        ),
    ]
