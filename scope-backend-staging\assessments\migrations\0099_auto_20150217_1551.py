from collections import Counter

from django.db import migrations


def uniqueify_financialinfos(apps, schema_editor):
    BalanceSheet = apps.get_model("assessments", "BalanceSheet")
    ProfitLossStatement = apps.get_model("assessments", "ProfitLossStatement")
    CashFlowStatement = apps.get_model("assessments", "CashFlowStatement")
    for model in (BalanceSheet, ProfitLossStatement, CashFlowStatement):
        constraint_counter = Counter(
            model.objects.all().values_list("assessment_id", "year")
        )
        for (assessment_id, year), count in constraint_counter.most_common():
            if count <= 1:
                break
            objects_to_move = model.objects.filter(
                assessment_id=assessment_id, year=year
            )[: count - 1]
            for obj in objects_to_move:
                obj.year = (
                    min(
                        list(
                            model.objects.filter(
                                assessment_id=assessment_id
                            ).values_list("year", flat=True)
                        )
                    )
                    - 1
                )
                obj.save()


class Migration(migrations.Migration):

    dependencies = [("assessments", "0098_auto_20150217_1536")]

    operations = [migrations.RunPython(uniqueify_financialinfos)]
