import datetime

import pytz
import tablib
from django.core.management.base import BaseCommand

from assessments.models import Assessment, ResponseComment


class Command(BaseCommand):
    help = "Get comments on all assessments made in 2017"

    def handle(self, *args, **options):
        """
        Project
        Assessee
        Assessor
        Quality Reviewer
        Comment
        Comment by
        Comment tab (name of the tab)
        Question number
        """
        filename = "assessment comments 2017 - {}.xlsx".format(
            datetime.datetime.now(pytz.utc)
        )
        queryset = Assessment.objects.filter(date__gte="2017-01-01")

        headers = [
            "Project",
            "Assessee",
            "Assessor",
            "Quality Reviewer",
            "Comment",
            "Comment by",
            "Comment location",
        ]
        data = []

        for assessment in queryset:

            # TAB COMMENTS
            all_tabs_comments = []

            all_tabs_comments.extend(assessment.assessmenttabcomments.all())
            all_tabs_comments.extend(assessment.organisationaltabcomments.all())
            all_tabs_comments.extend(assessment.valuechaintabcomments.all())
            all_tabs_comments.extend(assessment.financetabcomments.all())
            all_tabs_comments.extend(assessment.financialhistorytabcomments.all())
            all_tabs_comments.extend(assessment.financialproductiontabcomments.all())
            all_tabs_comments.extend(assessment.financialoverviewtabcomments.all())
            all_tabs_comments.extend(assessment.financialperformancetabcomments.all())
            all_tabs_comments.extend(assessment.profitlosstabcomments.all())
            all_tabs_comments.extend(assessment.balancesheettabcomments.all())
            all_tabs_comments.extend(assessment.cashflowtabcomments.all())
            all_tabs_comments.extend(assessment.productiontabcomments.all())
            all_tabs_comments.extend(assessment.monthlyproductiontabcomments.all())
            all_tabs_comments.extend(assessment.governancetabcomments.all())
            all_tabs_comments.extend(assessment.observationstabcomments.all())
            all_tabs_comments.extend(assessment.documentationtabcomments.all())
            all_tabs_comments.extend(assessment.termsandconditionstabcomments.all())

            response_comments = ResponseComment.objects.filter(
                response__in=assessment.responses.all()
            )

            for comment in all_tabs_comments:
                data.append(
                    [
                        comment.assessment.project.name
                        if comment.assessment.project
                        else "-",  # 'Project',
                        comment.assessment.producing_organization.customer.name,  # 'Assessee',
                        ",".join(
                            [
                                assignment.assessor.user.get_full_name()
                                for assignment in comment.assessment.assessmentassignments.all()
                            ]
                        ),  # 'Assessor(s)',
                        comment.assessment.display_quality_reviewer.get_full_name()
                        if comment.assessment.display_quality_reviewer
                        else "-",  # 'Quality Reviewer',
                        comment.contents,  # 'Comment',
                        comment.user.get_full_name(),  # 'Comment by',
                        comment.get_location(),  # 'Comment location',
                    ]
                )
            for comment in response_comments:
                data.append(
                    [
                        comment._assessment.project.name
                        if comment._assessment.project
                        else "-",  # 'Project',
                        comment._assessment.producing_organization.customer.name,  # 'Assessee',
                        ",".join(
                            [
                                assignment.assessor.user.get_full_name()
                                for assignment in comment._assessment.assessmentassignments.all()
                            ]
                        ),  # 'Assessor(s)',
                        comment._assessment.display_quality_reviewer.get_full_name()
                        if comment._assessment.display_quality_reviewer
                        else "-",  # 'Quality Reviewer',
                        comment.contents,  # 'Comment',
                        comment.user.get_full_name(),  # 'Comment by',
                        comment.response.question_display_position,  # 'Comment location',
                    ]
                )

        with open(filename, "wb") as f:
            f.write(tablib.Dataset(*data, headers=headers).xlsx)
