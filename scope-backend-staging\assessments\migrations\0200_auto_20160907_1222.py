from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0199_auto_20160906_1433")]

    operations = [
        migrations.AlterField(
            model_name="executive",
            name="years_in_function",
            field=models.PositiveSmallIntegerField(null=True, blank=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="executive",
            name="years_in_organization",
            field=models.PositiveSmallIntegerField(null=True, blank=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="executive",
            name="years_in_sector",
            field=models.PositiveSmallIntegerField(null=True, blank=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="nonexecutive",
            name="years_in_function",
            field=models.PositiveSmallIntegerField(null=True, blank=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="nonexecutive",
            name="years_in_organization",
            field=models.PositiveSmallIntegerField(null=True, blank=True),
            preserve_default=True,
        ),
        migrations.Alter<PERSON>ield(
            model_name="nonexecutive",
            name="years_in_sector",
            field=models.PositiveSmallIntegerField(null=True, blank=True),
            preserve_default=True,
        ),
    ]
