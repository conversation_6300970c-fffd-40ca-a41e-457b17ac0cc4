from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0100_auto_20150217_1606")]

    operations = [
        migrations.AddField(
            model_name="financialratio",
            name="return_on_capital",
            field=models.DecimalField(
                null=True, editable=False, max_digits=6, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="financialscores",
            name="return_on_capital",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="financialscores17",
                null=True,
                blank=True,
                to="assessments.FinancialScore",
            ),
            preserve_default=True,
        ),
    ]
