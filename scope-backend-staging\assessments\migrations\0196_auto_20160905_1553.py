from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0195_auto_20160905_1510")]

    operations = [
        migrations.AlterField(
            model_name="assessmentassignment",
            name="invitation",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                to="assessments.AssessmentInvitation",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterUniqueTogether(
            name="assessmentassignment",
            unique_together=set([("assessment", "assessor")]),
        ),
        migrations.AlterUniqueTogether(
            name="assessmentinvitation",
            unique_together=set([("assessment", "assessor")]),
        ),
    ]
