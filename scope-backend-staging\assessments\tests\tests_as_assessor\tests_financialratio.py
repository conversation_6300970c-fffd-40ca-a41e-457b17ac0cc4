import json

from rest_framework.reverse import reverse

from assessments.factories import FinancialRatioFactory
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class FinancialRatioTestCase(DenormMixin, AssessorJWTTestCase):
    def test_can_list(self):
        """
        Assessor should be able to list financial ratio objects
        """
        FinancialRatioFactory.create_batch(
            5, assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:financialratio-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertEqual(5, response_dict["count"])

    def test_can_filter_by_assessment_id(self):
        """
        Assessor should be able to filter financial ratio objects by assessment
        id
        """
        financial_ratios = FinancialRatioFactory.create_batch(
            5, assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:financialratio-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertEqual(5, response_dict["count"])
        assessment_id = financial_ratios[0].assessment_id
        data = {"assessment_id": assessment_id}
        response = self.client.get(url, data, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertEqual(1, response_dict["count"])
