from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0145_auto_20160316_1459")]

    operations = [
        migrations.AddField(
            model_name="ratioscores",
            name="asset_turnover",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="current_ratio",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="days_inventory_outstanding",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="days_sales_outstanding",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="debt_coverage_ratio",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="debt_servicing",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="debt_to_assets_ratio",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="debt_to_equity_ratio",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="financial_scores",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.FinancialScores",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="gross_margin",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="net_profit_growth",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="operating_profit_margin",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="quick_ratio",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="return_on_assets",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="return_on_equity",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="revenue_growth",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="working_capital_turnover",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.FinancialScore",
                null=True,
            ),
            preserve_default=True,
        ),
    ]
