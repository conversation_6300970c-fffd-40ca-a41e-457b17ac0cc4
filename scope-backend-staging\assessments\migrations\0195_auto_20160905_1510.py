from django.db import migrations, models


def make_assessment_assessor_unique(apps, schema_editor):
    AssessmentAssignment = apps.get_model("assessments", "AssessmentAssignment")
    AssessmentInvitation = apps.get_model("assessments", "AssessmentInvitation")
    double_assignments = (
        AssessmentAssignment.objects.values("assessment", "assessor")
        .annotate(c=models.Count("id"))
        .filter(c__gt=1)
        .values_list("assessment_id", "assessor_id", "c")
    )
    for assessment_id, assessor_id, c in double_assignments:
        assignments = AssessmentAssignment.objects.filter(
            assessment_id=assessment_id, assessor_id=assessor_id
        )[: c - 1]
        for assignment in assignments:
            assignment.delete()
    double_invitations = (
        AssessmentInvitation.objects.values("assessment", "assessor")
        .annotate(c=models.Count("id"))
        .filter(c__gt=1)
        .values_list("assessment_id", "assessor_id", "c")
    )
    for assessment_id, assessor_id, c in double_invitations:
        invitations = (
            AssessmentInvitation.objects.filter(
                assessment_id=assessment_id, assessor_id=assessor_id
            )
            .annotate(invite_count=models.Count("assessmentassignment"))
            .order_by("invite_count")[: c - 1]
        )
        for invitation in invitations:
            invitation.delete()


class Migration(migrations.Migration):

    dependencies = [("assessments", "0194_auto_20160905_1508")]

    operations = [migrations.RunPython(make_assessment_assessor_unique)]
