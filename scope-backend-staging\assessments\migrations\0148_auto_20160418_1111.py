import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0147_ratioscores_net_profit_margin")]

    operations = [
        migrations.AlterModelOptions(
            name="unscoredsectionresponse", options={"ordering": ("question__title",)}
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="_previous",
            field=models.ForeignKey(
                related_name="+",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.BalanceSheet",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="_balancesheet",
            field=models.ForeignKey(
                related_name="_financial_ratios",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.BalanceSheet",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="_profitlossstatement",
            field=models.ForeignKey(
                related_name="_financial_ratios",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.ProfitLossStatement",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="_profitlossstatement_previous_year",
            field=models.ForeignKey(
                related_name="_previous_financial_ratios",
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                editable=False,
                to="assessments.ProfitLossStatement",
                null=True,
            ),
            preserve_default=True,
        ),
    ]
