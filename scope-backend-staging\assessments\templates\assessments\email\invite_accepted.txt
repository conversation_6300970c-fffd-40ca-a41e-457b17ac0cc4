{% load i18n %}
{% language language_code %}
{% blocktrans with fullname=assignment.assessor.user.get_full_name po_name=assignment.assessment.producing_organization.customer.name po_address=assignment.assessment.producing_organization.address tool=assignment.assessment.tool.type.name date=assignment.assessment.date %}
Dear SCOPEinsight,


{{ fullname }} has accepted an invitation to conduct an assessment.
The {{ tool }} Assessment of {{ po_name }} in {{ po_address }} is scheduled to be completed before {{ date }}.

Kind regards
SCOPEinsight{% endblocktrans %}
{% endlanguage %}
