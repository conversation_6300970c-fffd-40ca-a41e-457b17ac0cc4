from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("hrm", "0013_assessor_second_region"),
        ("assessments", "0148_auto_20160418_1111"),
    ]

    operations = [
        migrations.CreateModel(
            name="Certification",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("accepted", models.BooleanField(default=False)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("name", models.TextField()),
                ("status", models.TextField(blank=True)),
                ("start_year", models.PositiveSmallIntegerField(null=True, blank=True)),
                ("end_year", models.PositiveSmallIntegerField(null=True, blank=True)),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="certifications",
                        to="assessments.Product",
                    ),
                ),
                (
                    "quality_controller",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="certifications_quality_controlled",
                        blank=True,
                        to="hrm.Employee",
                        null=True,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        )
    ]
