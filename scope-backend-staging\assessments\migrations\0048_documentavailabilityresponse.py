from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("hrm", "0005_auto_20141228_2126"),
        ("products", "0019_documentavailabilityquestion"),
        ("assessments", "0047_auto_20150105_1006"),
    ]

    operations = [
        migrations.CreateModel(
            name="DocumentAvailabilityResponse",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("available", models.NullBooleanField()),
                ("comment", models.TextField(blank=True)),
                ("accepted", models.BooleanField(default=False)),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="document_availability_responses",
                        to="assessments.Assessment",
                    ),
                ),
                (
                    "assessor",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="document_availability_responses",
                        blank=True,
                        to="hrm.Assessor",
                        null=True,
                    ),
                ),
                (
                    "quality_controller",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="document_availability_responses_quality_controlled",
                        blank=True,
                        to="hrm.Employee",
                        null=True,
                    ),
                ),
                (
                    "question",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="responses",
                        to="products.DocumentAvailabilityQuestion",
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        )
    ]
