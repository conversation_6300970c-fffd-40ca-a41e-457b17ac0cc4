# Generated by Django 2.1.10 on 2019-10-23 13:30

from django.db import migrations
from django.db.models import Q


def set_display_in_list_to_false(apps, schema_editor):
    User = apps.get_model("accounts", "User")
    nobody_obj = Q(
        Q(is_employee=False)
        & Q(is_assessor=False)
        & Q(is_contact=False)
        & Q(is_customer_admin=False)
        & Q(is_customer_user=False)
        & Q(is_financial_specialist=False)
        & Q(is_quality_reviewer=False)
        & Q(is_superuser=False)
    )
    filter_object = Q(
        Q(email__icontains="@scopeimport.com")
        | Q(email__icontains="generated-email-")
        | Q(email__iregex=r"\S*\d{2,}@scopeinsight\.com")
    )
    empty_contact = Q(
        Q(is_contact=True)
        & Q(is_employee=False)
        & Q(is_assessor=False)
        & Q(is_customer_admin=False)
        & Q(is_customer_user=False)
        & Q(is_financial_specialist=False)
        & Q(is_quality_reviewer=False)
        & Q(is_superuser=False)
        & Q(customer_contact__access_to_dashboard="")
    )
    users = User.objects.filter(
        nobody_obj
        | filter_object
        | empty_contact
        | Q(allowed_frontends="")
        | Q(
            Q(allowed_frontends__icontains="website")
            & ~Q(allowed_frontends__icontains="dashboard")
            & ~Q(allowed_frontends__icontains="smartclient")
        )
    )
    for u in users:
        u.display_in_list = False
        u.save()


class Migration(migrations.Migration):

    dependencies = [("accounts", "0038_user_display_in_list")]
    operations = [migrations.RunPython(set_display_in_list_to_false)]
