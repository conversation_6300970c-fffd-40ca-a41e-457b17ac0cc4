from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0119_auto_20150529_1546")]

    operations = [
        migrations.CreateModel(
            name="AdditionalForestryInfo",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "total_area",
                    models.DecimalField(default=0, max_digits=10, decimal_places=2),
                ),
                (
                    "average_year_production_area",
                    models.DecimalField(default=0, max_digits=10, decimal_places=2),
                ),
                (
                    "rotation_cycle",
                    models.DecimalField(default=0, max_digits=10, decimal_places=2),
                ),
                (
                    "annual_productive_area",
                    models.DecimalField(default=0, max_digits=10, decimal_places=2),
                ),
                (
                    "distance_to_transformation",
                    models.DecimalField(default=0, max_digits=10, decimal_places=2),
                ),
                (
                    "cost_of_transport",
                    models.DecimalField(default=0, max_digits=10, decimal_places=2),
                ),
                (
                    "_assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        editable=False,
                        to="assessments.Assessment",
                        null=True,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="ForestryInventory",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "trees_per_ha",
                    models.DecimalField(default=0, max_digits=10, decimal_places=2),
                ),
                (
                    "basal_area",
                    models.DecimalField(default=0, max_digits=10, decimal_places=2),
                ),
                (
                    "volume",
                    models.DecimalField(default=0, max_digits=10, decimal_places=2),
                ),
                (
                    "carbon",
                    models.DecimalField(default=0, max_digits=10, decimal_places=2),
                ),
                (
                    "_assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        editable=False,
                        to="assessments.Assessment",
                        null=True,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name="additionalforestryinfo",
            name="commercial_census",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="afi_2",
                null=True,
                blank=True,
                to="assessments.ForestryInventory",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="additionalforestryinfo",
            name="forestry_inventory",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="afi_1",
                null=True,
                blank=True,
                to="assessments.ForestryInventory",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="assessment",
            name="additional_forestry_info",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                null=True,
                blank=True,
                to="assessments.AdditionalForestryInfo",
            ),
            preserve_default=True,
        ),
    ]
