# Generated by Django 1.10.5 on 2017-04-03 14:11


from django.db import migrations, models

import libs.models_helpers


class Migration(migrations.Migration):

    dependencies = [("assessments", "0267_auto_20170223_1229")]

    operations = [
        migrations.AlterField(
            model_name="assessmentdocument",
            name="file",
            field=models.FileField(
                blank=True,
                max_length=500,
                null=True,
                upload_to=libs.models_helpers.class_based_upload_to,
            ),
        ),
        migrations.AlterField(
            model_name="balancesheetdocument",
            name="file",
            field=models.FileField(
                blank=True,
                max_length=500,
                null=True,
                upload_to=libs.models_helpers.class_based_upload_to,
            ),
        ),
        migrations.AlterField(
            model_name="costofsaledocument",
            name="file",
            field=models.FileField(
                blank=True,
                max_length=500,
                null=True,
                upload_to=libs.models_helpers.class_based_upload_to,
            ),
        ),
        migrations.AlterField(
            model_name="documentavailabilityresponsedocument",
            name="file",
            field=models.FileField(
                blank=True,
                max_length=500,
                null=True,
                upload_to=libs.models_helpers.class_based_upload_to,
            ),
        ),
        migrations.AlterField(
            model_name="expensedocument",
            name="file",
            field=models.FileField(
                blank=True,
                max_length=500,
                null=True,
                upload_to=libs.models_helpers.class_based_upload_to,
            ),
        ),
        migrations.AlterField(
            model_name="profitlossstatementdocument",
            name="file",
            field=models.FileField(
                blank=True,
                max_length=500,
                null=True,
                upload_to=libs.models_helpers.class_based_upload_to,
            ),
        ),
        migrations.AlterField(
            model_name="responsedocument",
            name="file",
            field=models.FileField(
                blank=True,
                max_length=500,
                null=True,
                upload_to=libs.models_helpers.class_based_upload_to,
            ),
        ),
        migrations.AlterField(
            model_name="subresponsedocument",
            name="file",
            field=models.FileField(
                blank=True,
                max_length=500,
                null=True,
                upload_to=libs.models_helpers.class_based_upload_to,
            ),
        ),
    ]
