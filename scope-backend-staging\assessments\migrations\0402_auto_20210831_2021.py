# Generated by Django 2.2.20 on 2021-08-31 18:21

from django.db import migrations


def link_assessment_evaluation_to_global_quality_issues(apps, schema_editor):
    AssessmentEvaluation = apps.get_model("assessments", "AssessmentEvaluation")
    GlobalQualityIssuesOption = apps.get_model("products", "GlobalQualityIssuesOption")

    for assessmentevaluation in AssessmentEvaluation.objects.all():
        option = GlobalQualityIssuesOption.objects.filter(
            name=assessmentevaluation.global_quality_issues
        ).last()
        if option and assessmentevaluation.global_quality_issues != "Other":
            AssessmentEvaluation.objects.filter(id=assessmentevaluation.id).update(
                global_quality_issues=""
            )
        if not option and assessmentevaluation.global_quality_issues:
            option = GlobalQualityIssuesOption.objects.filter(name="Other").last()
        if option:
            assessmentevaluation.global_quality_issues_options.add(option)
        # if the field is empty, leave it empty and make no links
        # if the field has predefined value that differs from "Other", leave it empty and make a link
        # if the field has predefined value that is "Other", leave it like that and make a link
        # if the field has no predefined value, leave it like that and make a link to "Other" option


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0401_assessmentevaluation_global_quality_issues_options"),
    ]

    operations = [
        migrations.RunPython(link_assessment_evaluation_to_global_quality_issues)
    ]
