from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0227_auto_20161020_1307")]

    operations = [
        migrations.CreateModel(
            name="AgentBankAccountLoanHistory",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("has_bank_account", models.NullBooleanField()),
                ("has_mobile_account", models.NullBooleanField()),
                ("has_loan_history", models.NullBooleanField()),
                ("has_paid_back_loans", models.NullBooleanField()),
                (
                    "assessment",
                    models.OneToOneField(
                        on_delete=models.CASCADE,
                        related_name="agent_bank_account_loan_history",
                        to="assessments.Assessment",
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        )
    ]
