from django.conf.urls import url
from rest_framework import routers

from .views import DashboardViewSet, GroupViewSet, UserReceiveViewSet, UserViewSet, UserTasksViewSet

app_name = "accounts"
router = routers.DefaultRouter()
router.register(r"users", UserReceiveViewSet)
router.register(r"users", UserViewSet)
router.register(r"groups", GroupViewSet)
router.register(r"accounts_tasks", UserTasksViewSet, basename="accounts_tasks")
urlpatterns = router.urls + [
    url(r"dashboard", DashboardViewSet.as_view(), name="dashboard")
]
