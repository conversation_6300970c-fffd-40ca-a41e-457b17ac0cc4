# Generated by Django 1.11.12 on 2018-05-23 14:23


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0323_assessment_display_financial_quality_reviewer")
    ]

    operations = [
        migrations.<PERSON>er<PERSON>ield(
            model_name="assessment",
            name="balance_sheet_tab_accepted",
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name="assessment",
            name="cash_flow_tab_accepted",
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name="assessment",
            name="financial_performance_tab_accepted",
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name="assessment",
            name="monthly_production_tab_accepted",
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name="assessment",
            name="profit_loss_tab_accepted",
            field=models.BooleanField(default=True),
        ),
    ]
