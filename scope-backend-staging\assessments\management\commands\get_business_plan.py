import datetime

import pytz
import tablib
from django.core.management.base import BaseCommand

from assessments.models import Assessment, DocumentAvailabilityResponse


class Command(BaseCommand):
    help = "Get comments on all assessments made in 2017"

    def handle(self, *args, **options):
        """
        Assessment id
        Project name
        Project id
        Assessee
        """
        filename = "assessees_with_business_plan-mrt-2018.xlsx".format(
            datetime.datetime.now(pytz.utc)
        )
        queryset = Assessment.objects.all()

        headers = [
            "Assessment id",
            "Assessment tool",
            "Project name",
            "Project id",
            "Assessee",
            "Business plan",
            "Comment",
        ]
        data = []

        for assessment in queryset:
            if DocumentAvailabilityResponse.objects.filter(
                assessment=assessment, question_title="Business plan"
            ).exists():
                r = DocumentAvailabilityResponse.objects.get(
                    assessment=assessment, question_title="Business plan"
                )
                data.append(
                    [
                        assessment.pk,
                        assessment.tool.title,
                        assessment.project.name
                        if assessment.project
                        else "-",  # 'Project name',
                        assessment.project.id
                        if assessment.project
                        else "-",  # 'Project id',
                        assessment.producing_organization.customer.name,  # 'Assessee',
                        r.available,
                        r.comment,
                    ]
                )
        with open(filename, "wb") as f:
            f.write(tablib.Dataset(*data, headers=headers).xlsx)
