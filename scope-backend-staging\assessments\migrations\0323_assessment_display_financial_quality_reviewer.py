# Generated by Django 1.11.12 on 2018-05-14 14:05


import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("assessments", "0322_assessment_financial_quality_reviewer"),
    ]

    operations = [
        migrations.AddField(
            model_name="assessment",
            name="display_financial_quality_reviewer",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        )
    ]
