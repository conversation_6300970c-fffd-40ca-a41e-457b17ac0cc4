from django.core.management.base import BaseCommand

from assessments.models import Assessment, ProducingOrganizationDetails


class Command(BaseCommand):
    help = "Translate ProducingOrganizationDetails corrupted values"

    def handle(self, *args, **options):
        # Fix corrupt data
        shift_cells_left_assessments = [
            595,
            612,
            619,
            624,
            617,
            605,
            603,
            599,
            621,
            615,
            623,
            596,
            600,
            601,
            606,
            604,
            597,
            614,
            625,
            602,
            611,
            598,
            618,
            608,
            613,
            607,
            622,
            616,
            626,
            610,
            627,
            620,
            609,
            653,
            644,
            631,
            652,
            630,
            642,
            647,
            640,
            645,
            632,
            643,
            633,
            635,
            648,
            646,
            637,
            629,
            636,
            651,
            641,
            649,
            639,
            638,
            628,
            634,
            650,
        ]

        for ax_id in shift_cells_left_assessments:
            assessment = Assessment.objects.get(pk=ax_id)
            assessment.producing_organization_details.access_roads = (
                assessment.producing_organization_details.power_electricity
            )
            assessment.producing_organization_details.distance_to_hub = (
                assessment.producing_organization_details.internet_access
            )
            assessment.producing_organization_details.public_transportation = (
                assessment.producing_organization_details.mobile_network_coverage
            )
            assessment.producing_organization_details.power_electricity = (
                assessment.producing_organization_details.running_water
            )

            assessment.producing_organization_details.internet_access = ""
            assessment.producing_organization_details.mobile_network_coverage = ""
            assessment.producing_organization_details.running_water = ""
            assessment.producing_organization_details.save()

        shift_cells_right_assessments = [
            240,
            230,
            256,
            241,
            285,
            276,
            275,
            268,
            229,
            231,
            267,
            242,
            237,
            247,
            269,
            266,
            238,
            259,
            258,
            279,
            246,
            243,
            235,
            233,
            287,
            254,
            236,
            257,
            245,
            239,
            283,
            255,
            260,
            282,
            274,
            278,
            249,
            272,
            271,
            273,
            244,
            251,
            253,
            232,
            252,
            234,
            248,
            277,
            265,
            264,
            284,
            262,
            250,
            286,
            263,
            261,
            281,
            280,
            270,
            288,
            311,
            291,
            306,
            290,
            321,
            326,
            320,
            297,
            293,
            295,
            305,
            315,
            312,
            301,
            299,
            328,
            325,
            313,
            314,
            307,
            292,
            303,
            322,
            296,
            289,
            304,
            300,
            323,
            319,
            294,
            318,
            308,
            302,
            298,
            317,
            324,
            309,
            316,
            327,
            310,
        ]

        for ax_id in shift_cells_right_assessments:
            assessment = Assessment.objects.get(pk=ax_id)
            assessment.producing_organization_details.running_water = (
                assessment.producing_organization_details.mobile_network_coverage
            )
            assessment.producing_organization_details.mobile_network_coverage = (
                assessment.producing_organization_details.internet_access
            )
            assessment.producing_organization_details.internet_access = (
                assessment.producing_organization_details.power_electricity
            )
            assessment.producing_organization_details.power_electricity = (
                assessment.producing_organization_details.public_transportation
            )
            assessment.producing_organization_details.public_transportation = (
                assessment.producing_organization_details.distance_to_hub
            )
            assessment.producing_organization_details.distance_to_hub = (
                assessment.producing_organization_details.access_roads
            )

            assessment.producing_organization_details.access_roads = ""
            assessment.producing_organization_details.save()

        shift_cells_more_to_the_right_assessments = [526, 525, 527, 524, 528, 530, 529]

        for ax_id in shift_cells_more_to_the_right_assessments:
            assessment = Assessment.objects.get(pk=ax_id)
            assessment.producing_organization_details.running_water = (
                assessment.producing_organization_details.power_electricity
            )
            assessment.producing_organization_details.mobile_network_coverage = (
                assessment.producing_organization_details.public_transportation
            )
            assessment.producing_organization_details.internet_access = (
                assessment.producing_organization_details.distance_to_hub
            )
            assessment.producing_organization_details.power_electricity = (
                assessment.producing_organization_details.access_roads
            )

            assessment.producing_organization_details.access_roads = ""
            assessment.producing_organization_details.distance_to_hub = ""
            assessment.producing_organization_details.public_transportation = ""
            assessment.producing_organization_details.save()

        # Translate access road
        for x in ProducingOrganizationDetails.objects.filter(
            access_roads__icontains="Terracer"
        ):
            x.access_roads = "Dirt road"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            access_roads="Chemin de terre"
        ):
            x.access_roads = "Dirt road"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            access_roads="Asfaltada (calidad media)"
        ):
            x.access_roads = "Tarmac (average quality)"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            access_roads="Camino transitable todo el tiempo"
        ):
            x.access_roads = "All-weather road"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            access_roads__icontains="Goudron (mauvaise qualit"
        ):
            x.access_roads = "Tarmac (bad quality)"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            access_roads__icontains="moyenne"
        ):
            x.access_roads = "Tarmac (average quality)"
            x.save()

        # Translate public transportation
        for x in ProducingOrganizationDetails.objects.filter(
            public_transportation="Dans le voisinage"
        ):
            x.public_transportation = "Available in vicinity"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            public_transportation="Aucun dans le voisinage"
        ):
            x.public_transportation = "Available in vicinity"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            public_transportation="Disponible en vecindad"
        ):
            x.public_transportation = "Available in vicinity"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            public_transportation="Available in vecinity"
        ):
            x.public_transportation = "Available in vicinity"
            x.save()

        # Translate power electricity
        for x in ProducingOrganizationDetails.objects.filter(power_electricity="Autre"):
            x.power_electricity = "Other"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            power_electricity="Estable con generador de reserva"
        ):
            x.power_electricity = "Stable with generator backup"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            power_electricity__icontains="Estable s"
        ):
            x.power_electricity = "Stable without generator backup"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            power_electricity__icontains="ratrice uniquement"
        ):
            x.power_electricity = "Only generator"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            power_electricity="Inestable sin generador de soporte"
        ):
            x.power_electricity = "Unstable without generator backup"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            power_electricity__icontains="Instable sans g"
        ):
            x.power_electricity = "Unstable without generator backup"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            power_electricity__icontains="Pas d"
        ):
            x.power_electricity = "No electricity"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            power_electricity__icontains="Stable avec g"
        ):
            x.power_electricity = "Stable with generator backup"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            power_electricity__icontains="Stable sans"
        ):
            x.power_electricity = "Stable without generator backup"
            x.save()

        # Translate internet access
        for x in ProducingOrganizationDetails.objects.filter(
            internet_access__icontains="nea fija estable"
        ):
            x.internet_access = "Stable fixed line"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            internet_access__icontains="nea fija inestable"
        ):
            x.internet_access = "Unstable fixed line"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            internet_access="No hay internet"
        ):
            x.internet_access = "No internet"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            internet_access__icontains="vil"
        ):
            x.internet_access = "Mobile only"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            internet_access="Home, stable fixed line"
        ):
            x.internet_access = "Stable fixed line"
            x.save()

        # Translate mobile network coverage
        for x in ProducingOrganizationDetails.objects.filter(
            mobile_network_coverage="Cobertura ancha"
        ):
            x.mobile_network_coverage = "Broad coverage"
            x.save()

        for x in ProducingOrganizationDetails.objects.filter(
            mobile_network_coverage="Cobertura limitada"
        ):
            x.mobile_network_coverage = "Limited coverage"
            x.save()

        # Translate running water
        for x in ProducingOrganizationDetails.objects.filter(
            running_water="Agua potable"
        ):
            x.running_water = "Running water"
            x.save()
