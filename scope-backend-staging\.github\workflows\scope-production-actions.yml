name: scopeinsight production actions run
run-name: ${{ github.actor }} is doing scopeinsight production actions run
on: 
  push:
    branches:
       - main
jobs:
  build_containers:
    runs-on: self-hosted
    steps:
      - run: echo "The job was automatically triggered by a ${{ github.event_name }} event."
      - run: echo "This job is now running on a ${{ runner.os }} server hosted by scopeinsight!"
      - run: echo "The name of your branch is ${{ github.ref }} and your repository is ${{ github.repository }}."
      - name: Check out repository code
        uses: actions/checkout@v3
      - run: echo "💡 The ${{ github.repository }} repository has been cloned to the runner."
      - run: sudo docker compose build 
      - run: sudo docker compose push 


  deploy_containers:
    runs-on: self-hosted
    needs: build_containers
    steps:
      - run: echo "The job was automatically triggered by a ${{ github.event_name }} event."
      - run: echo "This job is now running on a ${{ runner.os }} server hosted by scopeinsight!"
      - run: echo "The name of your branch is ${{ github.ref }} and your repository is ${{ github.repository }}."
      - run: ssh api.scopeinsight.com 'sudo docker compose -f ~/dcfiles/production/backend/docker-compose.yml pull && sudo docker compose -f ~/dcfiles/production/backend/docker-compose.yml up -d && uptime && date'
      - run: ssh api.scopeinsight.com 'sudo docker exec backend-web-1 python manage.py denorm_drop --settings=settings.docker_production'
      - run: ssh api.scopeinsight.com 'sudo docker exec backend-web-1 python manage.py migrate --settings=settings.docker_production'
      - run: ssh api.scopeinsight.com 'sudo docker exec backend-web-1 python manage.py denorm_init --settings=settings.docker_production'
      - run: ssh api.scopeinsight.com 'sudo docker exec backend-trainingweb-1 python manage.py denorm_drop --settings=settings.docker_production_training'
      - run: ssh api.scopeinsight.com 'sudo docker exec backend-trainingweb-1 python manage.py migrate --settings=settings.docker_production_training'
      - run: ssh api.scopeinsight.com 'sudo docker exec backend-trainingweb-1 python manage.py denorm_init --settings=settings.docker_production_training'
