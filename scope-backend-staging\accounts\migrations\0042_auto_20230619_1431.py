# Generated by Django 3.1.14 on 2023-06-19 12:31

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0041_user_is_data_collector"),
    ]

    operations = [
        migrations.AlterField(
            model_name="user",
            name="language",
            field=models.CharField(
                choices=[
                    ("en", "English"),
                    ("fr", "French"),
                    ("es", "Spanish"),
                    ("am", "Amharic"),
                ],
                default="en",
                max_length=2,
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="languages_spoken",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    choices=[
                        ("en", "English"),
                        ("fr", "French"),
                        ("es", "Spanish"),
                        ("am", "Amharic"),
                    ],
                    default="en",
                    max_length=2,
                ),
                blank=True,
                null=True,
                size=None,
            ),
        ),
    ]
