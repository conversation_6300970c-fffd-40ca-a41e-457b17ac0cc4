from django.core.management.base import BaseCommand
from django.db.models import ProtectedError
from django.db.models.deletion import Collector

import accounts
import customers
import hrm
import messaging
from accounts.models import User


class Command(BaseCommand):
    help = "Delete unused users"

    def handle(self, *args, **options):
        whitelist_instances = {
            accounts.models.User,
            accounts.models.User.groups.through,
            accounts.models.PasswordResetRequest,
            customers.models.Contact,
            customers.models.Contact.customers.through,
            hrm.models.Employee,
            hrm.models.Assessor,
            hrm.models.Assessor.tools.through,
            hrm.models.Assessor.skills.through,
            messaging.models.Message,
        }
        users = User.objects.all()
        for user in users:
            c = Collector(using="default")
            try:
                c.collect([user])
            except ProtectedError:
                print("PROTECTED", user.pk)
                continue
            c.sort()
            has_instances = set(model for model, instance in c.instances_with_model())
            if not has_instances - whitelist_instances:
                user.delete()
            else:
                print("HAS OBJECTS", user.pk, has_instances - whitelist_instances)
