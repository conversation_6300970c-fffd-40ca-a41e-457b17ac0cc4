from django.db import migrations, models


def create_missing_subobjects(self, FinancialScores, FinancialScore):
    changed = False
    for field_name in [field.name for field in FinancialScores._meta.get_fields()]:
        if field_name.endswith("_id"):
            continue
        field = FinancialScores._meta.get_field(field_name)
        if (
            isinstance(field, models.OneToOneField)
            and field.related_model == FinancialScore
        ):
            if getattr(self, field_name) is None:
                setattr(self, field_name, FinancialScore.objects.create())
                changed = True
    if changed:
        self.save()


def create_missing_financial_score_subobjects(apps, schema_editor):
    FinancialScores = apps.get_model("assessments", "FinancialScores")
    FinancialScore = apps.get_model("assessments", "FinancialScore")
    for item in FinancialScores.objects.all():
        create_missing_subobjects(item, FinancialScores, FinancialScore)


class Migration(migrations.Migration):

    dependencies = [("assessments", "0143_auto_20160311_1933")]

    operations = [migrations.RunPython(create_missing_financial_score_subobjects)]
