# Generated by Django 1.11.12 on 2018-05-25 09:59


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0324_auto_20180523_1423")]

    operations = [
        migrations.AddField(
            model_name="assessment",
            name="assessor_comments_agent",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="assessor_comments_assessment",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="assessor_comments_documents",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="assessor_comments_finance",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="assessor_comments_finance_product",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="assessor_comments_governance",
            field=models.TextField(blank=True),
        ),
        migrations.Add<PERSON>ield(
            model_name="assessment",
            name="assessor_comments_monthly_production",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="assessor_comments_observations",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="assessor_comments_organizational",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="assessor_comments_production",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="assessor_comments_terms_and_conditions",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="assessor_comments_value_chain",
            field=models.TextField(blank=True),
        ),
    ]
