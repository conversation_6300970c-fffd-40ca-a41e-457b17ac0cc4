# Generated by Django 1.10.3 on 2016-11-21 09:43


import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0238_auto_20161116_1422")]

    operations = [
        migrations.AddField(
            model_name="assessment",
            name="_netmonthlyincomeprojection",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="+",
                to="assessments.NetMonthlyIncomeProjection",
            ),
        ),
        migrations.AddField(
            model_name="assessment",
            name="_totalmonthlyexpensesprojection",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="+",
                to="assessments.TotalMonthlyExpensesProjection",
            ),
        ),
        migrations.AddField(
            model_name="assessment",
            name="_totalmonthlyincomeprojection",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="+",
                to="assessments.TotalMonthlyIncomeProjection",
            ),
        ),
    ]
