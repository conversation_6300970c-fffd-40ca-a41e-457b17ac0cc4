{% load i18n %}
{% language language_code %}
{% if trainee %}
{% blocktrans with fullname=toUser.get_full_name po_name=invitation.assessment.producing_organization.customer.name po_address=invitation.assessment.producing_organization.address tool_type=invitation.assessment.tool.type.name %}
Dear {{ fullname }},

You have been invited to conduct a {{ tool_type }} {{ ax_type }} of the {{ po_name }} in {{ po_address }}.

Follow this link <a href="{{ frontend }}/#/home">{{ frontend }}/#/home</a> to accept the invitation and start your assessment.

Kind regards,

SCOPEinsight{% endblocktrans %}
{% else %}
{% blocktrans with fullname=toUser.get_full_name po_name=invitation.assessment.producing_organization.customer.name po_address=invitation.assessment.producing_organization.address tool_type=invitation.assessment.tool.type.name %}
Dear {{ fullname }},

You have been invited to conduct a {{ tool_type }} {{ ax_type }} of the {{ po_name }} in {{ po_address }}.

Follow this link <a href="{{ frontend }}/#/home">{{ frontend }}/#/home</a> to accept the invitation and start your assessment.

Kind regards,

SCOPEinsight{% endblocktrans %}
{% endif %}
{% endlanguage %}