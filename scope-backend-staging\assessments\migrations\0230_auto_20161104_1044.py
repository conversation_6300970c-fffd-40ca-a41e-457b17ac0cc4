from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0229_agentincome")]

    operations = [
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="number_of_female_farmers_under_supervision",
            field=models.PositiveSmallIntegerField(default=0),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="number_of_male_farmers_under_supervision",
            field=models.PositiveSmallIntegerField(default=0),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="number_of_potential_female_farmers",
            field=models.PositiveSmallIntegerField(default=0),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="number_of_potential_male_farmers",
            field=models.PositiveSmallIntegerField(default=0),
            preserve_default=True,
        ),
    ]
