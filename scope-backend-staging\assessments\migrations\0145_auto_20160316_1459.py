from django.db import migrations, models


def create_missing_subobjects(self, General<PERSON>he<PERSON>, GeneralCheck):
    changed = False
    for field_name in [field.name for field in GeneralChecks._meta.get_fields()]:
        if field_name.endswith("_id"):
            continue
        field = GeneralChecks._meta.get_field(field_name)
        if (
            isinstance(field, models.OneToOneField)
            and field.related_model == GeneralCheck
        ):
            if getattr(self, field_name) is None:
                setattr(self, field_name, GeneralCheck.objects.create())
                changed = True
    if changed:
        self.save()


def fix_general_checks(apps, schema_editor):
    GeneralChecks = apps.get_model("assessments", "GeneralChecks")
    GeneralCheck = apps.get_model("assessments", "GeneralCheck")
    for item in GeneralChecks.objects.all():
        create_missing_subobjects(item, GeneralChecks, GeneralCheck)


class Migration(migrations.Migration):

    dependencies = [("assessments", "0144_auto_20160315_1443")]

    operations = [migrations.RunPython(fix_general_checks)]
