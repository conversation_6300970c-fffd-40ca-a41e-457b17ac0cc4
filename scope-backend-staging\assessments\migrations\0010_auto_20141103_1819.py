from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("hrm", "0001_initial"), ("assessments", "0009_auto_20141103_1740")]

    operations = [
        migrations.AddField(
            model_name="profitlossstatement",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="profitlossstatements",
                default=1,
                to="assessments.Assessment",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="assessor",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="profitlossstatements",
                default=1,
                to="hrm.Assessor",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="audited",
            field=models.NullBooleanField(),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="year",
            field=models.PositiveSmallIntegerField(default=1),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="balancesheets",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="assessor",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="balancesheets",
                to="hrm.Assessor",
            ),
            preserve_default=True,
        ),
    ]
