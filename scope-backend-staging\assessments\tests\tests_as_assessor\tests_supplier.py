import json

from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import AssessmentFactory, SupplierFactory
from customers.factories import CustomerFactory
from libs.test_helpers import AssessorJWTTestCase, DenormMixin
from products.factories import ProductTypeOptionFactory


class SupplierTestCase(DenormMixin, AssessorJWTTestCase):
    def test_can_list(self):
        """
        Supplier should have correct fields
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )

        SupplierFactory.create(assessment=assessment)

        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        value_chain_players = response_dict["suppliers"]["objects"]

        required_keys = set(
            [
                "relation_to_producing_organization",
                "contract_in_place",
                "number_of_years_in_relation",
                "id",
                "description_of_agreement",
                "assessment",
                "contract_start_year",
                "display_relation_to_producing_organization",
                "customer",
                "url",
                "modified_at",
                "contract_end_year",
                "customer_type",
                "contact",
                "display_contract_in_place",
                "display_customer_type",
                "product_types_purchased",
                "payment_method",
                "display_payment_method",
                "amount_purchased",
                "other",
            ]
        )

        actual_keys = set(value_chain_players[0].keys())

        self.assertEqual(1, len(value_chain_players))
        self.assertSetEqual(required_keys, actual_keys)

    def test_can_create(self):
        """
        Assessor can create Supplier
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )

        organization = CustomerFactory.create()
        product_type_options = ProductTypeOptionFactory.create_batch(
            3, tool=assessment.tool
        )
        url = reverse("assessments:supplier-list") + "?fields=product_types_purchased"
        data = {
            "assessment": reverse("assessments:assessment-detail", [assessment.pk]),
            "tool": reverse("products:tool-detail", [assessment.tool.pk]),
            "customer": reverse("customers:customer-detail", [organization.pk]),
            "product_types_purchased": [
                reverse(
                    "products:producttypeoption-detail", [product_type_options[0].pk]
                ),
                reverse(
                    "products:producttypeoption-detail", [product_type_options[2].pk]
                ),
            ],
        }
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_201_CREATED, response.status_code)
        response_dict = json.loads(response.content)
        expected_links = {
            "http://testserver/products/product_type_options/{}/".format(
                product_type_options[0].pk
            ),
            "http://testserver/products/product_type_options/{}/".format(
                product_type_options[2].pk
            ),
        }
        links = set()
        for obj in response_dict["product_types_purchased"]["objects"]:
            links.add(obj["url"])
        self.assertSetEqual(expected_links, links)
