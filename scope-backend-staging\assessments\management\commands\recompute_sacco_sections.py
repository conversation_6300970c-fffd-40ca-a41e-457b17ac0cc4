from itertools import zip_longest

import progressbar
from denorm import flush
from denorm.models import DirtyInstance
from django.apps import apps
from django.core.management.base import BaseCommand
from django.db import connection, transaction
from django.db.models import Q


def grouper(iterable, n, fillvalue=None):
    "Collect data into fixed-length chunks or blocks"
    # grouper('ABCDEFG', 3, 'x') --> ABC DEF Gxx
    args = [iter(iterable)] * n
    return zip_longest(fillvalue=fillvalue, *args)


class Command(BaseCommand):
    args = "<model name>"
    help = "Rebuild denorms for multiple models 100 at a time"

    def add_arguments(self, parser):
        parser.add_argument("model", nargs="+", type=str)

    def handle(self, *args, **options):
        batch_size = 100
        connection.use_debug_cursor = False
        model_names = options["model"]
        # models = []
        # for model_name in model_names:
        #     app_name, model_name = model_name.split(".")
        #     klass = apps.get_model(app_label=app_name, model_name=model_name)
        #     models.append(klass)
        # for model in models:
        SectionResponse = apps.get_model(app_label="assessments", model_name="SectionResponse")

        # Filter the queryset
        queryset = SectionResponse.objects.filter(
            Q(assessment__tool__title__in=["SCOPE Basic 20.21.0", "SCOPE Pro 20.21.0"])
        )

        # Count the results
        total = queryset.count()
        for objs in progressbar.progressbar(
            grouper(queryset.all().iterator(), batch_size),
            max_value=total / batch_size,
        ):
            with transaction.atomic():
                dirty_instances = []
                for obj in objs:
                    if obj is None:
                        break
                    dirty_instances.append(DirtyInstance(content_object=obj))
                DirtyInstance.objects.bulk_create(dirty_instances)
                flush()
