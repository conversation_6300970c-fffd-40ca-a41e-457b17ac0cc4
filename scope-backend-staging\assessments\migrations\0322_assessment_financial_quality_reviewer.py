# Generated by Django 1.11.12 on 2018-05-14 13:51


import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("assessments", "0321_auto_20180511_1406"),
    ]

    operations = [
        migrations.AddField(
            model_name="assessment",
            name="financial_quality_reviewer",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="financial_quality_reviewed_assessments",
                to=settings.AUTH_USER_MODEL,
            ),
        )
    ]
