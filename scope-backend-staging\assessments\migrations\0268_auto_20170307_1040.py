# Generated by Django 1.10.5 on 2017-03-07 10:40


from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [("assessments", "0267_auto_20170223_1229")]

    operations = [
        migrations.AlterModelOptions(
            name="assessmentcomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="assessmentdocumentcomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="balancesheetcomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="balancesheetdocumentcomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="bankaccountcomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="basicfinancialinfocomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="cashflowstatementcomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="collateralassetcomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="costofsalecomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="costofsaledocumentcomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="documentavailabilityresponsecomment",
            options={"ordering": ("created_at",)},
        ),
        migrations.AlterModelOptions(
            name="enablingplayercomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="expensecomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="expensedocumentcomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="financialratiocomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="financialscorescomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="generalcheckscomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="granthistorycomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="inputpurchasecomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="insurancecomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="loanapplicationcomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="loanhistorycomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="loanrequirementcomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="producingorganizationdetailscomment",
            options={"ordering": ("created_at",)},
        ),
        migrations.AlterModelOptions(
            name="productcomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="productionfigurecomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="productionpurchasecomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="productionsalecomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="profitlossstatementcomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="profitlossstatementdocumentcomment",
            options={"ordering": ("created_at",)},
        ),
        migrations.AlterModelOptions(
            name="ratioscorescomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="responsecomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="responsedocumentcomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="shareholdercomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="subresponsecomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="subresponsedocumentcomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="unscoredsectionresponsecomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="unscoredtoolresponsecomment", options={"ordering": ("created_at",)}
        ),
        migrations.AlterModelOptions(
            name="valuechainplayercomment", options={"ordering": ("created_at",)}
        ),
    ]
