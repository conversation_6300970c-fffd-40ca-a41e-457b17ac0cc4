# Generated by Django 1.10.4 on 2017-01-09 15:42


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0252_auto_20170103_1243")]

    operations = [
        migrations.AddField(
            model_name="assessment",
            name="agent_tab_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="assessment_tab_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="documents_tab_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="finance_product_tab_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="finance_tab_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="governance_tab_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="observations_tab_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="organizational_tab_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="production_tab_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="value_chain_tab_completed",
            field=models.BooleanField(default=False),
        ),
    ]
