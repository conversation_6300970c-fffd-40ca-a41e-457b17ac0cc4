# Generated by Django 1.10.5 on 2017-03-10 13:21


from django.db import migrations


def move_language_skills_to_languages_spoken_field(apps, schema_editor):
    Assessor = apps.get_model("hrm", "Assessor")

    for assessor in Assessor.objects.filter(skills__name="english"):
        user = assessor.user
        if user.languages_spoken is None:
            user.languages_spoken = []
        user.languages_spoken.append("en")
        user.save()
    for assessor in Assessor.objects.filter(skills__name="french"):
        user = assessor.user
        if user.languages_spoken is None:
            user.languages_spoken = []
        user.languages_spoken.append("fr")
        user.save()
    for assessor in Assessor.objects.filter(skills__name="spanish"):
        user = assessor.user
        if user.languages_spoken is None:
            user.languages_spoken = []
        user.languages_spoken.append("es")
        user.save()


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0025_auto_20170310_1318"),
        ("hrm", "0026_auto_20170310_1220"),
    ]

    operations = [migrations.RunPython(move_language_skills_to_languages_spoken_field)]
