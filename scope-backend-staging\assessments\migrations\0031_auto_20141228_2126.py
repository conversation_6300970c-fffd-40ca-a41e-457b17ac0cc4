import datetime

from django.db import migrations, models
from django.utils.timezone import utc


class Migration(migrations.Migration):

    dependencies = [("assessments", "0030_assessmentassignment_done")]

    operations = [
        migrations.AddField(
            model_name="assessment",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 9, 357920, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="assessmentassignment",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 13, 445795, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="assessmentcomment",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 15, 954004, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="assessmentdocument",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 17, 670642, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="assessmentdocumentcomment",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 19, 913215, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="assessmentevaluation",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 22, 794919, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="assessmentinvitation",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 25, 455418, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 27, 626673, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="balancesheetdocument",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 29, 287329, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="balancesheetdocumentcomment",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 30, 910757, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 32, 526350, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="costofsale",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 34, 376153, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="costofsalecomment",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 35, 914600, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="costofsaledocument",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 37, 498271, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="costofsaledocumentcomment",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 39, 4047, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="expense",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 40, 410850, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="expensecomment",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 41, 722883, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="expensedocument",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 43, 75632, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="expensedocumentcomment",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 44, 812744, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="financialratio",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 46, 128354, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="financialscore",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 47, 514402, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="financialscores",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 49, 418687, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="generalcheck",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 52, 442195, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="generalchecks",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 53, 715009, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 55, 6381, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 56, 551790, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="profitlossstatementdocument",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 57, 810900, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="profitlossstatementdocumentcomment",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 24, 59, 94460, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="ratioscores",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 25, 0, 443656, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="response",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 25, 1, 797618, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="responsecomment",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 25, 3, 196006, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="responsedocument",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 25, 4, 750845, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="responsedocumentcomment",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 25, 6, 182872, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="service",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 25, 7, 542677, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="timesheetentry",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 25, 8, 919861, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="unscoredsectionresponse",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 25, 10, 218233, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="unscoredsectionresponsecomment",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 25, 11, 516832, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="unscoredtoolresponse",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 25, 12, 759403, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="unscoredtoolresponsecomment",
            name="modified_at",
            field=models.DateTimeField(
                default=datetime.datetime(2014, 12, 28, 21, 25, 14, 150859, tzinfo=utc),
                auto_now=True,
            ),
            preserve_default=False,
        ),
    ]
