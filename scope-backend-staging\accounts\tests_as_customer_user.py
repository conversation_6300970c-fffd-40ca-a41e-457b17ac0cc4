import json

from denorm import flush
from rest_framework import status
from rest_framework.reverse import reverse

from accounts.factories import UserFactory
from assessments.factories import AssessmentFactory
from customers.factories import ContactFactory
from libs.test_helpers import CustomerUserJWTTestCase, DenormMixin


class ImpersonationUserTestCase(DenormMixin, CustomerUserJWTTestCase):
    def test_only_employee_can_impersonate(self):
        """
        Dashboard User cannot impersonate, because has no permission to do so
        """
        user = UserFactory.create(email="<EMAIL>")
        ContactFactory.create(user=user, access_to_dashboard="customer_admin")
        response = self.client.post(
            "/accounts/users/impersonate/",
            {"email": "<EMAIL>"},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual("Not allowed to impersonate.", response_dict["detail"])


class UserTestCase(DenormMixin, CustomerUserJWTTestCase):
    maxDiff = None

    def test_can_see_users_of_my_assessees(self):
        """
        Should be able to see users of organizations my organization is
        assessing
        """
        assessment = AssessmentFactory.create(
            project__customer=self.jwt_user.organization
        )
        user = UserFactory.create(
            organization=assessment.producing_organization.customer, is_superuser=True
        )
        url = reverse("accounts:user-detail", [user.id])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_not_see_users_of_othe_assessees(self):
        """
        Should be not able to see users of organizations my organization is
        not assessing
        """
        user = UserFactory.create()
        url = reverse("accounts:user-detail", [user.id])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_404_NOT_FOUND, response.status_code)

    def test_can_see_contacts_of_my_assessees(self):
        """
        Should be able to see contacts of organizations my organization is
        assessing
        """
        assessment = AssessmentFactory.create(
            project__customer=self.jwt_user.organization
        )
        user = UserFactory.create(
            organization=assessment.producing_organization.customer
        )
        contact = ContactFactory.create(
            user=user, is_active=True, access_to_dashboard="customer_user"
        )
        contact.customers.set([user.organization])
        flush()
        url = reverse("accounts:user-detail", [user.id])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
