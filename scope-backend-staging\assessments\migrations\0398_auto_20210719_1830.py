# Generated by Django 2.2.20 on 2021-07-19 16:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "assessments",
            "0397_financialhistorytabcomment_financialoverviewtabcomment_financialproductiontabcomment",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="percent_of_active_members_under_30",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="percent_of_active_outgrowers_under_30",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="percent_of_full_time_employees_under_30",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="percent_of_members_under_30",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="percent_of_outgrowers_under_30",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="percent_of_part_time_employees_under_30",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="producingorganizationdetails",
            name="percent_of_seasonal_employees_under_30",
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
    ]
