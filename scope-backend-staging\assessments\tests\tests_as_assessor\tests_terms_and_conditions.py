import json
import shutil
from tempfile import mkdtemp

from django.conf import settings
from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import AssessmentFactory, TermsAndConditionsFactory
from assessments.models import TermsAndConditions
from libs.test_helpers import AssessorJ<PERSON>TTestCase, DenormMixin


class TermsAndConditionsTestCase(DenormMixin, AssessorJWTTestCase):
    def test_assessor_can_upload_file(self):
        temp_media_dir = mkdtemp(dir=settings.BASE_DIR)
        with self.settings(MEDIA_ROOT=temp_media_dir):
            assessment = AssessmentFactory.create(
                assessmentassignments__assessor=self.assessor
            )
            url = reverse("assessments:termsandconditions-list")
            data = {
                "assessment": reverse("assessments:assessment-detail", [assessment.pk]),
                "file": "data:text/plain;base64,Ygo=",
                "file_name": "a.txt",
            }
            response = self.client.post(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(status.HTTP_201_CREATED, response.status_code)
            self.assertEqual(1, TermsAndConditions.objects.count())
            tac = TermsAndConditions.objects.get()
            self.assertEqual("assessments/termsandconditions/a.txt", tac.file.name)
            self.assertEqual(2, tac.file.size)
            self.assertFalse(tac.signed)
        shutil.rmtree(temp_media_dir)

    def test_assessor_can_set_signed(self):
        tac = TermsAndConditionsFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        self.assertFalse(tac.signed)
        url = reverse("assessments:termsandconditions-detail", [tac.pk])
        data = {"signed": True}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        tac.refresh_from_db()
        self.assertTrue(tac.signed)

    def test_assessor_can_delete(self):
        tac = TermsAndConditionsFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:termsandconditions-detail", [tac.pk])
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(status.HTTP_204_NO_CONTENT, response.status_code)
        self.assertEqual(0, TermsAndConditions.objects.count())

    def test_assessor_can_not_set_other_signed(self):
        tac = TermsAndConditionsFactory.create()
        self.assertFalse(tac.signed)
        url = reverse("assessments:termsandconditions-detail", [tac.pk])
        data = {"signed": True}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_404_NOT_FOUND, response.status_code)
        tac.refresh_from_db()
        self.assertFalse(tac.signed)

    def test_assessor_can_not_delete_other(self):
        tac = TermsAndConditionsFactory.create()
        url = reverse("assessments:termsandconditions-detail", [tac.pk])
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(status.HTTP_404_NOT_FOUND, response.status_code)
        self.assertEqual(1, TermsAndConditions.objects.count())
