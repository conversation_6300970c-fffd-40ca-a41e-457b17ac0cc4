# Generated by Django 1.11.7 on 2018-02-15 15:08


import django.db.models.deletion
from django.db import migrations, models

import libs.models_helpers


class Migration(migrations.Migration):

    dependencies = [("assessments", "0301_auto_20180102_1419")]

    operations = [
        migrations.CreateModel(
            name="TermsAndConditions",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "file",
                    models.FileField(
                        blank=True,
                        max_length=500,
                        null=True,
                        upload_to=libs.models_helpers.class_based_upload_to,
                    ),
                ),
                ("signed", models.BooleanField(default=False)),
            ],
            options={"ordering": ("pk",)},
        ),
        migrations.AddField(
            model_name="assessment",
            name="terms_and_conditions_tab_completed",
            field=models.Bo<PERSON>an<PERSON>ield(default=False),
        ),
        migrations.AddField(
            model_name="termsandconditions",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="terms_and_conditions",
                to="assessments.Assessment",
            ),
        ),
    ]
