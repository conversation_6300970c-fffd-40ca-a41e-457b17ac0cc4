# Generated by Django 1.10.3 on 2016-11-10 21:34


from django.db import migrations, models

import accounts.helpers


class Migration(migrations.Migration):

    dependencies = [("accounts", "0012_passwordresetrequest")]

    operations = [
        migrations.AlterModelManagers(
            name="user", managers=[("objects", accounts.helpers.UserManager())]
        ),
        migrations.AlterField(
            model_name="user",
            name="groups",
            field=models.ManyToManyField(
                blank=True,
                help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                related_name="user_set",
                related_query_name="user",
                to="auth.Group",
                verbose_name="groups",
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="last_login",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="last login"
            ),
        ),
    ]
