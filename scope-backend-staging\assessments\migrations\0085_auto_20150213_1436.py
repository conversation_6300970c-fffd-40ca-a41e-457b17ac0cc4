from django.db import migrations


def move_input_type_information(apps, schema_editor):
    InputPurchase = apps.get_model("assessments", "InputPurchase")
    for inputpurchase in InputPurchase.objects.all().select_related("type"):
        inputpurchase.name = inputpurchase.type.name
        inputpurchase.unit = inputpurchase.type.unit
        inputpurchase.save()


class Migration(migrations.Migration):

    dependencies = [("assessments", "0084_auto_20150213_1436")]

    operations = [migrations.RunPython(move_input_type_information)]
