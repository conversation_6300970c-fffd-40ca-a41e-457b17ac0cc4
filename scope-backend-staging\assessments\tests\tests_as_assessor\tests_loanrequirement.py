import json

from rest_framework.reverse import reverse

from assessments.factories import LoanRequirementFactory
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class LoanRequirementTestCase(DenormMixin, AssessorJWTTestCase):
    def test_patching_return_full_money_object(self):
        """
        When patching money object has to return currency and amount
        """
        # Build LoanRequirement object to patch (using factory)

        amount = 400000
        amount_currency = "EUR"
        loan_requirement = LoanRequirementFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            amount=amount,
            amount_currency=amount_currency,
        )

        # Patch object
        new_amount = {"amount": {"currency": "USD", "amount": "400000"}}

        url = reverse("assessments:loanrequirement-detail", [loan_requirement.pk])
        response = self.client.patch(
            url, json.dumps(new_amount), content_type="application/json"
        )

        response_dict = json.loads(response.content)
        # Assert response contains what you want
        self.assertEqual("USD", response_dict["amount"]["currency"])
        self.assertEqual("400000", response_dict["amount"]["amount"])
