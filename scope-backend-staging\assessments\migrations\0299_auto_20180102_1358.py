# Generated by Django 1.11.7 on 2018-01-02 13:58


import djmoney.models.fields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [("assessments", "0298_auto_20171107_1921")]

    operations = [
        migrations.AlterField(
            model_name="balancesheet",
            name="account_receivables",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Account receivables",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="accounts_payable",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Accounts payable",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="cash",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Cash and cash equivalents",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="deferred_tax",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Deferred tax",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="fixed_assets",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Fixed assets",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="goodwill",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Goodwill",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="grants",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Grants (seed capital)",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="income_tax_payable",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Income tax payable",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="intangible_assets",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Intangible assets",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="inventories",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Inventories",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="long_term_loans",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Long term loans",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Other",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other_current_assets",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Other current assets",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other_current_liabilities",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Other current liabilities",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other_non_current_assets",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Other non-current assets",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other_non_current_liabilities",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Other non current liabilities",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other_receivables",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Other receivables",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="other_reserves",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Other reserves",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="overdrafts",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Overdrafts",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="provisions",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Provisions",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="retained_earnings",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Retained earnings",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="share_capital",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Share capital",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="share_premium",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Share premium",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="short_term_loans",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Short term loans",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="short_term_provisions",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Short term provisions",
            ),
        ),
        migrations.AlterField(
            model_name="balancesheet",
            name="statutory_legal_reserves",
            field=djmoney.models.fields.MoneyField(
                blank=True,
                decimal_places=2,
                default=None,
                max_digits=20,
                null=True,
                verbose_name="Statutory/legal reserves",
            ),
        ),
    ]
