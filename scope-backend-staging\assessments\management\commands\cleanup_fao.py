import progressbar
from django.core.management.base import BaseCommand

from assessments.models import Product
from products.models import GlobalProductTypeOption, ProductPerTypeOption


class Command(BaseCommand):
    help = "cleanup fao product names"

    def handle(self, *args, **options):
        name_to_item_code = {
            "Abaca manila hemp": "0809",
            "Agave fibres": "0800",
            "Alfalfa Meal and Pellets": "0862",
            "Alfalfa for forage": "0641",
            "Almonds": "0221",
            "Anise, badian, fennel": "0711",
            "Apples": "0515",
            "Apricots": "0526",
            "Areca nuts": "0226",
            "Artichokes": "0366",
            "Asparagus": "0367",
            "Asses": "1107",
            "Avocados": "0572",
            "Bagasse": "0170",
            "Bambara beans": "0203",
            "Bananas": "0486",
            "Barley": "0044",
            "Beans": "0414",
            "Beehives": "1181",
            "Beeswax": "1183",
            "Beet Pulp": "0169",
            "Beet Tops": "0629",
            "Beets for fodder": "0647",
            "Blood Meal": "1175",
            "Blueberries": "0552",
            "Bran of Barley": "0047",
            "Bran of Buckwheat": "0091",
            "Bran of Cereals nes": "0112",
            "Bran of Fonio": "0096",
            "Bran of Maize": "0059",
            "Bran of Millet": "0081",
            "Bran of Mixed Grain": "0105",
            "Bran of Oats": "0077",
            "Bran of Pulses": "0213",
            "Bran of Rice": "0035",
            "Bran of Rye": "0073",
            "Bran of Sorghum": "0085",
            "Bran of Triticale": "0099",
            "Bran of Wheat": "0017",
            "Brazil nuts": "0216",
            "Broad Beans": "0420",
            "Buckwheat": "0089",
            "Buffalo hids": "0957",
            "Buffalo milk": "0951",
            "Buffaloes": "0946",
            "Bulgur": "0021",
            "Butter and Ghee of Sheep Milk": "0983",
            "Butter of Buffalo Milk": "0952",
            "Butter of Cow Milk": "0886",
            "Butter of Goat Milk": "1022",
            "Cabbage for fodder": "0644",
            "Cabbages": "0358",
            "Cake of Copra": "0253",
            "Cake of Cottonseed": "0332",
            "Cake of Groundnuts": "0245",
            "Cake of Hempseed": "0338",
            "Cake of Kapok": "0314",
            "Cake of Linseed": "0335",
            "Cake of Maize": "0061",
            "Cake of Mustard Seed": "0294",
            "Cake of Oilseeds nes": "0341",
            "Cake of Palm Kernel": "0259",
            "Cake of Poppy Seed": "0298",
            "Cake of Rapeseed": "0272",
            "Cake of Rice Bran": "0037",
            "Cake of Safflower Seed": "0282",
            "Cake of Sesame Seed": "0291",
            "Cake of Soybeans": "0238",
            "Cake of Sunflower Seed": "0269",
            "Camel hides": "1133",
            "Camel milk": "1130",
            "Camels": "1126",
            "Canary seed": "0101",
            "Cane Tops": "0630",
            "Carobs": "0461",
            "Carrot": "0426",
            "Carrots for fodder": "0648",
            "Cashew nuts": "0217",
            "Cashewapple": "0591",
            "Cassava leaves": "0378",
            "Cassava": "0125",
            "Castor Beans": "0265",
            "Cattle hides": "0919",
            "Cattle": "0866",
            "Cauliflowers and broccoli": "0393",
            "Cheese from Cow Milk": "0901",
            "Cheese of Buffalo Milk": "0955",
            "Cheese of Goat Milk": "1021",
            "Cheese of Sheep Milk": "0984",
            "Cherries": "0531",
            "Chestnuts": "0220",
            "Chick-peas": "0191",
            "Chickens": "1057",
            "Chicory roots": "0459",
            "Chillies and peppers": "0401",
            "Cinnamon (canella)": "0693",
            "Clover for forage": "0640",
            "Cloves": "0698",
            "Cocoa Butter": "0664",
            "Cocoa Powder and Cake": "0665",
            "Cocoa": "0661",
            "Coconuts": "0249",
            "Cocoons": "1185",
            "Coffee Husks and Skins": "0660",
            "Coffee": "0656",
            "Coffee, Roasted": "0657",
            "Coir": "0813",
            "Compound Feed nes": "0845",
            "Compound Feed, Cattle": "0840",
            "Compound Feed, Pigs": "0842",
            "Compound Feed, Poultry": "0841",
            "Cotton": "0768",
            "Cottonseed": "0329",
            "Cow milk (fresh)": "0882",
            "Cow milk (pastorized)": "0883",
            "Cow peas": "0195",
            "Cranberries": "0554",
            "Cream, Fresh": "0885",
            "Cucumbers and gherkins": "0397",
            "Currants": "0550",
            "Dates": "0577",
            "Ducks": "1068",
            "Eggplants": "0399",
            "Eggs": "1062",
            "Feed Additives": "0854",
            "Feed Minerals": "0855",
            "Feed Supplements": "0850",
            "Feed Yeast": "0849",
            "Figs": "0569",
            "Fish Meal": "1174",
            "Flax fibre": "0773",
            "Flax": "0771",
            "Fonio": "0094",
            "Food Waste, Prep. for Feed": "1259",
            "Food Wastes": "0653",
            "Forage Products nes": "0651",
            "Fructose, Chemically Pure": "0154",
            "Fur skin": "1195",
            "Garlic": "0406",
            "Geese": "1072",
            "Ghee from Buffalo Milk": "0953",
            "Ghee from Cow Milk": "0887",
            "Ginger": "0720",
            "Gluten Feed and Meal": "0846",
            "Goat milk": "1020",
            "Goatkskins": "1025",
            "Goats": "1016",
            "Gooseberries": "0549",
            "Grain": "0103",
            "Grapefruit and pomelo": "0507",
            "Grapes": "0560",
            "Grasses nes for forage": "0639",
            "Green Corn (Maize)": "0446",
            "Green oilseeds for silage": "0642",
            "Groundnuts": "0242",
            "Gum": "0839",
            "Hair": "1008",
            "Hay (Clover, Lucerne, etc.)": "0858",
            "Hay nes": "0859",
            "Hay, non leguminous": "0857",
            "Hazelnuts": "0225",
            "Hemp fibre": "0777",
            "Hempseed": "0336",
            "Honey": "1182",
            "Hops": "0677",
            "Horse hides": "1102",
            "Horses": "1096",
            "Ice Cream and Edible Ice": "0910",
            "Jojoba Seeds": "0277",
            "Jute": "0780",
            "Kapok firbre": "0778",
            "Kapok fruit": "0310",
            "Karakul skins": "1002",
            "Karite Nuts (Sheanuts)": "0263",
            "Kiwi fruit": "0592",
            "Kolanuts": "0224",
            "Leaves, Tops and Vines nes": "0650",
            "Leeks and other alliaceous vegetables": "0407",
            "Legumes for silage": "0643",
            "Lemons and limes": "0497",
            "Lentils": "0201",
            "Lettuce and chicory": "0372",
            "Linseed": "0333",
            "Lupins": "0210",
            "Maize for forage": "0636",
            "Maize": "0056",
            "Mangoes": "0571",
            "Maple Sugar and Syrups": "0160",
            "Marc of Grape": "0566",
            "Mate": "0671",
            "Meat Meal": "1173",
            "Melons, Cantaloupes": "0568",
            "Melonseed": "0299",
            "Millet": "0079",
            "Mules": "1110",
            "Mushrooms": "0449",
            "Mustard seed": "0292",
            "Non-Protein Nitrogens": "0851",
            "Nutmeg, mace, cardamoms": "0702",
            "Oats": "0075",
            "Oilseeds": "0339",
            "Okra": "0430",
            "Olives": "0260",
            "Onions, shallots": "0402",
            "Oranges": "0490",
            "Other Concentrates nes": "0852",
            "Other Fructose and Syrup": "0166",
            "Other camelids": "1157",
            "Other cereal products": "0111",
            "Other chocolate products": "0666",
            "Other citrus fruits": "0512",
            "Other nuts": "0234",
            "Other pulses": "0211",
            "Other rodents": "1150",
            "Other spices": "0723",
            "Other sugar crops": "0161",
            "Other tropical fruits": "0603",
            "Other vegetables": "0463",
            "Palm kernels": "0256",
            "Papayas": "0600",
            "Peaches and nectarines": "0534",
            "Pears": "0521",
            "Peas": "0417",
            "Pepper": "0687",
            "Peppermint, Spearmint": "0748",
            "Persimmons": "0587",
            "Pigeon peas": "0197",
            "Pigeons and other birds": "1083",
            "Pigs": "1034",
            "Pigskins": "1044",
            "Pimento": "0689",
            "Pineapples": "0574",
            "Pistachios": "0223",
            "Plantains": "0489",
            "Plums": "0536",
            "Poppy seed": "0296",
            "Potato Offals": "0120",
            "Potatoes": "0116",
            "Pulp, Waste of Fruit for Feed": "0628",
            "Pumpkins for fodder": "0645",
            "Pumpkins, squash and gourds": "0394",
            "Pyrethrum": "0754",
            "Quinces": "0523",
            "Quinoa": "0092",
            "Rabbit skins": "1146",
            "Rabbits": "1140",
            "Ramie": "0788",
            "Rapeseed or colza seed": "0270",
            "Raspberries": "0547",
            "Rice, Broken": "0032",
            "Rice, Husked": "0028",
            "Rice, Milled (Husked)": "0029",
            "Rice, Milled": "0031",
            "Rice, paddy": "0027",
            "Roots and tubers": "0149",
            "Rubber": "0836",
            "Rye grass for forage": "0638",
            "Rye": "0071",
            "Safflower seed": "0280",
            "Sesame seed": "0289",
            "Sheep milk": "0982",
            "Sheep": "0976",
            "Sheepskins": "0995",
            "Sisal": "0789",
            "Skim Milk of Buffalo": "0954",
            "Skim Milk of Cows": "0888",
            "Skim Milk of Goat": "1023",
            "Skim Sheep Milk": "0985",
            "Sorghum for forage": "0637",
            "Sorghum": "0083",
            "Soybeans": "0236",
            "Spinach": "0373",
            "Straw and Husks": "0635",
            "Strawberries": "0544",
            "String Beans": "0423",
            "Sugar beet": "0157",
            "Sugar cane": "0156",
            "Sunflower seed": "0267",
            "Swedes for fodder": "0649",
            "Sweet potatoes": "0122",
            "Tallowtree Seeds": "0305",
            "Tangerines, mandarins, clementines, satsumas": "0495",
            "Taro (Cocoyam)": "0136",
            "Tea": "0667",
            "Tobacco leaves": "0826",
            "Tomatoes": "0388",
            "Triticale": "0097",
            "Tung Nuts": "0275",
            "Turkeys": "1079",
            "Turnips for fodder": "0646",
            "Vanilla": "0692",
            "Vegetable Products for Feed nes": "0652",
            "Vegetables, roots fodder nes": "0655",
            "Vetches": "0205",
            "Vitamins": "0853",
            "Walnuts": "0222",
            "Watermelons": "0567",
            "Wheat": "0015",
            "Whey, Condensed": "0890",
            "Whey, Fresh": "0903",
            "Whole Milk": "0889",
            "Wool": "0987",
            "Yams": "0137",
            "Yautia (Cocoyam)": "0135",
            "Yoghurt": "0891",
        }
        category_name_to_code = {
            "Cereals and cereal products": "1",
            "Roots and tubers": "2",
            "Sugar crops and sweeteners": "3",
            "Pulses": "4",
            "Nuts": "5",
            "Oil-bearing crops": "6",
            "Vegetables": "7",
            "Fruits": "8",
            "Fibres of vegetal and animal origin": "9",
            "Spices": "10",
            "Fodder crops": "11",
            "Coffee, cocoa, tea": "12",
            "Tobacco, rubber and other crops": "13",
            "Livestock": "16",
            "Animal products": "18",
            "Hides and skins": "19",
        }
        for product in progressbar.progressbar(Product.objects.all()):
            product.name = product.name.strip()
            for key in name_to_item_code.keys():
                if key.lower() == product.name.lower():
                    product.name = key
                    break
            product.global_product_type = product.global_product_type.strip()
            for key in category_name_to_code.keys():
                if key.lower() == product.global_product_type.lower():
                    product.global_product_type = key
                    break
            product.save()
        for productoption in progressbar.progressbar(
            ProductPerTypeOption.objects.all()
        ):
            productoption.name = productoption.name.strip()
            for key in name_to_item_code.keys():
                if key.lower() == productoption.name.lower():
                    productoption.name = key
                    break
            productoption.save()
        for producttypeoption in progressbar.progressbar(
            GlobalProductTypeOption.objects.all()
        ):
            producttypeoption.name = producttypeoption.name.strip()
            for key in category_name_to_code.keys():
                if key.lower() == producttypeoption.name.lower():
                    producttypeoption.name = key
                    break
            producttypeoption.save()
