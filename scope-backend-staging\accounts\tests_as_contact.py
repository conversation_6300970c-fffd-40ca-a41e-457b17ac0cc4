import json

from rest_framework import status
from rest_framework.reverse import reverse

from accounts.factories import UserFactory
from accounts.models import User
from customers.factories import ContactFactory
from libs.test_helpers import ContactJWTTestCase, DenormMixin


class UserTestCase(DenormMixin, ContactJWTTestCase):
    maxDiff = None

    def test_can_not_modify_other_user(self):
        """
        Contact can not modify other user object
        """
        other_user = UserFactory.create(email="<EMAIL>", language="en")
        url = reverse("accounts:user-detail", [other_user.pk])
        data = {"language": "es"}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_404_NOT_FOUND, response.status_code)
        db_user = User.objects.get(pk=other_user.pk)
        self.assertEqual("en", db_user.language)

    def test_role_booleans_work(self):
        """
        The role booleans should be set correctly
        """
        url = reverse("accounts:user-me")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset(
            {
                "is_assessor": False,
                "is_financial_specialist": False,
                "is_employee": False,
                "is_quality_reviewer": False,
                "is_contact": True,
            },
            response_dict,
        )


class LoginTestCase(DenormMixin, ContactJWTTestCase):
    def test_can_not_login_to_app(self):
        """
        It should not be possible to log in to app
        """
        response = self.client.post(
            "/api-token-auth/",
            {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
            HTTP_ORIGIN="http://app.testserver",
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)

    def test_can_not_login_to_dashboard(self):
        """
        It should not be possible to log in to dashboard
        """
        response = self.client.post(
            "/api-token-auth/",
            {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)

    def test_can_login_to_portal(self):
        """
        It should be possible to log in to portal
        """
        response = self.client.post(
            "/api-token-auth/",
            {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
            HTTP_ORIGIN="http://portal.testserver",
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)


class ImpersonationTestCase(DenormMixin, ContactJWTTestCase):
    def test_only_employee_can_impersonate(self):
        """
        Regular contact cannot impersonate, because has no access to dashboard
        """
        user = UserFactory.create(email="<EMAIL>")
        ContactFactory.create(user=user, access_to_dashboard="customer_admin")
        response = self.client.post(
            "/accounts/users/impersonate/",
            {"email": "<EMAIL>"},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_401_UNAUTHORIZED, response.status_code)
