# Generated by Django 3.1.14 on 2021-12-31 09:01

from django.db import migrations


def update_question_type(apps, schema_editor):
    SubResponse = apps.get_model("assessments", "SubResponse")
    SubQuestion = apps.get_model("products", "SubQuestion")

    for subquestion in SubQuestion.objects.filter(
        title="Does your organization have sufficient staff to achieve its annual plan and/or targets?"
    ):
        SubQuestion.objects.filter(id=subquestion.id).update(type="mpc")

    for subresponse in SubResponse.objects.filter(
        subquestion__title="Does your organization have sufficient staff to achieve its annual plan and/or targets?"
    ):
        SubResponse.objects.filter(id=subresponse.id).update(
            selected_option=subresponse.checked_options.first()
        )
        # subresponse.checked_options.clear()


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0410_auto_20211229_1217"),
    ]

    operations = [migrations.RunPython(update_question_type)]
