import json

from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import AssessmentFactory
from libs.test_helpers import AssessorJ<PERSON>TTestCase, DenormMixin


class BasicFinancialInfoTestCase(DenormMixin, AssessorJWTTestCase):
    def test_minus_currency(self):
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:basicfinancialinfo-list")
        data = {
            "assessment": reverse("assessments:assessment-detail", [assessment.pk]),
            "year": 2016,
            "turnover": {"currency": "-", "amount": 1234},
        }
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)

    def test_default_currency(self):
        """
        Default currency should come from assessment
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor,
            currency="RWF",
        )
        url = reverse("assessments:basicfinancialinfo-list")
        data = {
            "assessment": reverse("assessments:assessment-detail", [assessment.pk]),
            "year": 2016,
        }
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_201_CREATED, response.status_code)
        detail_url = json.loads(response.content)["url"]
        patch_data = {"turnover": {"amount": 1234}}
        response = self.client.patch(
            detail_url, json.dumps(patch_data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        instance = assessment.basic_financial_infos.get()
        assert instance.turnover.currency.code == "RWF"
        assert instance.turnover_currency == "RWF"
