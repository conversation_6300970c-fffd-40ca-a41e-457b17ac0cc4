# Generated by Django 1.10.5 on 2017-10-25 15:49


from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [("assessments", "0294_merge_20171020_1031")]

    operations = [
        migrations.RemoveField(model_name="assessmentdocument", name="_assessment"),
        migrations.RemoveField(model_name="assessmentdocument", name="assessment"),
        migrations.RemoveField(
            model_name="assessmentdocumentcomment", name="_assessment"
        ),
        migrations.RemoveField(
            model_name="assessmentdocumentcomment", name="assessmentdocument"
        ),
        migrations.RemoveField(model_name="assessmentdocumentcomment", name="user"),
        migrations.RemoveField(model_name="balancesheetdocument", name="_assessment"),
        migrations.RemoveField(model_name="balancesheetdocument", name="balancesheet"),
        migrations.RemoveField(
            model_name="balancesheetdocumentcomment", name="_assessment"
        ),
        migrations.RemoveField(
            model_name="balancesheetdocumentcomment", name="balancesheetdocument"
        ),
        migrations.RemoveField(model_name="balancesheetdocumentcomment", name="user"),
        migrations.RemoveField(model_name="costofsaledocument", name="_assessment"),
        migrations.RemoveField(model_name="costofsaledocument", name="costofsale"),
        migrations.RemoveField(
            model_name="costofsaledocumentcomment", name="_assessment"
        ),
        migrations.RemoveField(
            model_name="costofsaledocumentcomment", name="costofsaledocument"
        ),
        migrations.RemoveField(model_name="costofsaledocumentcomment", name="user"),
        migrations.RemoveField(model_name="expensedocument", name="_assessment"),
        migrations.RemoveField(model_name="expensedocument", name="expense"),
        migrations.RemoveField(model_name="expensedocumentcomment", name="_assessment"),
        migrations.RemoveField(
            model_name="expensedocumentcomment", name="expensedocument"
        ),
        migrations.RemoveField(model_name="expensedocumentcomment", name="user"),
        migrations.RemoveField(
            model_name="profitlossstatementdocument", name="_assessment"
        ),
        migrations.RemoveField(
            model_name="profitlossstatementdocument", name="profitlossstatement"
        ),
        migrations.RemoveField(
            model_name="profitlossstatementdocumentcomment", name="_assessment"
        ),
        migrations.RemoveField(
            model_name="profitlossstatementdocumentcomment",
            name="profitlossstatementdocument",
        ),
        migrations.RemoveField(
            model_name="profitlossstatementdocumentcomment", name="user"
        ),
        migrations.RemoveField(
            model_name="responsedocumentcomment", name="_assessment"
        ),
        migrations.RemoveField(
            model_name="responsedocumentcomment", name="responsedocument"
        ),
        migrations.RemoveField(model_name="responsedocumentcomment", name="user"),
        migrations.RemoveField(model_name="subresponsedocument", name="_assessment"),
        migrations.RemoveField(model_name="subresponsedocument", name="subresponse"),
        migrations.RemoveField(
            model_name="subresponsedocumentcomment", name="_assessment"
        ),
        migrations.RemoveField(
            model_name="subresponsedocumentcomment", name="subresponsedocument"
        ),
        migrations.RemoveField(model_name="subresponsedocumentcomment", name="user"),
        migrations.DeleteModel(name="AssessmentDocument"),
        migrations.DeleteModel(name="AssessmentDocumentComment"),
        migrations.DeleteModel(name="BalanceSheetDocument"),
        migrations.DeleteModel(name="BalanceSheetDocumentComment"),
        migrations.DeleteModel(name="CostOfSaleDocument"),
        migrations.DeleteModel(name="CostOfSaleDocumentComment"),
        migrations.DeleteModel(name="ExpenseDocument"),
        migrations.DeleteModel(name="ExpenseDocumentComment"),
        migrations.DeleteModel(name="ProfitLossStatementDocument"),
        migrations.DeleteModel(name="ProfitLossStatementDocumentComment"),
        migrations.DeleteModel(name="ResponseDocumentComment"),
        migrations.DeleteModel(name="SubResponseDocument"),
        migrations.DeleteModel(name="SubResponseDocumentComment"),
    ]
