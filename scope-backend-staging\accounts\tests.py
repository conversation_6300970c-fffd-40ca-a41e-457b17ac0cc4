import json
import os
from tempfile import mkdtemp
from unittest import skip

import httpretty
import polib
from denorm import flush
from django.core import mail
from django.db.models.signals import post_save
from django.test import TestCase
from factory.django import mute_signals
from freezegun import freeze_time
from rest_framework import status
from rest_framework.reverse import reverse

from accounts.factories import PasswordResetRequestFactory, UserFactory
from accounts.models import User
from customers.factories import ContactFactory, RepresentativeFactory
from hrm.factories import AssessorFactory, EmployeeFactory
from libs.test_helpers import DenormMixin, EmployeeJWTTestCase


class AuthorizationTestCase(DenormMixin, EmployeeJWTTestCase):
    def test_jwt_failure(self):
        """
        JWT failure should return 401 status code
        """
        with freeze_time("9999-01-01"):
            # jwt should be expired by now
            response = self.client.get("/")
            self.assertEqual(status.HTTP_401_UNAUTHORIZED, response.status_code)
            response_dict = json.loads(response.content)
            self.assertDictEqual(
                {"detail": "Signature has expired.", "status_code": 401}, response_dict
            )


class UserTestCase(DenormMixin, EmployeeJWTTestCase):
    def test_superuser_assessor_can_see_all_users(self):
        """
        Superuser who is also an assessor should be able to see all users
        """
        AssessorFactory.create(user=self.jwt_user)
        UserFactory.create_batch(9, is_superuser=True)
        flush()
        url = reverse("accounts:user-list")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("count", response_dict)
        self.assertEqual(10, response_dict["count"])


class PasswordResetTestCase(TestCase):
    fake_api_url = "https://faketraineeapi.scopeinsight.com/users/receive/"
    maxDiff = None

    @classmethod
    def setUpTestData(cls):
        httpretty.register_uri(httpretty.POST, cls.fake_api_url)
        httpretty.enable(allow_net_connect=False)
        super().setUpTestData()

    @classmethod
    def tearDownClass(cls):
        super().tearDownClass()
        httpretty.disable()
        httpretty.reset()

    def tearDown(self):
        httpretty.httpretty.latest_requests = []

    def test_can_request_password_reset(self):
        with mute_signals(post_save):
            user = UserFactory.create(language="en")
        url = reverse("accounts:user-request-password-reset")
        data = {"email": user.email}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        # check reset object has been made
        self.assertIsNotNone(user.password_reset_request)
        password_reset_request = user.password_reset_request
        # check email
        self.assertEqual(1, len(mail.outbox))
        message = mail.outbox[0]
        expected_link = "http://portal.testserver/#/set-password?token={}".format(
            password_reset_request.uuid
        )

        self.assertIn(expected_link, message.body)
        html = message.alternatives[0][0]
        self.assertIn(expected_link, html)

    def test_translatable_email(self):
        obj = PasswordResetRequestFactory.create(user__language="es")
        translations_dir = mkdtemp()
        po_dir = os.path.join(translations_dir, "es", "LC_MESSAGES")
        os.makedirs(po_dir)
        pofile = polib.POFile()
        pofile.append(
            polib.POEntry(
                msgid="Please proceed to %(frontend_host)s/#/set-password?token=%(token)s to set your password.",
                msgstr="1%(frontend_host)s%(token)s",
            )
        )
        pofile.append(polib.POEntry(msgid="Set SCOPEinsight password", msgstr="1"))
        pofile.metadata = {
            **pofile.metadata,
            "Content-Type": "text/plain; charset=utf-8",
            "Content-Transfer-Encoding": "8bit",
        }
        pofile.save(os.path.join(po_dir, "django.po"))
        pofile.save_as_mofile(os.path.join(po_dir, "django.mo"))
        with self.settings(LOCALE_PATHS=[translations_dir]):
            obj.send_email_notification()
        self.assertEqual(1, len(mail.outbox))
        message = mail.outbox[0]
        html = message.alternatives[0][0]
        txt = message.body
        subject = message.subject
        self.assertIn(f"1http://portal.testserver{obj.uuid}", html)
        self.assertEqual(f"\n1http://portal.testserver{obj.uuid}\n", txt)
        self.assertEqual("1", subject)

    @skip("for now...")
    def test_password_reset_for_unknown_email_returns_200(self):
        url = reverse("accounts:user-request-password-reset")
        data = {"email": "<EMAIL>"}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_apply_password_reset(self):
        password_reset_request = PasswordResetRequestFactory()
        url = reverse("accounts:user-apply-password-reset")
        data = {"token": password_reset_request.uuid, "password": "scopescopeA1"}
        self.client.post(url, json.dumps(data), content_type="application/json")
        user = User.objects.get(pk=password_reset_request.user_id)
        self.assertTrue(user.check_password("scopescopeA1"))

    def test_cannot_apply_password_reset_twice(self):
        password_reset_request = PasswordResetRequestFactory()
        url = reverse("accounts:user-apply-password-reset")
        data = {"token": password_reset_request.uuid, "password": "scopescopeA1"}
        self.client.post(url, json.dumps(data), content_type="application/json")
        user = User.objects.get(pk=password_reset_request.user_id)
        self.assertTrue(user.check_password("scopescopeA1"))
        url = reverse("accounts:user-apply-password-reset")
        data = {"token": password_reset_request.uuid, "password": "scopescopeA1"}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        response_dict = json.loads(response.content)
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        self.assertIn("token", response_dict)
        self.assertEqual(
            [
                "Invalid token. You have already used the link once to reset your password. Please request for another Reset Link."
            ],
            response_dict["token"],
        )

    def test_can_check_token_validity_success(self):
        password_reset_request = PasswordResetRequestFactory()
        url = reverse("accounts:user-check-reset-token")
        data = {"token": password_reset_request.uuid, "password": "scopescopeA1"}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("token_valid", response_dict)
        self.assertTrue(response_dict["token_valid"])

    def test_can_check_token_validity_fail(self):
        password_reset_request = PasswordResetRequestFactory()

        url = reverse("accounts:user-apply-password-reset")
        data = {"token": password_reset_request.uuid, "password": "scopescopeA1"}
        self.client.post(url, json.dumps(data), content_type="application/json")

        url = reverse("accounts:user-check-reset-token")
        data = {"token": password_reset_request.uuid, "password": "scopescopeA1"}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        response_dict = json.loads(response.content)
        self.assertIn("token_valid", response_dict)
        self.assertFalse(response_dict["token_valid"])

    def test_can_not_set_short_password(self):
        password_reset_request = PasswordResetRequestFactory()
        url = reverse("accounts:user-apply-password-reset")
        data = {"token": password_reset_request.uuid, "password": "scopeA1"}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("password", response_dict)
        self.assertEqual(["Password does not match rules"], response_dict["password"])

    def test_can_not_set_lowercase_password(self):
        password_reset_request = PasswordResetRequestFactory()
        url = reverse("accounts:user-apply-password-reset")
        data = {"token": password_reset_request.uuid, "password": "scopescope1"}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("password", response_dict)
        self.assertEqual(["Password does not match rules"], response_dict["password"])

    def test_can_not_set_uppercase_password(self):
        password_reset_request = PasswordResetRequestFactory()
        url = reverse("accounts:user-apply-password-reset")
        data = {"token": password_reset_request.uuid, "password": "SCOPESCOPE1"}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("password", response_dict)
        self.assertEqual(["Password does not match rules"], response_dict["password"])

    def test_can_not_set_password_without_numbers_or_specials(self):
        password_reset_request = PasswordResetRequestFactory()
        url = reverse("accounts:user-apply-password-reset")
        data = {"token": password_reset_request.uuid, "password": "SCOPEscope"}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("password", response_dict)
        self.assertEqual(["Password does not match rules"], response_dict["password"])

    def test_can_request_password_reset_again(self):
        """
        When requesting password reset again, just resend email
        """
        with mute_signals(post_save):
            user = UserFactory.create()
            obj = PasswordResetRequestFactory.create(user=user)
        url = reverse("accounts:user-request-password-reset")
        data = {"email": user.email}
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertIsNotNone(user.password_reset_request)
        self.assertEqual(1, len(mail.outbox))
        self.assertEqual(obj.pk, user.password_reset_request.pk)

    def test_apply_password_reset_sends_to_trainee(self):
        password_reset_request = PasswordResetRequestFactory.create(
            user__can_access_trainee=True, user__training_id=8888
        )
        url = reverse("accounts:user-apply-password-reset")
        data = {"token": password_reset_request.uuid, "password": "scopescopeA1"}
        with self.settings(
            SEND_TO_TRAINING_URL=self.fake_api_url, ALLOW_SEND_TO_TRAINING=True
        ):
            self.client.post(url, json.dumps(data), content_type="application/json")
        request = httpretty.httpretty.latest_requests[0]
        password_reset_request.user.refresh_from_db()
        expected_data = {
            "training_id": password_reset_request.user.training_id,
            "password": password_reset_request.user.password,
        }
        self.assertDictContainsSubset(expected_data, request.parsed_body)


class PermissionsDenormTestCase(DenormMixin, TestCase):
    def test_is_employee_updates(self):
        user = UserFactory.create()
        flush()
        self.assertFalse(user.is_employee)
        EmployeeFactory.create(user=user, qc_only=False)
        flush()
        user.refresh_from_db()
        self.assertTrue(user.is_employee)


class UserRolesTestCase(DenormMixin, TestCase):
    def test_customer_roles_booleans(self):
        """
        customer roles booleans should be set according to Contact object
        """
        user = UserFactory.create()
        contact = ContactFactory.create(user=user, access_to_dashboard="customer_user")
        flush()
        user.refresh_from_db()
        self.assertTrue(user.is_customer_user)
        self.assertFalse(user.is_customer_admin)
        self.assertFalse(user.can_create_projects)
        self.assertFalse(user.is_contact)
        contact.access_to_dashboard = "customer_admin"
        contact.save()
        flush()
        user.refresh_from_db()
        self.assertFalse(user.is_customer_user)
        self.assertTrue(user.is_customer_admin)
        self.assertTrue(user.can_create_projects)
        self.assertFalse(user.is_contact)
        contact.access_to_dashboard = ""
        contact.save()
        flush()
        user.refresh_from_db()
        self.assertFalse(user.is_customer_user)
        self.assertFalse(user.is_customer_admin)
        self.assertFalse(user.can_create_projects)
        self.assertTrue(user.is_contact)
        contact.is_active = False
        contact.save()
        flush()
        user.refresh_from_db()
        self.assertFalse(user.is_customer_user)
        self.assertFalse(user.is_customer_admin)
        self.assertFalse(user.can_create_projects)
        self.assertFalse(user.is_contact)

    def test_employee_roles_booleans(self):
        """
        employee roles booleans should be set according to Employee object
        """
        user = UserFactory.create()
        employee = EmployeeFactory.create(user=user, qc_only=False)
        flush()
        user.refresh_from_db()
        self.assertTrue(user.is_employee)
        self.assertTrue(user.can_create_projects)
        self.assertFalse(user.is_quality_reviewer)
        employee.qc_only = True
        employee.save()
        flush()
        user.refresh_from_db()
        self.assertFalse(user.is_employee)
        self.assertFalse(user.can_create_projects)
        self.assertTrue(user.is_quality_reviewer)
        employee.is_active = False
        employee.save()
        flush()
        user.refresh_from_db()
        self.assertFalse(user.is_employee)
        self.assertFalse(user.can_create_projects)
        self.assertFalse(user.is_quality_reviewer)

    def test_assessor_roles_booleans(self):
        """
        assessor roles booleans should be set according to Assessor object
        """
        user = UserFactory.create()
        assessor = AssessorFactory.create(
            user=user, is_assessor=True, financial_specialist=False
        )
        flush()
        user.refresh_from_db()
        self.assertTrue(user.is_assessor)
        self.assertFalse(user.is_financial_specialist)
        assessor.is_assessor = False
        assessor.financial_specialist = True
        assessor.save()
        flush()
        user.refresh_from_db()
        self.assertFalse(user.is_assessor)
        self.assertTrue(user.is_financial_specialist)
        assessor.is_active = False
        assessor.save()
        flush()
        user.refresh_from_db()
        self.assertFalse(user.is_assessor)
        self.assertFalse(user.is_financial_specialist)
