from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0045_auto_20150102_1526")]

    operations = [
        migrations.AddField(
            model_name="unscoredsectionresponse",
            name="_section_response",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="unscored_responses",
                blank=True,
                to="assessments.SectionResponse",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="unscoredsectionresponse",
            name="answer",
            field=models.TextField(blank=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="unscoredsectionresponse",
            name="assessor",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="unscored_section_responses",
                blank=True,
                to="hrm.Assessor",
                null=True,
            ),
            preserve_default=True,
        ),
    ]
