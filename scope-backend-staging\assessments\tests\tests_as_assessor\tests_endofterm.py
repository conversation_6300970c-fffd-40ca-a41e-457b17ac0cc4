import json

from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import AssessmentFactory, ExecutiveFactory
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class EndOfTermTestCase(DenormMixin, AssessorJWTTestCase):
    valid_cases = ["10-2010", ""]
    invalid_cases = ["10/2010", "a", "10 2010", "oct 2010", "2010-10"]

    def test_end_of_term_validates_on_patch(self):
        executive = ExecutiveFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:executive-detail", [executive.pk])
        for value in self.valid_cases:
            data = {"end_of_term": value}
            response = self.client.patch(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(
                status.HTTP_200_OK,
                response.status_code,
                "{} resulted in status code {}, expected {}".format(
                    repr(value), response.status_code, status.HTTP_200_OK
                ),
            )
        for value in self.invalid_cases:
            data = {"end_of_term": value}
            response = self.client.patch(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(
                status.HTTP_400_BAD_REQUEST,
                response.status_code,
                "{} resulted in status code {}, expected {}".format(
                    repr(value), response.status_code, status.HTTP_400_BAD_REQUEST
                ),
            )

    def test_end_of_term_validates_on_create(self):
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:executive-list")
        data = {
            "assessment": reverse("assessments:assessment-detail", [assessment.pk]),
            "name": "test",
            "end_of_term": None,
        }
        for value in self.valid_cases:
            data["end_of_term"] = value
            response = self.client.post(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(
                status.HTTP_201_CREATED,
                response.status_code,
                "{} resulted in status code {}, expected {}".format(
                    repr(value), response.status_code, status.HTTP_200_OK
                ),
            )
        for value in self.invalid_cases:
            data["end_of_term"] = value
            response = self.client.post(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(
                status.HTTP_400_BAD_REQUEST,
                response.status_code,
                "{} resulted in status code {}, expected {}".format(
                    repr(value), response.status_code, status.HTTP_400_BAD_REQUEST
                ),
            )
