from django.db import migrations


def shareholder_boolean_to_string(apps, schema_editor):
    Executive = apps.get_model("assessments", "Executive")
    NonExecutive = apps.get_model("assessments", "NonExecutive")
    for executive in Executive.objects.all():
        if executive.old_shareholder is None:
            executive.shareholder = ""
        elif executive.old_shareholder:
            executive.shareholder = "Yes"
        else:
            executive.shareholder = "No"
    for nonexecutive in NonExecutive.objects.all():
        if nonexecutive.old_shareholder is None:
            nonexecutive.shareholder = ""
        elif nonexecutive.old_shareholder:
            nonexecutive.shareholder = "Yes"
        else:
            nonexecutive.shareholder = "No"


class Migration(migrations.Migration):

    dependencies = [("assessments", "0195_auto_20160905_0929")]

    operations = [migrations.RunPython(shareholder_boolean_to_string)]
