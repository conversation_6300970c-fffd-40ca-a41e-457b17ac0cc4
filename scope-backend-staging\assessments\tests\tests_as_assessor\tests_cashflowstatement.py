from rest_framework.reverse import reverse

from assessments.factories import AssessmentAssignmentFactory, CashFlowStatementFactory
from assessments.models import AssessmentAssignment, CashFlowStatement
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class CashFlowStatementSheetTestCase(DenormMixin, AssessorJWTTestCase):
    def test_can_delete(self):
        """
        Fin Assessor should be able to delete the CF statement
        """
        cashflow_statement = CashFlowStatementFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            assessment__assessmentassignments__assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        url = reverse("assessments:cashflowstatement-detail", [cashflow_statement.pk])
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(204, response.status_code)
        self.assertEqual(0, CashFlowStatement.objects.count())

    def test_can_delete_double_role(self):
        """
        Assessor should be able to delete the CF statement, when the assessor
        is both financial assessor and regular assessor and the regular kind
        has been submitted.
        """
        cashflow_statement = CashFlowStatementFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            assessment__assessmentassignments__assigned_as=AssessmentAssignment.AS_ASSESSOR,
        )
        assignment = cashflow_statement.assessment.assessmentassignments.get()
        AssessmentAssignmentFactory.create(
            assessment=assignment.assessment,
            assessor=assignment.assessor,
            assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        assignment.locked_for_assessor = True
        assignment.locked_for_employee = False
        assignment.save()
        url = reverse("assessments:cashflowstatement-detail", [cashflow_statement.pk])
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(204, response.status_code)
        self.assertEqual(0, CashFlowStatement.objects.count())

    def test_can_not_delete(self):
        """
        Regular Assessor should not be able to delete the CF statement
        """
        cashflow_statement = CashFlowStatementFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            assessment__assessmentassignments__assigned_as=AssessmentAssignment.AS_ASSESSOR,
        )
        url = reverse("assessments:cashflowstatement-detail", [cashflow_statement.pk])
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(403, response.status_code)
        self.assertEqual(1, CashFlowStatement.objects.count())

    def test_can_not_delete_double_role(self):
        """
        Assessor should not be able to delete the CF statement, when the assessor
        is both financial assessor and regular assessor and the financial kind
        has been submitted.
        """
        cashflow_statement = CashFlowStatementFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            assessment__assessmentassignments__assigned_as=AssessmentAssignment.AS_ASSESSOR,
        )
        assignment = cashflow_statement.assessment.assessmentassignments.get()
        fin_assignment = AssessmentAssignmentFactory.create(
            assessment=assignment.assessment,
            assessor=assignment.assessor,
            assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        fin_assignment.locked_for_assessor = True
        fin_assignment.locked_for_employee = False
        fin_assignment.save()
        url = reverse("assessments:cashflowstatement-detail", [cashflow_statement.pk])
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(403, response.status_code)
        self.assertEqual(1, CashFlowStatement.objects.count())
