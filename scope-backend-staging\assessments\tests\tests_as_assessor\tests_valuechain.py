import json

from rest_framework.reverse import reverse

from assessments.factories import AssessmentFactory, ValueChainPlayerFactory
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class ValueChainTestCase(DenormMixin, AssessorJWTTestCase):
    def test_can_list(self):
        """
        ValuechainPlayer should have correct fields
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )

        ValueChainPlayerFactory.create(assessment=assessment)

        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        value_chain_players = response_dict["valuechainplayers"]["objects"]

        required_keys = set(
            [
                "percent_sold_to_customer",
                "relation_to_producing_organization",
                "contract_in_place",
                "number_of_years_in_relation",
                "id",
                "description_of_agreement",
                "assessment",
                "contract_start_year",
                "display_relation_to_producing_organization",
                "customer",
                "url",
                "modified_at",
                "contract_end_year",
                "customer_type",
                "contact",
                "display_contract_in_place",
                "display_customer_type",
                "description_of_relation",
                "product_types_purchased",
                "other",
            ]
        )

        actual_keys = set(value_chain_players[0].keys())

        self.assertEqual(1, len(value_chain_players))
        self.assertSetEqual(required_keys, actual_keys)
