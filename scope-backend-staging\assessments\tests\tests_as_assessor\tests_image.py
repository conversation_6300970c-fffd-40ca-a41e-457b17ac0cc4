import base64
import json
from shutil import rmtree
from tempfile import mkdtemp

from django.conf import settings
from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import AssessmentAssignmentFactory, ImageFactory
from assessments.models import Image
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class ImageTestCase(DenormMixin, AssessorJWTTestCase):
    def tearDown(self):
        dirs_to_delete = getattr(self, "dirs_to_delete", [])
        for dirname in set(dirs_to_delete):
            rmtree(dirname)

    def test_can_create_image_for_own_assessment(self):
        temp_media_dir = mkdtemp(dir=settings.BASE_DIR)
        self.dirs_to_delete = [temp_media_dir]
        with self.settings(MEDIA_ROOT=temp_media_dir):
            assignment = AssessmentAssignmentFactory.create(assessor=self.assessor)

            image_data = "/9j/4AAQSkZJRgABAQEASABIAAD/2wBDAAMCAgICAgMCAgIDAwMDBAYEBAQEBAgGBgUGCQgKCgkICQkKDA8MCgsOCwkJDRENDg8QEBEQCgwSExIQEw8QEBD/yQALCAABAAEBAREA/8wABgAQEAX/2gAIAQEAAD8A0s8g/9k="
            data = {
                "assessment": reverse(
                    "assessments:assessment-detail", [assignment.assessment_id]
                ),
                "file": "data: image/jpeg;base64,{}".format(image_data),
                "file_name": "blub.jpg",
                "title": "blub",
            }
            url = reverse("assessments:image-list")
            response = self.client.post(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(
                status.HTTP_201_CREATED, response.status_code, response.content
            )
            self.assertEqual(1, Image.objects.count())
            image = Image.objects.get()
            self.assertEqual(assignment.assessment, image.assessment)
            self.assertEqual("blub", image.title)
            self.assertEqual("assessments/image/blub.jpg", image.file.name)
            self.assertEqual(image_data.encode(), base64.b64encode(image.file.read()))

    def test_can_delete_image_for_own_assessment(self):
        temp_media_dir = mkdtemp(dir=settings.BASE_DIR)
        self.dirs_to_delete = [temp_media_dir]
        with self.settings(MEDIA_ROOT=temp_media_dir):
            image = ImageFactory.create(
                assessment__assessmentassignments__assessor=self.assessor
            )
            url = reverse("assessments:image-detail", [image.pk])
            response = self.client.delete(url, content_type="application/json")
            self.assertEqual(status.HTTP_204_NO_CONTENT, response.status_code)
            self.assertEqual(0, Image.objects.count())

    def test_can_update_image_for_own_assessment(self):
        temp_media_dir = mkdtemp(dir=settings.BASE_DIR)
        self.dirs_to_delete = [temp_media_dir]
        with self.settings(MEDIA_ROOT=temp_media_dir):
            image = ImageFactory.create(
                assessment__assessmentassignments__assessor=self.assessor
            )
            data = {"title": "new title"}
            url = reverse("assessments:image-detail", [image.pk])
            response = self.client.patch(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(status.HTTP_200_OK, response.status_code)
            image.refresh_from_db()
            self.assertEqual("new title", image.title)

    def test_cannot_create_image_for_other_assessment(self):
        temp_media_dir = mkdtemp(dir=settings.BASE_DIR)
        self.dirs_to_delete = [temp_media_dir]
        with self.settings(MEDIA_ROOT=temp_media_dir):
            assignment = AssessmentAssignmentFactory.create()

            image_data = "/9j/4AAQSkZJRgABAQEASABIAAD/2wBDAAMCAgICAgMCAgIDAwMDBAYEBAQEBAgGBgUGCQgKCgkICQkKDA8MCgsOCwkJDRENDg8QEBEQCgwSExIQEw8QEBD/yQALCAABAAEBAREA/8wABgAQEAX/2gAIAQEAAD8A0s8g/9k="
            data = {
                "assessment": reverse(
                    "assessments:assessment-detail", [assignment.assessment_id]
                ),
                "file": "data: image/jpeg;base64,{}".format(image_data),
                "file_name": "blub.jpg",
                "title": "blub",
            }
            url = reverse("assessments:image-list")
            response = self.client.post(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(
                status.HTTP_403_FORBIDDEN, response.status_code, response.content
            )
            self.assertEqual(0, Image.objects.count())

    def test_cannot_delete_image_for_other_assessment(self):
        temp_media_dir = mkdtemp(dir=settings.BASE_DIR)
        self.dirs_to_delete = [temp_media_dir]
        with self.settings(MEDIA_ROOT=temp_media_dir):
            image = ImageFactory.create()
            url = reverse("assessments:image-detail", [image.pk])
            response = self.client.delete(url, content_type="application/json")
            self.assertEqual(status.HTTP_404_NOT_FOUND, response.status_code)
            self.assertEqual(1, Image.objects.count())

    def test_cannot_update_image_for_other_assessment(self):
        temp_media_dir = mkdtemp(dir=settings.BASE_DIR)
        self.dirs_to_delete = [temp_media_dir]
        with self.settings(MEDIA_ROOT=temp_media_dir):
            image = ImageFactory.create()
            data = {"title": "new title"}
            url = reverse("assessments:image-detail", [image.pk])
            response = self.client.patch(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(status.HTTP_404_NOT_FOUND, response.status_code)
            image.refresh_from_db()
            self.assertNotEqual("new title", image.title)

    def test_images_in_assessment_get(self):
        temp_media_dir = mkdtemp(dir=settings.BASE_DIR)
        self.dirs_to_delete = [temp_media_dir]
        with self.settings(MEDIA_ROOT=temp_media_dir):
            image = ImageFactory.create(
                assessment__assessmentassignments__assessor=self.assessor
            )
            url = reverse("assessments:assessment-detail", [image.assessment_id])
            response = self.client.get(url, content_type="application/json")
            response_dict = json.loads(response.content)
            self.assertIn("images", response_dict)
            images = response_dict["images"]
            self.assertIn("objects", images)
            objects = images["objects"]
            self.assertEqual(1, len(objects))
            obj = objects[0]
            self.assertIn("title", obj)
            self.assertIn("file", obj)
            self.assertIn("file_name", obj)

    def test_assessment_mandatory(self):
        data = {}
        url = reverse("assessments:image-list")
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(
            status.HTTP_400_BAD_REQUEST, response.status_code, response.content
        )
        response_dict = json.loads(response.content)
        self.assertIn("assessment", response_dict)
        self.assertListEqual(["This field is required."], response_dict["assessment"])

    def test_file_mandatory(self):
        data = {}
        url = reverse("assessments:image-list")
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(
            status.HTTP_400_BAD_REQUEST, response.status_code, response.content
        )
        response_dict = json.loads(response.content)
        self.assertIn("file", response_dict)
        self.assertListEqual(["No file was submitted."], response_dict["file"])

    def test_file_name_mandatory(self):
        data = {}
        url = reverse("assessments:image-list")
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(
            status.HTTP_400_BAD_REQUEST, response.status_code, response.content
        )
        response_dict = json.loads(response.content)
        self.assertIn("file_name", response_dict)
        self.assertListEqual(["This field is required."], response_dict["file_name"])

    def test_title_mandatory(self):
        data = {}
        url = reverse("assessments:image-list")
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(
            status.HTTP_400_BAD_REQUEST, response.status_code, response.content
        )
        response_dict = json.loads(response.content)
        self.assertIn("title", response_dict)
        self.assertListEqual(["This field is required."], response_dict["title"])

    def test_file_must_be_image(self):
        temp_media_dir = mkdtemp(dir=settings.BASE_DIR)
        self.dirs_to_delete = [temp_media_dir]
        with self.settings(MEDIA_ROOT=temp_media_dir):
            assignment = AssessmentAssignmentFactory.create(assessor=self.assessor)

            image_data = "Ygo="
            data = {
                "assessment": reverse(
                    "assessments:assessment-detail", [assignment.assessment_id]
                ),
                "file": "data: image/jpeg;base64,{}".format(image_data),
                "file_name": "blub.jpg",
                "title": "blub",
            }
            url = reverse("assessments:image-list")
            response = self.client.post(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(
                status.HTTP_400_BAD_REQUEST, response.status_code, response.content
            )
            self.assertEqual(0, Image.objects.count())
            response_dict = json.loads(response.content)
            self.assertIn("file", response_dict)
            self.assertListEqual(["File is not an image."], response_dict["file"])
