# Generated by Django 2.2.20 on 2021-09-28 19:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0403_auto_20210921_2341"),
    ]

    operations = [
        migrations.AddField(
            model_name="agentincome",
            name="income_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="account_receivables_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="accounts_payable_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="average_inventory_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="average_receivables_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="cash_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="deferred_tax_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="fixed_assets_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="goodwill_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="grants_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="income_tax_payable_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="intangible_assets_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="inventories_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="long_term_loans_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="manual_total_assets_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="net_assets_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="net_working_capital_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="other_current_assets_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="other_current_liabilities_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="other_non_current_assets_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="other_non_current_liabilities_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="other_receivables_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="other_reserves_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="other_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="overdrafts_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="provisions_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="retained_earnings_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="share_capital_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="share_premium_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="short_term_loans_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="short_term_provisions_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="statutory_legal_reserves_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="total_assets_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="total_current_assets_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="total_current_liabilities_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="total_equity_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="total_liabilities_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="total_non_current_assets_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancesheet",
            name="total_non_current_liabilities_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="bankaccount",
            name="current_balance_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="basicfinancialinfo",
            name="cost_of_sales_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="basicfinancialinfo",
            name="net_profit_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="basicfinancialinfo",
            name="shareholders_equity_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="basicfinancialinfo",
            name="turnover_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="basicprofitlossstatement",
            name="cost_of_sales_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="basicprofitlossstatement",
            name="gross_profit_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="basicprofitlossstatement",
            name="net_profit_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="basicprofitlossstatement",
            name="operational_costs_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="basicprofitlossstatement",
            name="turnover_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="adjustment_for_tax_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="cash_at_beginning_of_period_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="cash_at_end_of_period_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="cash_generated_from_operations_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="decrease_in_trade_and_other_receivables_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="depreciation_and_amortization_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="dividends_paid_to_organizations_shareholders_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="dividends_received_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="financing_other_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="income_taxes_paid_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="increase_in_inventories_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="increase_in_trade_and_other_payables_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="interest_expense_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="interest_paid_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="interest_received_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="investing_other_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="investment_income_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="loans_granted_to_associates_or_subsidiaries_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="net_cash_flow_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="net_cash_from_operating_activities_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="net_cash_used_in_financing_activities_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="net_cash_used_in_investing_activities_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="net_income_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="proceeds_from_borrowings_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="proceeds_from_loan_from_subsidiary_undertaking_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="proceeds_from_sale_of_ordinary_shares_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="profit_on_sale_of_ppe_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="purchase_of_treasury_shares_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="purchases_of_financial_assets_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="purchases_of_intangible_assets_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="purchases_of_property_plant_and_equipment_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="repayments_from_borrowings_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="cashflowstatement",
            name="working_capital_changes_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="collateralasset",
            name="estimated_value_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="costofsale",
            name="value_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="expense",
            name="value_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="granthistory",
            name="amount_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="inputpurchase",
            name="price_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="loanapplication",
            name="amount_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="loanhistory",
            name="amount_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="loanrequirement",
            name="amount_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="loanrequirement",
            name="value_of_collateral_to_pledge_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="prefinancehistory",
            name="amount_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="depreciation_and_amortization_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="direct_sales_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="earnings_from_discontinued_operations_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="ebit_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="ebitda_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="gross_profit_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="income_from_continuing_operations_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="income_tax_expense_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="indirect_sales_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="interest_expense_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="manual_net_profit_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="net_profit_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="other_income_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="total_cost_of_sales_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="total_expenses_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="profitlossstatement",
            name="total_sales_usd",
            field=models.DecimalField(
                blank=True, decimal_places=2, editable=False, max_digits=20, null=True
            ),
        ),
    ]
