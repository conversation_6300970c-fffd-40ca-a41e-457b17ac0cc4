from django.core.management.base import BaseCommand
from django.db.models import Prefetch
from django.core.serializers.json import DjangoJSONEncoder
from assessments.models import Response as ResponseModel, Assessment, SubResponse
import json

class Command(BaseCommand):
    help = 'Export all responses, subresponses, subquestions, and subquestion options for a given assessment in JSON format'

    def add_arguments(self, parser):
        parser.add_argument('assessment_id', type=int, help='ID of the assessment to export')

    def handle(self, *args, **kwargs):
        assessment_id = kwargs['assessment_id']
        output_file = f'Assessment_{assessment_id}.json'
        
        try:
            assessment = Assessment.objects.get(id=assessment_id)
        except Assessment.DoesNotExist:
            self.stdout.write(self.style.ERROR('Assessment not found'))
            return
        
        responses = ResponseModel.objects.filter(assessment=assessment).prefetch_related(
            Prefetch('subresponses', queryset=SubResponse.objects.select_related('subquestion').prefetch_related('subquestion__options')),
        )

        # Custom serialization to include nested relationships
        data = []
        for response in responses:
            response_data = {
                'question': {
                    'title': response.question.title,
                    'description': response.question.description,
                    'explanation': response.question.explanation,
                },
                'section': response._section_response.parent.section_title if response._section_response else None,
                'subresponses': [],
            }

            for subresponse in response.subresponses.all():
                subresponse_data = {
                    'subquestion': {
                        'title': subresponse.subquestion.title,
                        'description': subresponse.subquestion.description,
                        'options': [],
                    },
                    'checked_options': [option.title for option in subresponse.checked_options.all()],
                    'selected_option': subresponse.selected_option.title if subresponse.selected_option else None,
                    'value': subresponse.value,
                    'not_relevant': subresponse.not_relevant,
                    'none_of_the_above': subresponse.none_of_the_above,
                }

                for option in subresponse.subquestion.options.all():
                    subresponse_data['subquestion']['options'].append(option.title)

                response_data['subresponses'].append(subresponse_data)

            data.append(response_data)
        # Print JSON to console or write to a file
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, cls=DjangoJSONEncoder)
            self.stdout.write(self.style.SUCCESS(f'Successfully exported data to {output_file}'))
        except IOError as e:
            self.stdout.write(self.style.ERROR(f'Error writing to file: {e}'))