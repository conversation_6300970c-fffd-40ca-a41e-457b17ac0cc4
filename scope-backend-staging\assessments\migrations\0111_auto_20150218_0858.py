from collections import Counter

from django.db import migrations


def uniqueify_financial_ratio(apps, schema_editor):
    FinancialRatio = apps.get_model("assessments", "FinancialRatio")
    uniqueness_counter = Counter(
        FinancialRatio.objects.all().values_list("assessment_id", "year")
    )
    for (assessment_id, year), count in uniqueness_counter.most_common():
        if count <= 1:
            break
        duplicates = FinancialRatio.objects.filter(
            assessment_id=assessment_id, year=year
        )[: count - 1]
        for financialratio in duplicates:
            financialratio.year = (
                min(
                    list(
                        FinancialRatio.objects.filter(
                            assessment_id=assessment_id
                        ).values_list("year", flat=True)
                    )
                )
                - 1
            )
            financialratio.save()


class Migration(migrations.Migration):

    dependencies = [("assessments", "0110_auto_20150218_0849")]

    operations = [migrations.RunPython(uniqueify_financial_ratio)]
