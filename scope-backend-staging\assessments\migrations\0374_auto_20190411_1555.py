# Generated by Django 2.1.8 on 2019-04-11 15:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0373_auto_20190410_1501")]

    operations = [
        migrations.AddField(
            model_name="assessment",
            name="bank_accounts_no_info_reason",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="basic_financial_infos_no_info_reason",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="basic_profit_loss_no_info_reason",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="capital_requirements_no_info_reason",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="collateral_assets_no_info_reason",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="grant_histories_no_info_reason",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="insurances_no_info_reason",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="loan_applications_no_info_reason",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="loan_histories_no_info_reason",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="loan_requirements_no_info_reason",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="pre_finance_histories_no_info_reason",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessment",
            name="shareholders_no_info_reason",
            field=models.TextField(blank=True),
        ),
    ]
