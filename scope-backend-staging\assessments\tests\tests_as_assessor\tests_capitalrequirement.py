import json

from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import AssessmentFactory, CapitalRequirementFactory
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class CapitalRequirementTestCase(DenormMixin, AssessorJWTTestCase):
    def test_get_table(self):
        """
        Create and get for capital requirement table should be possible
        """
        capital_requirement = CapitalRequirementFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:capitalrequirement-detail", [capital_requirement.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("status", response_dict)

    def test_create_table(self):
        """
        Create and get for capital requirement table should be possible
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )

        data = {
            "assessment": reverse("assessments:assessment-detail", [assessment.pk]),
            "amount": {"currency": "USD", "amount": "1000"},
            "share_type": "cookies",
            "status": "done",
            "description": "Description",
            "comment": "Comment",
        }
        url = reverse("assessments:capitalrequirement-list")
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_201_CREATED, response.status_code)

        response_dict = json.loads(response.content)

        self.assertEqual("1000", response_dict["amount"]["amount"])
        self.assertEqual("USD", response_dict["amount"]["currency"])
        self.assertEqual("done", response_dict["status"])
        self.assertEqual("cookies", response_dict["share_type"])
        self.assertEqual("Description", response_dict["description"])
        self.assertEqual("Comment", response_dict["comment"])

    def test_patch_table(self):
        """
        Create and get for capital requirement table should be possible
        """

        capital_requirement = CapitalRequirementFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            share_type="cookies",
            status="done",
            description="Description",
            comment="Comment",
        )
        data = {
            "amount": {"currency": "EUR", "amount": "2000"},
            "share_type": "cake",
            "status": "N/A",
            "description": "Description bla",
            "comment": "Comment bla",
        }
        url = reverse("assessments:capitalrequirement-detail", [capital_requirement.pk])
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)

        response_dict = json.loads(response.content)

        self.assertEqual("2000", response_dict["amount"]["amount"])
        self.assertEqual("EUR", response_dict["amount"]["currency"])
        self.assertEqual("N/A", response_dict["status"])
        self.assertEqual("cake", response_dict["share_type"])
        self.assertEqual("Description bla", response_dict["description"])
        self.assertEqual("Comment bla", response_dict["comment"])
