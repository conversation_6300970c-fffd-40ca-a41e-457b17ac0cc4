import json

from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import ResponseFactory
from libs.test_helpers import AssessorJ<PERSON>TTestCase, DenormMixin


class ResponseTestCase(DenormMixin, AssessorJWTTestCase):
    def test_can_not_modify_accepted_answer_after_submit(self):
        """
        Assessor should not be able to modify an accepted answer
        """
        response = ResponseFactory.create(
            accepted=True,
            assessment__assessmentassignments__submitted_at_least_once=True,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        url = reverse("assessments:response-detail", [response.pk])
        data = {}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)

    def test_can_modify_not_accepted_answer_after_submit(self):
        """
        Assessor should be able to modify not accepted answer
        """
        response = ResponseFactory.create(
            accepted=False,
            assessment__assessmentassignments__submitted_at_least_once=True,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        url = reverse("assessments:response-detail", [response.pk])
        data = {}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_modify_answer_before_submit(self):
        """
        Assessor should be able to modify answer before submitting
        """
        response = ResponseFactory.create(
            accepted=True,
            assessment__assessmentassignments__submitted_at_least_once=False,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        url = reverse("assessments:response-detail", [response.pk])
        data = {}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_comment_is_always_allowed(self):
        """
        subresponse.comment modification is always allowed
        """
        response = ResponseFactory.create(
            accepted=True,
            assessment__assessmentassignments__submitted_at_least_once=True,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        url = reverse("assessments:response-detail", [response.pk])
        data = {"comment": "bliep"}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
