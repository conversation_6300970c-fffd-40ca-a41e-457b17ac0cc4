import progressbar
import tablib
from django.core.management.base import BaseCommand

from assessments.models import Product


class Command(BaseCommand):
    args = "<file>"
    help = "Import fao product names"

    def add_arguments(self, parser):
        parser.add_argument("file", nargs="+", type=str)

    def handle(self, *args, **options):
        filename = options["file"][0]
        pre_import_data = list(
            Product.objects.all()
            .order_by("assessment_id", "id")
            .values_list("assessment_id", "id", "name", "global_product_type")
        )
        with open("pre_fao_import.xlsx", "wb") as f:
            f.write(
                tablib.Dataset(
                    *pre_import_data,
                    headers=[
                        "assessment_id",
                        "product_id",
                        "name",
                        "global_product_type",
                    ]
                ).xlsx
            )

        import_data = tablib.Dataset()
        with open(filename, "rb") as f:
            import_data.load(f.read())
        for item in progressbar.progressbar(import_data.dict):
            if item["FAO Item Name"]:
                Product.objects.filter(
                    assessment_id=item["Assessment id"], name=item["Product name"]
                ).update(
                    name=item["FAO Item Name"],
                    global_product_type=item["FAO Category Name"] or "",
                )

        pre_import_data = list(
            Product.objects.all()
            .order_by("assessment_id", "id")
            .values_list("assessment_id", "id", "name", "global_product_type")
        )
        with open("post_fao_import.xlsx", "wb") as f:
            f.write(
                tablib.Dataset(
                    *pre_import_data,
                    headers=[
                        "assessment_id",
                        "product_id",
                        "name",
                        "global_product_type",
                    ]
                ).xlsx
            )
