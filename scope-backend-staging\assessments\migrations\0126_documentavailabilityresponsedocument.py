from django.db import migrations, models

import libs.models_helpers


class Migration(migrations.Migration):

    dependencies = [
        ("hrm", "0013_assessor_second_region"),
        ("assessments", "0125_assessment_auditing_firm"),
    ]

    operations = [
        migrations.CreateModel(
            name="DocumentAvailabilityResponseDocument",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "file",
                    models.FileField(
                        null=True,
                        upload_to=libs.models_helpers.class_based_upload_to,
                        blank=True,
                    ),
                ),
                ("accepted", models.BooleanField(default=False)),
                (
                    "_assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        editable=False,
                        to="assessments.Assessment",
                        null=True,
                    ),
                ),
                (
                    "documentavailabilityresponse",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="documents",
                        to="assessments.DocumentAvailabilityResponse",
                    ),
                ),
                (
                    "quality_controller",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="documentavailabilityresponsedocument_quality_controlled",
                        blank=True,
                        to="hrm.Employee",
                        null=True,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        )
    ]
