from django.db import migrations


def move_assessment_customer_info(apps, schema_editor):
    # We can't import the Person model directly as it may be a newer
    # version than this migration expects. We use the historical version.
    Customer = apps.get_model("customers", "Customer")
    OrganizationType = apps.get_model("customers", "OrganizationType")
    ValueChainPlayer = apps.get_model("assessments", "ValueChainPlayer")
    EnablingPlayer = apps.get_model("assessments", "EnablingPlayer")
    for player in list(ValueChainPlayer.objects.all()) + list(
        EnablingPlayer.objects.all()
    ):
        player.customer = Customer.objects.get_or_create(name=player.name)[0]
        if not player.customer.type:
            player.customer.type = OrganizationType.objects.get_or_create(
                name=player.type.name
            )[0]
            player.customer.save()
        player.save()


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0051_auto_20150118_2118"),
        ("customers", "0014_auto_20150118_2118"),
    ]

    operations = [migrations.RunPython(move_assessment_customer_info)]
