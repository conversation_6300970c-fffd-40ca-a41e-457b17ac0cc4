from django.db import migrations


def end_of_term_integer_to_string(apps, schema_editor):
    Executive = apps.get_model("assessments", "Executive")
    NonExecutive = apps.get_model("assessments", "NonExecutive")
    for executive in Executive.objects.all():
        if executive.old_end_of_term:
            executive.end_of_term = "01-{}".format(executive.old_end_of_term)
            executive.save()
    for nonexecutive in NonExecutive.objects.all():
        if nonexecutive.old_end_of_term:
            nonexecutive.end_of_term = "01-{}".format(nonexecutive.old_end_of_term)
            nonexecutive.save()


class Migration(migrations.Migration):

    dependencies = [("assessments", "0195_auto_20160905_1007")]

    operations = [migrations.RunPython(end_of_term_integer_to_string)]
