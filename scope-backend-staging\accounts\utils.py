from collections import OrderedDict
import requests
import json

from django.conf import settings
from django.http import JsonResponse
from django.db.models import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Min
from django.db.models.functions import Greatest, Least
from django.utils.translation import ugettext_lazy as _
from rest_framework.response import Response

from customers.models import Contact
from libs.generic_helpers import unfilter
from pagination import CustomPageNumberPagination


class Unnest(Func):
    function = "UNNEST"


class UserPagination(CustomPageNumberPagination):
    def get_filters(self, paginator):
        roles = []
        roles_queryset = unfilter(paginator.object_list, "roles")

        has_assessors = roles_queryset.filter(is_assessor=True).exists()
        if has_assessors:
            roles.append({"label": "Assessor", "value": "is_assessor"})
        has_financial_specialists = roles_queryset.filter(
            is_financial_specialist=True
        ).exists()
        if has_financial_specialists:
            roles.append(
                {"label": "Financial specialist", "value": "is_financial_specialist"}
            )
        has_employees = roles_queryset.filter(is_employee=True).exists()
        if has_employees:
            roles.append({"label": "SI Employee", "value": "is_employee"})
        has_quality_controllers = roles_queryset.filter(
            is_quality_reviewer=True
        ).exists()
        if has_quality_controllers:
            roles.append(
                {"label": "SI Quality reviewer", "value": "is_quality_reviewer"}
            )
        has_contacts = Contact.objects.filter(user__in=roles_queryset).exists()
        if has_contacts:
            roles.append({"label": "Subscriber", "value": "is_contact"})
        has_dashboard_admins = roles_queryset.filter(is_customer_admin=True).exists()
        if has_dashboard_admins:
            roles.append({"label": "Dashboard Admin", "value": "is_customer_admin"})
        has_dashboard_users = roles_queryset.filter(is_customer_user=True).exists()
        if has_dashboard_users:
            roles.append({"label": "Dashboard User", "value": "is_customer_user"})
        
        has_self_assessors = roles_queryset.filter(is_self_assessor=True).exists()
        if has_self_assessors:
            roles.append({"label": "Self Assessor", "value": 'is_self_assessor'})
        
        has_data_collectors = roles_queryset.filter(is_data_collector=True).exists()
        if has_data_collectors:
            roles.append({"label": "Data Collector", "value": 'is_data_collector'})

        organization = [
            {"label": item, "value": item}
            for item in (
                unfilter(paginator.object_list, "organization")
                .values_list("organization__name", flat=True)
                .order_by("organization__name")
                .distinct()
            )
        ]
        country = [
            {"label": _(item), "value": item}
            for item in (
                unfilter(paginator.object_list, "country")
                .values_list("country", flat=True)
                .order_by("country")
                .distinct()
            )
        ]
        is_active = [
            {"label": _("Yes") if item else _("No"), "value": 1 if item else 0}
            for item in (
                unfilter(paginator.object_list, "is_active")
                .exclude(is_active__isnull=True)
                .values_list("is_active", flat=True)
                .order_by("-is_active")
                .distinct()
            )
        ]
        languages_spoken = [
            {"label": _(dict(settings.LANGUAGES).get(item, item)), "value": item}
            for item in (
                set(
                    unfilter(paginator.object_list, "languages_spoken")
                    .exclude(languages_spoken__isnull=True)
                    .annotate(
                        distinct_languages=Unnest("languages_spoken", distinct=True)
                    )
                    .values_list("distinct_languages", flat=True)
                )
            )
        ]
        tools = [
            {"label": item, "value": item}
            for item in (
                unfilter(paginator.object_list, "tool_type")
                .exclude(assessor__is_active=False)
                .exclude(assessor__tools__isnull=True)
                .values_list("assessor__tools__name", flat=True)
                .order_by("assessor__tools__name")
                .distinct()
            )
        ]
        roles_params = self.request.query_params.getlist("roles")
        assessor = "is_assessor" in roles_params
        financial_specialist = "is_financial_specialist" in roles_params
        if assessor and financial_specialist:
            aggregates = unfilter(paginator.object_list, "expiration_date").aggregate(
                min_date=Min(
                    Least(
                        "assessor__expiration_date_assessor",
                        "assessor__expiration_date_financial_specialist",
                        output_field=DateField(),
                    )
                ),
                max_date=Max(
                    Greatest(
                        "assessor__expiration_date_assessor",
                        "assessor__expiration_date_financial_specialist",
                        output_field=DateField(),
                    )
                ),
            )
        elif assessor:
            aggregates = unfilter(paginator.object_list, "expiration_date").aggregate(
                min_date=Min("assessor__expiration_date_assessor"),
                max_date=Max("assessor__expiration_date_assessor"),
            )
        elif financial_specialist:
            aggregates = unfilter(paginator.object_list, "expiration_date").aggregate(
                min_date=Min("assessor__expiration_date_financial_specialist"),
                max_date=Max("assessor__expiration_date_financial_specialist"),
            )
        else:
            aggregates = unfilter(paginator.object_list, "expiration_date").aggregate(
                min_date=Min(
                    Least(
                        "assessor__expiration_date_assessor",
                        "assessor__expiration_date_financial_specialist",
                        output_field=DateField(),
                    )
                ),
                max_date=Max(
                    Greatest(
                        "assessor__expiration_date_assessor",
                        "assessor__expiration_date_financial_specialist",
                        output_field=DateField(),
                    )
                ),
            )

        return {
            "organization": organization,
            "country": country,
            "is_active": is_active,
            "languages_spoken": languages_spoken,
            "tools": tools,
            "expiration_date": {
                "min": aggregates["min_date"],
                "max": aggregates["max_date"],
            },
            "roles": roles,
        }

    def get_paginated_response(self, data):
        return Response(
            OrderedDict(
                [
                    ("count", self.page.paginator.count),
                    ("filters", self.get_filters(self.page.paginator)),
                    ("next", self.get_next_link()),
                    ("previous", self.get_previous_link()),
                    ("results", data),
                ]
            )
        )

def register_learnworks_user(request, instance):
    api_url = f"{settings.LEARNWORLDS_API_URL}/users"
    headers = {
        "Authorization": f"Bearer {settings.LEARNWORLDS_TOKEN}",
        "Lw-Client": settings.LEARNWORLDS_CLIENT_ID
    }
    data = {
        "tags": [instance.language, instance.organization.name],
        "email": instance.email,
        "username": instance.full_name,
        "userRole": "user",
        "send_registration_email": True,
        "signup_validation_rules": False,
        "subscribed_for_marketing_emails": False
    }
    try:
        response = requests.post(api_url, data=json.dumps(data), headers=headers)
        if response.status_code >= 400:
            # If request was unsuccessful, return an error message
            print({'error': 'Failed to fetch data from the API'}, status=response.status_code)
            return
        else:
            print(response.json())
            return
    except requests.RequestException as e:
        # If an error occurs during the request, return an error message
        print({'error': str(e)}, status=500)
