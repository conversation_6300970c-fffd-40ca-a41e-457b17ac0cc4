{% load i18n %}
{% language language_code %}
{% if trainee %}
{% blocktrans with fullname=to_user.get_full_name tool=assessment.tool.type.name assessor=assessor.get_full_name assignment=assignment po_name=assessment.producing_organization.customer.name po_city=assessment.producing_organization.city po_country=assessment.producing_organization.country project_name=assessment.project.name customer_name=assessment.project.customer.name axr_title=axr_title ax_type=ax_type %}
Dear {{ fullname }},

{{ assessor }} has submitted their feedback for the {{ tool }} {{ax_type}} of {{ po_name }} in {{ po_city }}, {{ po_country }}.

This assessment is part of {{ project_name }} of {{ customer_name }}.

<a href="{{ frontend_prefix }}/#/assessments/assignment/detail/{{ assignment }}">Click here to review the assessment</a>.

Kind regards,
SCOPEinsight{% endblocktrans %}

{% else %}
{% blocktrans with fullname=to_user.get_full_name tool=assessment.tool.type.name assessor=assessor.get_full_name assignment=assignment po_name=assessment.producing_organization.customer.name po_city=assessment.producing_organization.city po_country=assessment.producing_organization.country project_name=assessment.project.name customer_name=assessment.project.customer.name axr_title=axr_title ax_type=ax_type %}
Dear {{ fullname }},

{{ assessor }} has submitted their feedback for the {{ tool }} {{ ax_type }} of {{ po_name }} in {{ po_city }}, {{ po_country }}.

This assessment is part of {{ project_name }} of {{ customer_name }}.

<a href="{{ frontend_prefix }}/#/assessments/assignment/detail/{{ assignment }}">Click here to review the assessment</a>.

Kind regards,
SCOPEinsight{% endblocktrans %}
{% endif %}
{% endlanguage %}
