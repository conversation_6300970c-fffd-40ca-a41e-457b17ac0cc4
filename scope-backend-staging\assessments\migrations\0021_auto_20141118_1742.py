from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("hrm", "0003_auto_20141118_0858"),
        ("assessments", "0020_auto_20141118_1742"),
        ("products", "0007_auto_20141118_1742"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="unscoredtoolresponse",
            name="question",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="responses",
                to="products.UnscoredToolQuestion",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="unscoredsectionresponsecomment",
            name="unscoredsectionresponse",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="comments",
                to="assessments.UnscoredSectionResponse",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="unscoredsectionresponsecomment",
            name="user",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="unscoredsectionresponsecomments",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="unscoredsectionresponse",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="unscored_section_responses",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="unscoredsectionresponse",
            name="assessor",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="unscored_section_responses",
                to="hrm.Assessor",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="unscoredsectionresponse",
            name="quality_controller",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="unscored_section_responses_quality_controlled",
                blank=True,
                to="hrm.Employee",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="unscoredsectionresponse",
            name="question",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="responses",
                to="products.UnscoredSectionQuestion",
            ),
            preserve_default=True,
        ),
    ]
