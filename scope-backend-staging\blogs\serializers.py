import base64
import mimetypes
import re
from uuid import uuid4

import magic
from bs4 import BeautifulSoup
from django.conf import settings
from django.core.files.base import ContentFile

from libs.rest_framework_helpers.serializers import CustomHyperlinkedModelSerializer

from .models import Blog, BlogSet

base64image_re = re.compile(r"data:image/.*?;base64,(.*)")


class BlogSetSerializer(CustomHyperlinkedModelSerializer):
    class Meta:
        model = BlogSet
        fields = (
            "id",
            "url",
            "modified_at",
            "created_at",
            "blogs",
            "archived_by",
            "read_by",
        )
        extra_kwargs = {
            "url": {"view_name": "blogs:blogset-detail"},
            "blogs": {"view_name": "blogs:blog-detail"},
            "read_by": {"view_name": "accounts:user-detail"},
            "archived_by": {"view_name": "accounts:user-detail"},
        }


class BlogSerializer(CustomHyperlinkedModelSerializer):
    def generate_filename(self, file_data):
        """
        Generate filename and full filename that django would generate on save
        """
        extension = mimetypes.guess_extension(
            magic.from_buffer(file_data, mime=True), strict=False
        )
        filename = "{}{}".format(str(uuid4()), extension)
        file_model = self.Meta.model._meta.get_field("images").related_model
        file_field = file_model._meta.get_field("file")
        prefixed_filename = file_field.generate_filename(file_model(), filename)
        full_filename = "{media_url}{prefixed_filename}".format(
            media_url=settings.MEDIA_URL, prefixed_filename=prefixed_filename
        )
        request = self.context.get("request")
        if request:
            full_filename = request.build_absolute_uri(full_filename)
        return filename, full_filename

    def extract_image(self, base64image):
        file_data = base64.b64decode(base64image)
        filename, full_filename = self.generate_filename(file_data)
        file_obj = ContentFile(file_data)
        file_obj.filename = filename
        return file_obj, full_filename

    def extract_images(self, validated_data):
        image_objs = []
        data = validated_data.get("body")
        if data is not None:
            body_changed = False
            soup = BeautifulSoup(validated_data["body"], "lxml")
            images = soup.find_all("img")
            for image in images:
                src = image.attrs["src"]
                image_match = base64image_re.match(src)
                if image_match:
                    image_obj, url = self.extract_image(image_match.group(1))
                    if image_obj is not None:
                        image_objs.append(image_obj)
                        image.attrs["src"] = url
                        body_changed = True
            if body_changed:
                validated_data["body"] = "".join(
                    [
                        str(element)
                        for element in soup.html.body.findChildren(recursive=False)
                    ]
                )
        return image_objs, validated_data

    def create(self, validated_data):
        image_objs, validated_data = self.extract_images(validated_data)
        instance = super(BlogSerializer, self).create(validated_data)
        for image_obj in image_objs:
            db_image = instance.images.model()
            db_image.blog = instance
            db_image.file.save(image_obj.filename, image_obj)
            db_image.save()
        return instance

    class Meta:
        fields = ("id", "url", "modified_at", "body", "subject", "language", "blog_set")
        model = Blog
        extra_kwargs = {
            "url": {"view_name": "blogs:blog-detail"},
            "blog_set": {"view_name": "blogs:blogset-detail"},
        }
