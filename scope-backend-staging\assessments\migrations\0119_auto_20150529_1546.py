from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0118_auto_20150529_1419")]

    operations = [
        migrations.CreateModel(
            name="ForestryPlan",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("objective_and_strategy", models.TextField(blank=True)),
                ("non_timber_products", models.TextField(blank=True)),
                ("environmental_services", models.TextField(blank=True)),
                ("commercial_species", models.TextField(blank=True)),
                (
                    "_assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        editable=False,
                        to="assessments.Assessment",
                        null=True,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name="assessment",
            name="forest_annual_plan",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="assessment2",
                null=True,
                blank=True,
                to="assessments.ForestryPlan",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="assessment",
            name="forest_management_plan",
            field=models.OneToOneField(
                on_delete=models.CASCADE,
                related_name="assessment1",
                null=True,
                blank=True,
                to="assessments.ForestryPlan",
            ),
            preserve_default=True,
        ),
    ]
