from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("customers", "0014_auto_20150118_2118"),
        ("assessments", "0050_assessment_observations"),
    ]

    operations = [
        migrations.AddField(
            model_name="enablingplayer",
            name="customer",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="enablingplayers",
                blank=True,
                to="customers.Customer",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="valuechainplayer",
            name="customer",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="valuechainplayers",
                blank=True,
                to="customers.Customer",
                null=True,
            ),
            preserve_default=True,
        ),
    ]
