from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("hrm", "0001_initial"), ("assessments", "0011_cashflowstatement")]

    operations = [
        migrations.CreateModel(
            name="FinancialRatio",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("year", models.PositiveSmallIntegerField()),
                (
                    "revenue_growth",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "net_profit_growth",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "gross_margin",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "operating_profit_margin",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "net_profit_margin",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "return_on_equity",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "return_on_assets",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "asset_turnover",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "working_capital_turnover",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "debt_to_assets_ratio",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "debt_to_equity_ratio",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "debt_coverage_ratio",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "debt_servicing",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "current_ratio",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "quick_ratio",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "days_inventory_outstanding",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "days_sales_outstanding",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="financial_ratios",
                        to="assessments.Assessment",
                    ),
                ),
                (
                    "assessor",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="financial_ratios",
                        to="hrm.Assessor",
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="FinancialScore",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("status", models.DecimalField(max_digits=2, decimal_places=1)),
                ("trend", models.DecimalField(max_digits=2, decimal_places=1)),
                ("volatility", models.DecimalField(max_digits=2, decimal_places=1)),
                (
                    "total_score",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=2,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="FinancialScores",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "assessment",
                    models.OneToOneField(
                        on_delete=models.CASCADE,
                        related_name="financial_scores",
                        to="assessments.Assessment",
                    ),
                ),
                (
                    "asset_turnover",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.FinancialScore",
                        null=True,
                    ),
                ),
                (
                    "current_ratio",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.FinancialScore",
                        null=True,
                    ),
                ),
                (
                    "days_inventory_outstanding",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.FinancialScore",
                        null=True,
                    ),
                ),
                (
                    "days_sales_outstanding",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.FinancialScore",
                        null=True,
                    ),
                ),
                (
                    "debt_coverage_ratio",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.FinancialScore",
                        null=True,
                    ),
                ),
                (
                    "debt_servicing",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.FinancialScore",
                        null=True,
                    ),
                ),
                (
                    "debt_to_assets_ratio",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.FinancialScore",
                        null=True,
                    ),
                ),
                (
                    "debt_to_equity_ratio",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.FinancialScore",
                        null=True,
                    ),
                ),
                (
                    "gross_margin",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.FinancialScore",
                        null=True,
                    ),
                ),
                (
                    "net_profit_growth",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.FinancialScore",
                        null=True,
                    ),
                ),
                (
                    "net_profit_margin",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.FinancialScore",
                        null=True,
                    ),
                ),
                (
                    "operating_profit_margin",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.FinancialScore",
                        null=True,
                    ),
                ),
                (
                    "quick_ratio",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.FinancialScore",
                        null=True,
                    ),
                ),
                (
                    "return_on_assets",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.FinancialScore",
                        null=True,
                    ),
                ),
                (
                    "return_on_equity",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.FinancialScore",
                        null=True,
                    ),
                ),
                (
                    "revenue_growth",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.FinancialScore",
                        null=True,
                    ),
                ),
                (
                    "working_capital_turnover",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.FinancialScore",
                        null=True,
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="GeneralCheck",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("available", models.NullBooleanField()),
                (
                    "quality",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, "not available / not good"),
                            (1, "there is a potential issue / pay attention"),
                            (2, "no issue, is available"),
                        ]
                    ),
                ),
                ("notes", models.TextField(blank=True)),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="GeneralChecks",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("quality_of_the_financial_statements", models.TextField(blank=True)),
                (
                    "accepted_format",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
                (
                    "assessment",
                    models.OneToOneField(
                        on_delete=models.CASCADE,
                        related_name="general_checks",
                        to="assessments.Assessment",
                    ),
                ),
                (
                    "asset_register",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
                (
                    "audited",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
                (
                    "balance_sheet",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
                (
                    "bank_statements",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
                (
                    "cash_flow",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
                (
                    "cash_flow_projections",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
                (
                    "collateral",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
                (
                    "depreciation_policy",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
                (
                    "external_valuation",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
                (
                    "financial_statements",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
                (
                    "grant_history",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
                (
                    "grants_recorded_in_financial_statement",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
                (
                    "loan_default_known",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
                (
                    "loan_history",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
                (
                    "loan_recorded_in_financial_statement",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
                (
                    "profit_and_loss",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
                (
                    "record_keeping",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
                (
                    "software_system",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        to="assessments.GeneralCheck",
                        null=True,
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="RatioScores",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("accountant_notes", models.TextField(blank=True)),
                (
                    "growth",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "profitability",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "productivity",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "solvency",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "liquidity",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "working_capital",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "total_score",
                    models.DecimalField(
                        null=True,
                        editable=False,
                        max_digits=6,
                        decimal_places=1,
                        blank=True,
                    ),
                ),
                (
                    "assessment",
                    models.OneToOneField(
                        on_delete=models.CASCADE,
                        related_name="ratio_scores",
                        to="assessments.Assessment",
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
    ]
