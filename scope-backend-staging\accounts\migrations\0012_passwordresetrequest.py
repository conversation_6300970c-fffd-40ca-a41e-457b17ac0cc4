from django.conf import settings
from django.db import migrations, models

import libs.generic_helpers


class Migration(migrations.Migration):

    dependencies = [("accounts", "0011_auto_20160202_1047")]

    operations = [
        migrations.CreateModel(
            name="PasswordResetRequest",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "uuid",
                    models.CharField(
                        default=libs.generic_helpers.uuid4_as_str,
                        unique=True,
                        max_length=36,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="password_reset_requests",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        )
    ]
