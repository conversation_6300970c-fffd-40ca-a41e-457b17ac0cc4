import calendar
import datetime
import operator
from collections import OrderedDict, namedtuple
from decimal import ROUND_HALF_UP, Decimal, DivisionByZero, InvalidOperation
from functools import reduce

import tablib
from denorm import denormalized, depend_on, flush
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator, RegexValidator
from django.db import models, transaction
from django.db.models import F, Max, Min, Prefetch, Q, Sum
from django.db.models.signals import post_delete, post_save, pre_delete, pre_save
from django.dispatch import receiver, Signal
from django.utils import translation
from django.utils.translation import ugettext, ugettext_lazy as _
from djmoney.models.fields import MoneyField
from djmoney.settings import CURRENCY_CHOICES
from moneyed import Money
from mptt.models import MPTTModel, TreeForeignKey
from rest_framework.exceptions import ParseError

from accounts.models import User
from assessments.constants import category_name_to_code, name_to_item_code
from conversion.models import ConversionRatio
from customers.models import Contact, ProducingOrganization
from hrm.models import AssessmentSkillType, Assessor, Employee
from libs.field_helpers import LowerCaseTextField
from libs.generic_helpers import (
    dict_compare,
    mean,
    partial_sum,
    strip_zeroes,
    weighted_average,
)
from libs.models_helpers import (
    BaseComment,
    BaseModel,
    BaseType,
    DocumentMixin,
    _add_strings_to_translation_files,
    commentable,
    documentable,
)
from libs.rest_framework_helpers.serializers import serialize_instance
from messaging.models import MessageType
from products.models import (
    DocumentAvailabilityQuestion,
    GlobalQualityIssuesOption,
    ProductPerTypeOption,
    ProductTypeOption,
    Question,
    ScaleType,
    Section,
    ServiceOption,
    SubQuestion,
    SubQuestionOption,
    Tool,
    UnscoredSectionQuestion,
)
from reports.models import Report


class LandUse(BaseModel):
    area = models.DecimalField(max_digits=10, decimal_places=2)
    unit = models.TextField()
    percent_irrigated = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    percent_productive_use = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    denorm_always_skip = ("modified_at",)

    @denormalized(
        models.ForeignKey,
        "Assessment",
        related_name="+",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    @depend_on("totallanduse1")
    @depend_on("totallanduse2")
    @depend_on("totallanduse3")
    def _assessment(self):
        totallanduse_names = ["totallanduse1", "totallanduse2", "totallanduse3"]
        totallanduse = None
        for name in totallanduse_names:
            try:
                totallanduse = getattr(self, name)
            except TotalLandUse.DoesNotExist:
                pass
        if totallanduse:
            return totallanduse._assessment
        else:
            return None

    class Meta:
        ordering = ("pk",)


class TotalLandUse(BaseModel):
    owned = models.OneToOneField(
        LandUse,
        related_name="totallanduse1",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    leased_rented = models.OneToOneField(
        LandUse,
        related_name="totallanduse2",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    used_otherwise = models.OneToOneField(
        LandUse,
        related_name="totallanduse3",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    denorm_always_skip = ("modified_at",)

    @denormalized(
        models.DecimalField, max_digits=10, decimal_places=2, blank=True, null=True
    )
    @depend_on("owned__area")
    @depend_on("leased_rented__area")
    @depend_on("used_otherwise__area")
    def total_area(self):
        return sum(
            [
                Decimal(self.owned.area) if self.owned else Decimal(0),
                (
                    Decimal(self.leased_rented.area)
                    if self.leased_rented
                    else Decimal(0)
                ),
                (
                    Decimal(self.used_otherwise.area)
                    if self.used_otherwise
                    else Decimal(0)
                ),
            ]
        )

    @denormalized(models.TextField, blank=True)
    @depend_on("owned__unit")
    @depend_on("leased_rented__unit")
    @depend_on("used_otherwise__unit")
    def unit(self):
        units = set()
        if self.owned:
            units.add(self.owned.unit.lower().strip())
        if self.leased_rented:
            units.add(self.leased_rented.unit.lower().strip())
        if self.used_otherwise:
            units.add(self.used_otherwise.unit.lower().strip())
        return ",".join(sorted(units))

    @denormalized(
        models.ForeignKey,
        "Assessment",
        related_name="+",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    @depend_on("producingorganizationdetails1")
    @depend_on("producingorganizationdetails2")
    def _assessment(self):
        pod_names = ["producingorganizationdetails1", "producingorganizationdetails2"]
        pod = None
        for name in pod_names:
            try:
                pod = getattr(self, name)
            except ProducingOrganizationDetails.DoesNotExist:
                pass
        if pod:
            return pod._assessment
        else:
            return None

    class Meta:
        ordering = ("pk",)


class LandUseForestry(BaseModel):
    area = models.DecimalField(max_digits=10, decimal_places=2)
    unit = models.TextField()
    denorm_always_skip = ("modified_at",)

    @denormalized(
        models.ForeignKey,
        "Assessment",
        related_name="+",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    @depend_on("totallanduse1")
    @depend_on("totallanduse2")
    @depend_on("totallanduse3")
    @depend_on("totallanduse4")
    @depend_on("totallanduse5")
    @depend_on("totallanduse6")
    @depend_on("totallanduse7")
    def _assessment(self):
        totallanduse_names = [
            "totallanduse1",
            "totallanduse2",
            "totallanduse3",
            "totallanduse4",
            "totallanduse5",
            "totallanduse6",
            "totallanduse7",
        ]
        totallanduse = None
        for name in totallanduse_names:
            try:
                totallanduse = getattr(self, name)
            except TotalLandUseForestry.DoesNotExist:
                pass
        if totallanduse:
            return totallanduse._assessment
        else:
            return None

    class Meta:
        ordering = ("pk",)


class TotalLandUseForestry(BaseModel):
    forestry = models.OneToOneField(
        LandUseForestry,
        related_name="totallanduse1",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    agriculture = models.OneToOneField(
        LandUseForestry,
        related_name="totallanduse2",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    infrastructure = models.OneToOneField(
        LandUseForestry,
        related_name="totallanduse3",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    cultural = models.OneToOneField(
        LandUseForestry,
        related_name="totallanduse4",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    water = models.OneToOneField(
        LandUseForestry,
        related_name="totallanduse5",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    other = models.OneToOneField(
        LandUseForestry,
        related_name="totallanduse6",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    not_used = models.OneToOneField(
        LandUseForestry,
        related_name="totallanduse7",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    denorm_always_skip = ("modified_at",)

    @denormalized(
        models.DecimalField, max_digits=10, decimal_places=2, blank=True, null=True
    )
    @depend_on("forestry__area")
    @depend_on("agriculture__area")
    @depend_on("infrastructure__area")
    @depend_on("cultural__area")
    @depend_on("water__area")
    @depend_on("other__area")
    def total_used_area(self):
        return sum(
            [
                Decimal(self.forestry.area) if self.forestry else Decimal(0),
                (Decimal(self.agriculture.area) if self.agriculture else Decimal(0)),
                (
                    Decimal(self.infrastructure.area)
                    if self.infrastructure
                    else Decimal(0)
                ),
                Decimal(self.cultural.area) if self.cultural else Decimal(0),
                Decimal(self.water.area) if self.water else Decimal(0),
                Decimal(self.other.area) if self.other else Decimal(0),
            ]
        )

    @denormalized(models.TextField, blank=True)
    @depend_on("forestry__unit")
    @depend_on("agriculture__unit")
    @depend_on("infrastructure__unit")
    @depend_on("cultural__unit")
    @depend_on("water__unit")
    @depend_on("other__unit")
    def unit(self):
        units = set()
        if self.forestry:
            units.add(self.forestry.unit.lower().strip())
        if self.agriculture:
            units.add(self.agriculture.unit.lower().strip())
        if self.infrastructure:
            units.add(self.infrastructure.unit.lower().strip())
        if self.cultural:
            units.add(self.cultural.unit.lower().strip())
        if self.water:
            units.add(self.water.unit.lower().strip())
        if self.other:
            units.add(self.other.unit.lower().strip())
        return ",".join(sorted(units))

    @denormalized(
        models.ForeignKey,
        "Assessment",
        related_name="+",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    @depend_on("producingorganizationdetails___assessment_id")
    def _assessment(self):
        pod_names = ["producingorganizationdetails"]
        pod = None
        for name in pod_names:
            try:
                pod = getattr(self, name)
            except ProducingOrganizationDetails.DoesNotExist:
                pass
        if pod:
            return pod._assessment
        else:
            return None

    class Meta:
        ordering = ("pk",)


class ProducingOrganizationDetails(BaseModel):
    number_of_female_executives = models.PositiveIntegerField(blank=True, null=True)
    number_of_male_executives = models.PositiveIntegerField(blank=True, null=True)
    percent_of_executives_under_30 = models.PositiveIntegerField(blank=True, null=True)
    number_of_female_non_executives = models.PositiveIntegerField(blank=True, null=True)
    number_of_male_non_executives = models.PositiveIntegerField(blank=True, null=True)
    percent_of_non_executives_under_30 = models.PositiveIntegerField(
        blank=True, null=True
    )
    number_of_female_members = models.PositiveIntegerField(blank=True, null=True)
    number_of_male_members = models.PositiveIntegerField(blank=True, null=True)
    percent_of_members_under_30 = models.PositiveIntegerField(blank=True, null=True)
    number_of_female_active_members = models.PositiveIntegerField(blank=True, null=True)
    number_of_male_active_members = models.PositiveIntegerField(blank=True, null=True)
    percent_of_active_members_under_30 = models.PositiveIntegerField(
        blank=True, null=True
    )
    number_of_female_full_time_employees = models.PositiveIntegerField(
        blank=True, null=True
    )
    number_of_male_full_time_employees = models.PositiveIntegerField(
        blank=True, null=True
    )
    percent_of_full_time_employees_under_30 = models.PositiveIntegerField(
        blank=True, null=True
    )
    number_of_female_part_time_employees = models.PositiveIntegerField(
        blank=True, null=True
    )
    number_of_male_part_time_employees = models.PositiveIntegerField(
        blank=True, null=True
    )
    percent_of_part_time_employees_under_30 = models.PositiveIntegerField(
        blank=True, null=True
    )
    number_of_female_seasonal_employees = models.PositiveIntegerField(
        blank=True, null=True
    )
    number_of_male_seasonal_employees = models.PositiveIntegerField(
        blank=True, null=True
    )
    percent_of_seasonal_employees_under_30 = models.PositiveIntegerField(
        blank=True, null=True
    )
    number_of_female_sharecroppers = models.PositiveIntegerField(blank=True, null=True)
    number_of_male_sharecroppers = models.PositiveIntegerField(blank=True, null=True)
    percent_of_sharecroppers_under_30 = models.PositiveIntegerField(
        blank=True, null=True
    )
    number_of_female_active_sharecroppers = models.PositiveIntegerField(
        blank=True, null=True
    )
    number_of_male_active_sharecroppers = models.PositiveIntegerField(
        blank=True, null=True
    )
    percent_of_active_sharecroppers_under_30 = models.PositiveIntegerField(
        blank=True, null=True
    )
    number_of_female_outgrowers = models.PositiveIntegerField(blank=True, null=True)
    number_of_male_outgrowers = models.PositiveIntegerField(blank=True, null=True)
    percent_of_outgrowers_under_30 = models.PositiveIntegerField(blank=True, null=True)
    number_of_female_active_outgrowers = models.PositiveIntegerField(
        blank=True, null=True
    )
    number_of_male_active_outgrowers = models.PositiveIntegerField(
        blank=True, null=True
    )
    percent_of_active_outgrowers_under_30 = models.PositiveIntegerField(
        blank=True, null=True
    )
    apex_organization = models.TextField(blank=True)
    number_of_member_cooperatives = models.PositiveIntegerField(blank=True, null=True)
    number_of_member_unions = models.PositiveIntegerField(blank=True, null=True)
    access_roads = models.TextField(blank=True)
    distance_to_hub = models.TextField(blank=True)
    public_transportation = models.TextField(blank=True)
    power_electricity = models.TextField(blank=True)
    internet_access = models.TextField(blank=True)
    mobile_network_coverage = models.TextField(blank=True)
    running_water = models.TextField(blank=True)
    demonstration_plot = models.TextField(blank=True)
    training_space = models.TextField(blank=True)
    warehousing = models.TextField(blank=True)
    services = models.ManyToManyField(ServiceOption, related_name="+", blank=True)
    mission = models.TextField(blank=True)
    vision = models.TextField(blank=True)
    cooperative = models.TextField(blank=True)
    union = models.TextField(blank=True)
    federation = models.TextField(blank=True)
    sacco = models.TextField(blank=True)
    other_representing_organizations = models.TextField(blank=True)
    land_used_by_producing_organization = models.OneToOneField(
        TotalLandUse,
        related_name="producingorganizationdetails1",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    land_used_by_members_outgrowers = models.OneToOneField(
        TotalLandUse,
        related_name="producingorganizationdetails2",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    land_used_for_forestry = models.OneToOneField(
        TotalLandUseForestry,
        related_name="producingorganizationdetails",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    number_of_male_farmers_under_supervision = models.PositiveSmallIntegerField(
        blank=True, null=True
    )
    number_of_female_farmers_under_supervision = models.PositiveSmallIntegerField(
        blank=True, null=True
    )
    number_of_potential_male_farmers = models.PositiveSmallIntegerField(
        blank=True, null=True
    )
    number_of_potential_female_farmers = models.PositiveSmallIntegerField(
        blank=True, null=True
    )
    personal_transportation = models.TextField(blank=True)
    computer_access = models.TextField(blank=True)
    comment_number_of_non_executives = models.TextField(blank=True)
    comment_number_of_executives = models.TextField(blank=True)
    comment_number_of_full_time_employees = models.TextField(blank=True)
    comment_number_of_part_time_employees = models.TextField(blank=True)
    comment_number_of_seasonal_employees = models.TextField(blank=True)
    comment_number_of_members = models.TextField(blank=True)
    comment_number_of_active_members = models.TextField(blank=True)
    comment_number_of_outgrowers = models.TextField(blank=True)
    comment_number_of_active_outgrowers = models.TextField(blank=True)
    comment_number_of_member_cooperatives = models.TextField(blank=True)
    comment_number_of_member_unions = models.TextField(blank=True)

    denorm_always_skip = ("modified_at",)

    @denormalized(models.PositiveIntegerField, blank=True, null=True)
    def number_of_active_outgrowers(self):
        empty = (
            self.number_of_female_active_outgrowers is None
            and self.number_of_male_active_outgrowers is None
        )
        if empty:
            return None
        return (self.number_of_female_active_outgrowers or 0) + (
            self.number_of_male_active_outgrowers or 0
        )

    @denormalized(models.PositiveIntegerField, blank=True, null=True)
    def number_of_outgrowers(self):
        empty = (
            self.number_of_female_outgrowers is None
            and self.number_of_male_outgrowers is None
        )
        if empty:
            return None
        return (self.number_of_female_outgrowers or 0) + (
            self.number_of_male_outgrowers or 0
        )

    @denormalized(models.PositiveIntegerField, blank=True, null=True)
    def number_of_active_sharecroppers(self):
        empty = (
            self.number_of_female_active_sharecroppers is None
            and self.number_of_male_active_sharecroppers is None
        )
        if empty:
            return None
        return (self.number_of_female_active_sharecroppers or 0) + (
            self.number_of_male_active_sharecroppers or 0
        )

    @denormalized(models.PositiveIntegerField, blank=True, null=True)
    def number_of_sharecroppers(self):
        empty = (
            self.number_of_female_sharecroppers is None
            and self.number_of_male_sharecroppers is None
        )
        if empty:
            return None
        return (self.number_of_female_sharecroppers or 0) + (
            self.number_of_male_sharecroppers or 0
        )

    @denormalized(models.PositiveIntegerField, blank=True, null=True)
    def number_of_seasonal_employees(self):
        empty = (
            self.number_of_female_seasonal_employees is None
            and self.number_of_male_seasonal_employees is None
        )
        if empty:
            return None
        return (self.number_of_female_seasonal_employees or 0) + (
            self.number_of_male_seasonal_employees or 0
        )

    @denormalized(models.PositiveIntegerField, blank=True, null=True)
    def number_of_part_time_employees(self):
        empty = (
            self.number_of_female_part_time_employees is None
            and self.number_of_male_part_time_employees is None
        )
        if empty:
            return None
        return (self.number_of_female_part_time_employees or 0) + (
            self.number_of_male_part_time_employees or 0
        )

    @denormalized(models.PositiveIntegerField, blank=True, null=True)
    def number_of_full_time_employees(self):
        empty = (
            self.number_of_female_full_time_employees is None
            and self.number_of_male_full_time_employees is None
        )
        if empty:
            return None
        return (self.number_of_female_full_time_employees or 0) + (
            self.number_of_male_full_time_employees or 0
        )

    @denormalized(models.PositiveIntegerField, blank=True, null=True)
    def number_of_active_members(self):
        empty = (
            self.number_of_female_active_members is None
            and self.number_of_male_active_members is None
        )
        if empty:
            return None
        return (self.number_of_female_active_members or 0) + (
            self.number_of_male_active_members or 0
        )

    @denormalized(models.PositiveIntegerField, blank=True, null=True)
    def number_of_members(self):
        empty = (
            self.number_of_female_members is None
            and self.number_of_male_members is None
        )
        if empty:
            return None
        return (self.number_of_female_members or 0) + (self.number_of_male_members or 0)

    @denormalized(models.PositiveIntegerField, blank=True, null=True)
    def number_of_non_executives(self):
        empty = (
            self.number_of_female_non_executives is None
            and self.number_of_male_non_executives is None
        )
        if empty:
            return None
        return (self.number_of_female_non_executives or 0) + (
            self.number_of_male_non_executives or 0
        )

    @denormalized(models.PositiveIntegerField, blank=True, null=True)
    def number_of_executives(self):
        empty = (
            self.number_of_female_executives is None
            and self.number_of_male_executives is None
        )
        if empty:
            return None
        return (self.number_of_female_executives or 0) + (
            self.number_of_male_executives or 0
        )

    @denormalized(
        models.ForeignKey,
        "Assessment",
        related_name="+",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    @depend_on("assessment__producing_organization_details_id")
    def _assessment(self):
        try:
            assessment = self.assessment
        except Assessment.DoesNotExist:
            assessment = None
        return assessment

    class Meta:
        ordering = ("pk",)


class AssessmentPurpose(BaseType):
    pass


class ForestryPlan(BaseModel):
    objective_and_strategy = models.TextField(blank=True)
    non_timber_products = models.TextField(blank=True)
    environmental_services = models.TextField(blank=True)
    commercial_species = models.TextField(blank=True)
    denorm_always_skip = ("modified_at",)

    @denormalized(
        models.ForeignKey,
        "Assessment",
        related_name="+",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    @depend_on("assessment1__id")
    @depend_on("assessment2__id")
    def _assessment(self):
        assessment_names = ["assessment1", "assessment2"]
        assessment = None
        for name in assessment_names:
            try:
                assessment = getattr(self, name)
            except Assessment.DoesNotExist:
                pass
        if assessment:
            return assessment
        else:
            return None

    class Meta:
        ordering = ("pk",)


class AssessmentManager(models.Manager):
    def get_queryset(self):
        # Only return non-deleted assessments by default
        return super().get_queryset().filter(is_deleted=False)

    def deleted(self):
        # Optional: method to access deleted assessments if needed
        return super().get_queryset().filter(is_deleted=True)

    def all_objects(self):
        # Optional: method to get all assessments regardless of deletion status
        return super().get_queryset()


class Assessment(BaseModel):
    objects = AssessmentManager()
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    tool = models.ForeignKey(Tool, related_name="assessments", on_delete=models.PROTECT)
    producing_organization = models.ForeignKey(
        ProducingOrganization, related_name="assessments", on_delete=models.PROTECT
    )
    rapid_finalized = models.BooleanField(default=False)
    project = models.ForeignKey(
        "projects.Project",
        related_name="assessments",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    quality_reviewer = models.ForeignKey(
        User,
        blank=True,
        null=True,
        related_name="quality_reviewed_assessments",
        on_delete=models.SET_NULL,
    )
    financial_quality_reviewer = models.ForeignKey(
        User,
        blank=True,
        null=True,
        related_name="financial_quality_reviewed_assessments",
        on_delete=models.SET_NULL,
    )
    date = models.DateField()  # real world date
    second_party = models.BooleanField(
        # Used to signal potential conflict of interest
        default=False
    )
    end_of_bookyear = models.PositiveSmallIntegerField(
        choices=enumerate(calendar.month_name[1:], 1), blank=True, null=True
    )
    producing_organization_details = models.OneToOneField(
        ProducingOrganizationDetails, blank=True, null=True, on_delete=models.PROTECT
    )
    present_representatives = models.TextField(blank=True)
    observations = models.TextField(blank=True)
    purposes = models.ManyToManyField(
        AssessmentPurpose, related_name="_assessments", blank=True
    )
    forest_management_plan = models.OneToOneField(
        ForestryPlan,
        related_name="assessment1",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    forest_annual_plan = models.OneToOneField(
        ForestryPlan,
        related_name="assessment2",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    auditing_firm = models.TextField(blank=True)
    currency = models.CharField(
        max_length=max(len(item[0]) for item in CURRENCY_CHOICES),
        choices=CURRENCY_CHOICES,
        blank=True,
    )
    denorm_always_skip = ("modified_at",)
    language = models.CharField(max_length=2, choices=settings.LANGUAGES, default="en")
    other_activities = models.TextField(blank=True)
    accountant_comments_profitloss = models.TextField(blank=True)
    accountant_comments_cashflow = models.TextField(blank=True)
    accountant_comments_balancesheet = models.TextField(blank=True)
    assessor_comments_assessment = models.TextField(blank=True)
    assessor_comments_organizational = models.TextField(blank=True)
    assessor_comments_value_chain = models.TextField(blank=True)
    assessor_comments_finance = models.TextField(blank=True)
    assessor_comments_production = models.TextField(blank=True)
    assessor_comments_finance_product = models.TextField(blank=True)
    assessor_comments_observations = models.TextField(blank=True)
    assessor_comments_documents = models.TextField(blank=True)
    assessor_comments_governance = models.TextField(blank=True)
    assessor_comments_terms_and_conditions = models.TextField(blank=True)
    assessor_comments_monthly_production = models.TextField(blank=True)
    assessor_comments_finance_overview = models.TextField(blank=True)
    draft_report_notification_sent = models.BooleanField(default=False)
    second_draft_report_notification_sent = models.BooleanField(default=False)
    draft_report_notification_dashboard_sent = models.BooleanField(default=False)
    second_draft_report_notification_dashboard_sent = models.BooleanField(default=False)
    projections_year = models.PositiveSmallIntegerField(blank=True, null=True)
    start_date = models.DateField(blank=True, null=True)  # scope wish date
    assessment_tab_accepted = models.BooleanField(default=True)
    organizational_tab_accepted = models.BooleanField(default=True)
    agent_tab_accepted = models.BooleanField(default=True)
    value_chain_tab_accepted = models.BooleanField(default=True)
    finance_history_tab_accepted = models.BooleanField(default=True)
    production_tab_accepted = models.BooleanField(default=True)
    finance_product_tab_accepted = models.BooleanField(default=True)
    observations_tab_accepted = models.BooleanField(default=True)
    documents_tab_accepted = models.BooleanField(default=True)
    data_sharing_consent_tab_accepted = models.BooleanField(default=True)
    finance_overview_tab_accepted = models.BooleanField(default=True)
    products_no_information_available = models.BooleanField(default=False)
    products_completed = models.BooleanField(default=False)
    agent_income_completed = models.BooleanField(default=False)
    agent_income_no_information_available = models.BooleanField(default=False)
    agent_produce_sold_completed = models.BooleanField(default=False)
    agent_produce_sold_no_information_available = models.BooleanField(default=False)
    balance_sheet_tab_accepted = models.BooleanField(default=True)
    bank_accounts_completed = models.BooleanField(default=False)
    bank_accounts_no_information_available = models.BooleanField(default=False)
    basic_financial_infos_completed = models.BooleanField(default=False)
    basic_financial_infos_no_information_available = models.BooleanField(default=False)
    basic_profit_loss_completed = models.BooleanField(default=False)
    basic_profit_loss_no_information_available = models.BooleanField(default=False)
    capital_requirements_completed = models.BooleanField(default=False)
    capital_requirements_no_information_available = models.BooleanField(default=False)
    cash_flow_tab_accepted = models.BooleanField(default=True)
    collateral_assets_completed = models.BooleanField(default=False)
    collateral_assets_no_information_available = models.BooleanField(default=False)
    enabling_players_completed = models.BooleanField(default=False)
    enabling_players_no_information_available = models.BooleanField(default=False)
    executive_set_completed = models.BooleanField(default=False)
    executive_set_no_information_available = models.BooleanField(default=False)
    finance_performance_tab_accepted = models.BooleanField(default=True)
    governance_structure_completed = models.BooleanField(default=False)
    governance_structure_no_information_available = models.BooleanField(default=False)
    financial_strategy_completed = models.BooleanField(default=False)
    financial_strategy_no_information_available = models.BooleanField(default=False)
    grant_histories_completed = models.BooleanField(default=False)
    grant_histories_no_information_available = models.BooleanField(default=False)
    insurances_completed = models.BooleanField(default=False)
    insurances_no_information_available = models.BooleanField(default=False)
    loan_applications_completed = models.BooleanField(default=False)
    loan_applications_no_information_available = models.BooleanField(default=False)
    loan_histories_completed = models.BooleanField(default=False)
    loan_histories_no_information_available = models.BooleanField(default=False)
    pre_finance_histories_completed = models.BooleanField(default=False)
    pre_finance_histories_no_information_available = models.BooleanField(default=False)
    loan_requirements_completed = models.BooleanField(default=False)
    loan_requirements_no_information_available = models.BooleanField(default=False)
    nonexecutive_set_completed = models.BooleanField(default=False)
    nonexecutive_set_no_information_available = models.BooleanField(default=False)
    productionmargins_completed = models.BooleanField(default=False)
    productionmargins_no_information_available = models.BooleanField(default=False)
    profit_loss_tab_accepted = models.BooleanField(default=True)
    shareholders_completed = models.BooleanField(default=False)
    shareholders_no_information_available = models.BooleanField(default=False)
    monthlyincomeprojection_set_completed = models.BooleanField(default=False)
    monthlyincomeprojection_set_no_information_available = models.BooleanField(
        default=False
    )
    monthlyexpensesprojection_set_completed = models.BooleanField(default=False)
    monthlyexpensesprojection_set_no_information_available = models.BooleanField(
        default=False
    )
    value_chain_players_completed = models.BooleanField(default=False)
    value_chain_players_no_information_available = models.BooleanField(default=False)
    assessment_tab_completed = models.BooleanField(default=False)
    organizational_tab_completed = models.BooleanField(default=False)
    agent_tab_completed = models.BooleanField(default=False)
    value_chain_tab_completed = models.BooleanField(default=False)
    finance_history_tab_completed = models.BooleanField(default=False)
    production_tab_completed = models.BooleanField(default=False)
    finance_product_tab_completed = models.BooleanField(default=False)
    profit_loss_tab_completed = models.BooleanField(default=False)
    balance_sheet_tab_completed = models.BooleanField(default=False)
    cash_flow_tab_completed = models.BooleanField(default=False)
    finance_performance_tab_completed = models.BooleanField(default=False)
    observations_tab_completed = models.BooleanField(default=False)
    documents_tab_completed = models.BooleanField(default=False)
    data_sharing_consent_tab_completed = models.BooleanField(default=False)
    build_response_tree_completed = models.BooleanField(default=False)
    finance_overview_tab_completed = models.BooleanField(default=False)
    report_revision_reason = models.TextField(blank=True)
    is_invalidated = models.BooleanField(default=False)
    invalidation_reason = models.TextField(blank=True)
    financial_strategy_no_info_reason = models.TextField(blank=True)
    basic_profit_loss_no_info_reason = models.TextField(blank=True)
    basic_financial_infos_no_info_reason = models.TextField(blank=True)
    bank_accounts_no_info_reason = models.TextField(blank=True)
    capital_requirements_no_info_reason = models.TextField(blank=True)
    collateral_assets_no_info_reason = models.TextField(blank=True)
    loan_requirements_no_info_reason = models.TextField(blank=True)
    loan_applications_no_info_reason = models.TextField(blank=True)
    loan_histories_no_info_reason = models.TextField(blank=True)
    pre_finance_histories_no_info_reason = models.TextField(blank=True)
    grant_histories_no_info_reason = models.TextField(blank=True)
    insurances_no_info_reason = models.TextField(blank=True)
    shareholders_no_info_reason = models.TextField(blank=True)
    counter_QR_to_assessor = models.IntegerField(default=0)
    is_deleted = models.BooleanField(default=False)

    class Meta:
        ordering = ("-date",)

    STATUS_CREATED = 0
    STATUS_IN_PROGRESS = 1
    STATUS_QC_QUALITY_REVIEW = 2
    STATUS_QC_ASSESSOR = 3
    STATUS_HAS_DRAFT = 4
    STATUS_HAS_FINAL = 5
    STATUS_CHOICES = (
        (STATUS_CREATED, _("Created")),
        (STATUS_IN_PROGRESS, _("In progress")),
        (STATUS_QC_QUALITY_REVIEW, _("QC Quality Review")),
        (STATUS_QC_ASSESSOR, _("QC Assessor")),
        (STATUS_HAS_DRAFT, _("Draft")),
        (STATUS_HAS_FINAL, _("Final")),
    )

    def get_assessments_from_same_organization(self):
        return Assessment.objects.filter(
            producing_organization__customer_id__in=self.producing_organization.customer.get_all_related_customer_ids()
        )

    @denormalized(
        models.ForeignKey,
        "Product",
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    @depend_on("productionsales__price")
    def primary_commodity(self):
        max_year = self.productionsales.filter(price__isnull=False).aggregate(
            max_year=Max("year")
        )["max_year"]
        max_price = 0
        max_priced_sale = None
        for sale in self.productionsales.filter(price__isnull=False, year=max_year):
            price = ConversionRatio.convert(
                sale.price.amount, sale.price.currency, "USD", self.canonical_date
            )
            if price > max_price:
                max_price = price
                max_priced_sale = sale
        if max_priced_sale:
            return max_priced_sale.product
        return None

    @denormalized(models.PositiveSmallIntegerField, default=0)
    @depend_on("producing_organization__assessments_count")
    def reassessment_counter(self):
        if self.id is None:
            return 0
        dates_and_ids = sorted(
            [
                (assessment.canonical_date, assessment.id)
                for assessment in self.get_assessments_from_same_organization()
            ]
        )
        try:
            return [id for (_, id) in dates_and_ids].index(self.id)
        except ValueError:
            return 0

    @denormalized(models.BooleanField, default=False)
    @depend_on("producing_organization__assessments_count")
    def is_latest(self):
        try:
            max_counter = (
                self.get_assessments_from_same_organization()
                .order_by("-reassessment_counter")[0]
                .reassessment_counter
            )
        except IndexError:
            max_counter = 0
        return self.reassessment_counter == max_counter

    @property
    def canonical_date(self):
        finalized_reports = self.reports.filter(
            status=Report.STATUS_FINAL, finalized_at__isnull=False
        )
        try:
            date = finalized_reports.order_by("-finalized_at")[0].finalized_at
        except IndexError:
            date = self.date
        return date

    @denormalized(models.BooleanField, default=False)
    @depend_on("reports__in_edit")
    def in_edit(self):
        return self.reports.filter(in_edit=True).exists()

    @denormalized(models.DateField, null=True, default=None)
    @depend_on("rapid_finalized")
    def rapid_finalization_date(self):
        if self.rapid_finalized and self.rapid_finalization_date == None:
            return datetime.date.today()
        return None

    @denormalized(
        models.PositiveSmallIntegerField, default=STATUS_CREATED, choices=STATUS_CHOICES
    )
    @depend_on("assessmentassignments__status")
    @depend_on("rapid_finalized")
    def status(self):
        if self.rapid_finalized:
            return Assessment.STATUS_HAS_FINAL

        value = (
            self.assessmentassignments.aggregate(min_status=Min("status"))["min_status"]
        ) or Assessment.STATUS_CREATED
        # create assessment log for changed value
        if value != self.status:
            from .utils import STATUS_INT_TO_STR

            old_value = STATUS_INT_TO_STR[self.status]
            new_value = STATUS_INT_TO_STR[value]
            target_model = "{}.{}".format(self._meta.app_label, self._meta.model_name)
            AssessmentLog.objects.create(
                assessment=self,
                action="update",
                target_model=target_model,
                target_id=self.id,
                changed_field="status",
                previous_data=old_value,
                updated_data=new_value,
            )
        return value

    @denormalized(models.BooleanField, default=False)
    @depend_on("assessmentassignments__submitted_at_least_once")
    def submitted_at_least_once(self):
        return any(
            [
                assignment.submitted_at_least_once
                for assignment in self.assessmentassignments.all()
            ]
        )

    @property
    def submitted_first(self):
        if not (self.assessmentassignments.exists()):
            return None
        else:
            return self.assessmentassignments.all()[0].submitted_first

    @denormalized(
        models.CharField,
        max_length=max(len(item[0]) for item in Report.STATUS_CHOICES),
        choices=Report.STATUS_CHOICES,
        default=Report.STATUS_DRAFT,
    )
    @depend_on("reports__status")
    def report_status(self):
        return (self.reports.aggregate(max_status=Max("status"))["max_status"]) or ""

    @denormalized(models.DateField, default=None, null=True)
    @depend_on("status")
    def qc_review_deadline(self):
        if self.status == Assessment.STATUS_QC_QUALITY_REVIEW:
            return datetime.datetime.now().date() + datetime.timedelta(days=3)
        elif self.status > Assessment.STATUS_QC_QUALITY_REVIEW:
            return self.qc_review_deadline if self.qc_review_deadline else None
        else:
            return None

    def can_make_reports(self, user):
        """User can make reports"""
        if user.is_employee:
            return True
        if user == self.quality_reviewer or user == self.display_quality_reviewer:
            return True
        elif (
            user == self.financial_quality_reviewer
            or user == self.financial_quality_reviewer
        ):
            return True
        else:
            return False

    def locked_from_app(self, user, assignment_type):
        try:
            assignment = self.assessmentassignments.get(
                assessor__user=user, assigned_as=assignment_type
            )
        except AssessmentAssignment.DoesNotExist:
            return True
        else:
            return assignment.locked_for_assessor

    def locked_from_dashboard(self, user):
        if not (self.assessmentassignments.exists()):
            return False
        if user.is_employee:
            return False
        if user == self.display_quality_reviewer:
            try:
                assignment = self.assessmentassignments.get(
                    assigned_as=AssessmentAssignment.AS_ASSESSOR
                )
            except AssessmentAssignment.DoesNotExist:
                return False
            else:
                return assignment.locked_for_employee
        elif user == self.display_financial_quality_reviewer:
            try:
                assignment = self.assessmentassignments.get(
                    assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST
                )
            except AssessmentAssignment.DoesNotExist:
                return False
            else:
                return assignment.locked_for_employee
        else:
            return not (
                self.assessmentassignments.filter(locked_for_employee=False).exists()
            )

    @classmethod
    def send_quality_control_notification_deadline(
        cls, assessment, user, axr_title, ax_type, is_late=False
    ):
        # Notify Quality Reviewer that the deadline is coming up or missed

        if is_late:
            slug = "quality_control_late_notification"
            template = "assessments/email/quality_control_deadline_missed.txt"
            subject_template = (
                "assessments/email/quality_control_deadline_missed_subject.txt"
            )
        else:
            slug = "quality_control_warning_notification"
            template = "assessments/email/quality_control_deadline_warning.txt"
            subject_template = (
                "assessments/email/quality_control_deadline_warning_subject.txt"
            )

        MessageType.send_message(
            from_user=None,
            to_user=user,
            slug=slug,
            template=template,
            subject_template=subject_template,
            context={
                "to_user": user,
                "assessment": assessment,
                "axr_title": axr_title,
                "ax_type": ax_type,
            },
            language=user.language,
        )

    @classmethod
    def send_self_assessor_late_email(
        cls, assignment, user, axr_title, ax_type,
    ):
        # Notify Quality Reviewer that the deadline is coming up or missed

        slug = "self_assessor_late_notification"
        template = "assessments/email/self_assessor_late.txt"
        subject_template = (
            "assessments/email/self_assessor_late_subject.txt"
        )

        MessageType.send_message(
            from_user=None,
            to_user=user,
            slug=slug,
            template=template,
            subject_template=subject_template,
            context={
                "to_user": user,
                "assignment": assignment,
                "axr_title": axr_title,
                "ax_type": ax_type,
            },
            language=user.language,
        )

    @classmethod
    def customer_export(cls, queryset):
        """
        Export data into tablib dataset intended for consumption by SCOPE
        customers
        All assessments in queryset must have same tool

        Assessee (assessee name)
        Assessee ID (assessee id)
        City (assessee city)
        Country (assessee country)
        Scheduled date (scheduled date of audit)
        Audit date (actual date of audit)
        Assessor (full name)
        Financial specialist (full name)
        Tool (tool type name)
        Tool version (major, minor, revision)
        Language
        Status
        Total score
        Chapters and sections (1 column per chapter, then 1 column for each
        section within that chapter, etc)
        """
        queryset = queryset.exclude(project__customer__pk=1963)
        chapter_and_section_positions_and_names = (
            SectionResponse.objects.filter(_assessment__in=queryset)
            .order_by()
            .values_list("section_display_position", "section_title")
            .distinct()
        )
        chapter_and_section_positions_and_names = sorted(
            set(
                (position, name.lower())
                for (position, name) in chapter_and_section_positions_and_names
            )
        )
        section_response_filter_object = reduce(
            operator.or_,
            (
                Q(section_title__iexact=name, section_display_position=position)
                for (position, name) in chapter_and_section_positions_and_names
            ),
            Q(),
        )
        headers = [
            "Assessee",
            "Assessee ID",
            "City",
            "Country",
            "Scheduled date",
            "Audit date",
            "Assessor",
            "Financial specialist",
            "Tool",
            "Tool version",
            "Language",
            "Status",
            "Total score",
        ] + [
            "{} {}".format(position, name)
            for (position, name) in chapter_and_section_positions_and_names
        ]
        data = []
        queryset = (
            queryset.order_by("pk")
            .select_related(
                "tool",
                "tool__type",
                "producing_organization",
                "producing_organization__customer",
            )
            .prefetch_related(
                Prefetch(
                    "assessmentassignments",
                    queryset=(
                        AssessmentAssignment.objects.exclude(
                            assessment__project__customer__pk=1963
                        )
                        .filter(assigned_as=AssessmentAssignment.AS_ASSESSOR)
                        .select_related("assessor", "assessor__user")
                    ),
                    to_attr="assessmentassignments_a",
                ),
                Prefetch(
                    "assessmentassignments",
                    queryset=(
                        AssessmentAssignment.objects.exclude(
                            assessment__project__customer__pk=1963
                        )
                        .filter(
                            assigned_as=(AssessmentAssignment.AS_FINANCIAL_SPECIALIST)
                        )
                        .select_related("assessor", "assessor__user")
                    ),
                    to_attr="assessmentassignments_fp",
                ),
                Prefetch(
                    "all_section_responses",
                    queryset=(
                        SectionResponse.objects.filter(section_response_filter_object)
                    ),
                    to_attr="section_responses_for_selected_chapters",
                ),
            )
        )
        for assessment in queryset:
            if assessment.assessmentassignments_fp:
                financial_specialist = assessment.assessmentassignments_fp[
                    0
                ].assessor.user.full_name
            else:
                financial_specialist = ""
            if assessment.assessmentassignments_a:
                assessor = assessment.assessmentassignments_a[0].assessor.user.full_name
            else:
                assessor = ""
            if assessment.status == Assessment.STATUS_HAS_FINAL:
                chapter_and_section_scores = assessment.scores_for_chapters(
                    chapter_and_section_positions_and_names
                )
                total_score = assessment.score
            else:
                chapter_and_section_scores = [
                    "-" for (position, name) in chapter_and_section_positions_and_names
                ]
                total_score = "-"
            data.append(
                [
                    assessment.producing_organization.customer.name,
                    assessment.producing_organization.customer.id,
                    assessment.producing_organization.city,
                    assessment.producing_organization.country,
                    assessment.date,
                    assessment.start_date,
                    assessor,
                    financial_specialist,
                    assessment.tool.type.name,
                    "{}.{}.{}".format(
                        assessment.tool.version_major,
                        assessment.tool.version_minor,
                        assessment.tool.version_revision,
                    ),
                    assessment.get_language_display(),
                    assessment.get_status_display(),
                    total_score,
                ]
                + chapter_and_section_scores
            )
        dataset = tablib.Dataset(*data, headers=headers)
        return dataset

    @denormalized(
        models.ForeignKey,
        User,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.PROTECT,
    )
    @depend_on("assessmentassignments__assessor_id")
    @depend_on("assessmentinvitations__assessor_id")
    def assessor_user(self):
        """
        Denormalized FK to assessor user, needed for sorting
        """
        assignment = (
            self.assessmentassignments.order_by("pk")
            .select_related("assessor__user")
            .first()
        )
        if assignment:
            return assignment.assessor.user
        invitation = (
            self.assessmentinvitations.exclude(status="declined")
            .order_by("pk")
            .select_related("assessor__user")
            .first()
        )
        if invitation:
            return invitation.assessor.user
        return None

    @denormalized(models.TextField, blank=True)
    @depend_on("assessor_user__full_name")
    @depend_on("assessor_user__full_name")
    def assessor_full_name(self):
        user = self.assessor_user
        if user:
            return user.full_name
        return ""

    def fake_earliest_year(self):
        years = sorted(self.balancesheets.values_list("year", flat=True), reverse=True)
        if 0 in years:
            return years[len(years) - 2] - 1

    @denormalized(
        models.ForeignKey,
        "NetMonthlyIncomeProjection",
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    @depend_on("netmonthlyincomeprojection__id")
    def _netmonthlyincomeprojection(self):
        try:
            return self.netmonthlyincomeprojection
        except NetMonthlyIncomeProjection.DoesNotExist:
            return None

    @denormalized(
        models.ForeignKey,
        "TotalMonthlyIncomeProjection",
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    @depend_on("totalmonthlyincomeprojection__id")
    def _totalmonthlyincomeprojection(self):
        try:
            return self.totalmonthlyincomeprojection
        except TotalMonthlyIncomeProjection.DoesNotExist:
            return None

    @denormalized(
        models.ForeignKey,
        "TotalMonthlyExpensesProjection",
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    @depend_on("totalmonthlyexpensesprojection__id")
    def _totalmonthlyexpensesprojection(self):
        try:
            return self.totalmonthlyexpensesprojection
        except TotalMonthlyExpensesProjection.DoesNotExist:
            return None

    @denormalized(models.BooleanField, default=True)
    @depend_on("tool__completion_check_required")
    def completion_check_required(self):
        return self.tool.completion_check_required

    @denormalized(models.BooleanField, default=True)
    @depend_on("tool__with_management_body")
    def with_management_body(self):
        return self.tool.with_management_body

    @denormalized(models.BooleanField, default=True)
    @depend_on("tool__with_margins_raw_materials")
    def with_margins_raw_materials(self):
        return self.tool.with_margins_raw_materials

    @denormalized(models.BooleanField, default=True)
    @depend_on("tool__with_general_checks")
    def with_general_checks(self):
        return self.tool.with_general_checks

    @denormalized(models.BooleanField, default=True)
    @depend_on("tool__full_completion_check_required")
    def full_completion_check_required(self):
        return self.tool.full_completion_check_required

    @denormalized(models.BooleanField, default=True)
    @depend_on("tool__with_terms_and_conditions")
    def with_terms_and_conditions(self):
        return self.tool.with_terms_and_conditions

    @denormalized(models.BooleanField, default=True)
    @depend_on("tool__is_new_basic_tool")
    def is_new_basic_tool(self):
        return self.tool.is_new_basic_tool

    @denormalized(models.BooleanField, default=True)
    @depend_on("with_no_info_reason")
    def with_no_info_reason(self):
        return self.tool.with_no_info_reason

    @denormalized(
        models.ForeignKey,
        User,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.PROTECT,
    )
    @depend_on("project__quality_reviewer_id")
    @depend_on("quality_reviewer_id")
    def display_quality_reviewer(self):
        if self.quality_reviewer is not None:
            return self.quality_reviewer
        elif self.project is not None and self.project.quality_reviewer is not None:
            return self.project.quality_reviewer
        else:
            return None

    @denormalized(
        models.ForeignKey,
        User,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.PROTECT,
    )
    @depend_on("project__financial_quality_reviewer_id")
    @depend_on("financial_quality_reviewer_id")
    def display_financial_quality_reviewer(self):
        if not self.tool.type.requires_accountant:
            return None
        if self.financial_quality_reviewer is not None:
            return self.financial_quality_reviewer
        elif (
            self.project is not None
            and self.project.financial_quality_reviewer is not None
        ):
            return self.project.financial_quality_reviewer
        else:
            return None

    @property
    def all_costs_of_sales(self):
        objects = CostOfSale.objects.filter(_assessment=self).order_by(
            "-profit_loss_statement__year"
        )
        names = sorted(set(obj.name for obj in objects))
        return OrderedDict(
            (
                name,
                OrderedDict(
                    (obj.profit_loss_statement.year, obj)
                    for obj in objects
                    if obj.name == name
                ),
            )
            for name in names
        )

    def _set_sales_expenses(self, value, model):
        """
        Common function to set sales and expenses
        """
        # Get years from db
        years = self.profitlossstatements.all().values_list("year", flat=True)
        # Get names from dict
        names = set(value.keys())
        # Get names from db
        db_names = set(
            model.objects.filter(_assessment=self).values_list("name", flat=True)
        )
        # Remove missing names
        missing_names = db_names - names
        if missing_names:
            model.objects.filter(_assessment=self, name__in=missing_names).delete()
        # Add new names
        new_names = names - db_names
        if new_names:
            for year in years:
                profit_loss_statement = self.profitlossstatements.get(year=year)
                for name in new_names:
                    try:
                        value_dict = list(
                            filter(lambda item: item.get("year") == year, value[name])
                        )[0]
                    except IndexError:
                        pass
                    else:
                        tmpvalue = value_dict["value"]
                        if tmpvalue == {}:
                            tmpvalue = None
                        if tmpvalue is not None:
                            if tmpvalue["amount"] is None:
                                tmpvalue = None
                            else:
                                tmpvalue = Money(**tmpvalue)
                        model.objects.create(
                            profit_loss_statement=profit_loss_statement,
                            name=name,
                            value=tmpvalue,
                        )
        # @todo: erwin, how about new years?

        # Modify existing names in place
        existing_names = names - new_names
        if existing_names:
            db_objects = (
                model.objects.filter(_assessment=self)
                .filter(name__in=existing_names)
                .select_related("profit_loss_statement")
            )
            for db_object in db_objects:
                try:
                    value_dict = list(
                        filter(
                            lambda item: item.get("year")
                            == db_object.profit_loss_statement.year,
                            value[db_object.name],
                        )
                    )[0]
                except IndexError:
                    pass
                else:
                    if value_dict["value"] is not None:
                        if value_dict["value"].get("amount") is None:
                            new_value = None
                        else:
                            if "currency" not in value_dict["value"]:
                                if db_object.value is not None:
                                    currency = db_object.value.currency.code
                                else:
                                    currency = self.currency or "USD"
                                value_dict["value"]["currency"] = currency
                            new_value = Money(**value_dict["value"])
                    else:
                        new_value = None
                    if db_object.value != new_value:
                        db_object.value = new_value
                        db_object.save()

    @all_costs_of_sales.setter
    def all_costs_of_sales(self, value):
        """
        Set costs of sales based on nested dictionaries
        """
        self._set_sales_expenses(value, CostOfSale)

    @property
    def all_expenses(self):
        objects = Expense.objects.filter(_assessment=self).order_by(
            "-profit_loss_statement__year"
        )
        names = sorted(set(obj.name for obj in objects))
        return OrderedDict(
            (
                name,
                OrderedDict(
                    (obj.profit_loss_statement.year, obj)
                    for obj in objects
                    if obj.name == name
                ),
            )
            for name in names
        )

    @all_expenses.setter
    def all_expenses(self, value):
        """
        Set expenses based on nested dictionaries
        """
        self._set_sales_expenses(value, Expense)

    @denormalized(models.TextField, blank=True)
    @depend_on("balancesheets__year")
    def _balancesheet_years(self):
        return ", ".join(
            [
                str(year)
                for year in (
                    self.balancesheets.order_by("year").values_list("year", flat=True)
                )
            ]
        )

    @denormalized(
        models.DecimalField, max_digits=4, decimal_places=3, blank=True, null=True
    )
    @depend_on("section_responses__score")
    @depend_on("section_responses__weight")
    def score(self):
        """
        Score of an assessment is the weighted average of the scores of sections
        """
        items = list(self.section_responses.all())
        items = list(filter(lambda item: item.score and item.weight, items))
        if items:
            total_score = sum([item.score * item.weight for item in items])
            total_weight = sum([item.weight for item in items])
            return total_score / total_weight
        else:
            return Decimal(0)

    @denormalized(
        models.DecimalField, max_digits=5, decimal_places=3, blank=True, null=True
    )
    #@depend_on("section_responses__responses__subresponses__selected_option__score")
    #@depend_on("section_responses__responses__subresponses__checked_options__id")
    @depend_on("section_responses__a2f_score")
    def a2f_score(self):
        items = list(self.section_responses.all())
        items = list(filter(lambda item: item.a2f_score is not None, items))
        total_sum = 0
        for section_response in self.section_responses.all():
            sub_sections = section_response.get_descendants()
            for sub_section in sub_sections:
                for response in sub_section.responses.all():
                    for subresponse in response.subresponses.all():
                        if subresponse.none_of_the_above == False and subresponse.not_relevant == False:
                            if subresponse.selected_option is not None:
                                total_sum += subresponse.selected_option.a2f_score
                            if subresponse.checked_options is not None:
                                for checked_option in subresponse.checked_options.all():
                                    total_sum += checked_option.a2f_score
        if items:
            a2f_score = 1 + max(((total_sum / 360) * 4), 0)
            average_score = round(a2f_score, 2)
            return Decimal(average_score)
        else:
            return Decimal(0)

    @denormalized(
        models.DecimalField, max_digits=10, decimal_places=3, blank=True, null=True
    )
    @depend_on("responses__a2f_score")
    def a2f_score_max(self):
        total_options_count = sum(
            subresponse.subquestion.options.count()
            for response in self.responses.all()
            for subresponse in response.subresponses.all()
        )
        return Decimal(total_options_count * 2) if total_options_count else Decimal(0)

    @denormalized(models.TextField, blank=True)
    @depend_on("tool__type_id")
    def tool_name(self):
        return self.tool.type.name

    @denormalized(models.PositiveIntegerField, blank=True, null=True)
    @depend_on("tool__version_major")
    def tool_version_major(self):
        return self.tool.version_major

    @denormalized(models.PositiveIntegerField, blank=True, null=True)
    @depend_on("tool__version_minor")
    def tool_version_minor(self):
        return self.tool.version_minor

    @denormalized(models.PositiveIntegerField, blank=True, null=True)
    @depend_on("tool__version_revision")
    def tool_version_revision(self):
        return self.tool.version_revision

    @denormalized(models.IntegerField, blank=True, null=True)
    @depend_on("assessmentinvitations__status")
    @depend_on("reports__status")
    @depend_on("assessmentassignments__locked_for_employee")
    def progress(self):
        """
        Registration        0%
        Downpayment         5%
        Allocation accepted 10%
        Axr submits to QC   60%
        Report generated    80%
        Report Finalization 100%

        @todo: trigger on report, payment and assessment invitation update!
        """
        from reports.models import Report

        if (
            Report.objects.filter(status=Report.STATUS_FINAL, assessment=self).count()
            > 0
        ):
            return 100
        elif Report.objects.filter(assessment=self).count() > 0:
            return 80
        elif self.assessmentassignments.filter(locked_for_employee=True).count() > 0:
            return 60
        elif (
            self.assessmentinvitations.filter(
                status=AssessmentInvitation.STATUS_ACCEPTED
            ).count()
            > 0
        ):
            return 10
        return 0

    @denormalized(models.BooleanField, default=False)
    @depend_on("responses__accepted")
    @depend_on("subresponses__accepted")
    def all_accepted(self):
        accepted_values = set(
            self.responses.all()
            .order_by()
            .values_list("accepted", flat=True)
            .distinct()
        ) | set(
            self.subresponses.all()
            .order_by()
            .values_list("accepted", flat=True)
            .distinct()
        )
        return accepted_values == set([True])

    @denormalized(models.BooleanField, default=False)
    @depend_on("responses__accepted")
    @depend_on("subresponses__accepted")
    def all_declined(self):
        accepted_values = set(
            self.responses.all()
            .order_by()
            .values_list("accepted", flat=True)
            .distinct()
        ) | set(
            self.subresponses.all()
            .order_by()
            .values_list("accepted", flat=True)
            .distinct()
        )
        return accepted_values == set([False])

    def connect_copy_score_and_weight_from_fks(self):
        """
        Connect all _copy_score_and_weight_from FKs for sections
        """
        all_sections = self.section_responses.all().get_descendants(include_self=True)
        dependent_sections = all_sections.filter(
            section__copy_score_and_weight_from__isnull=False
        )
        for dependent_section in dependent_sections:
            copy_section = all_sections.filter(
                section=dependent_section.section.copy_score_and_weight_from
            ).first()
            if copy_section:
                dependent_section._copy_score_and_weight_from = copy_section
                dependent_section.save()

    @property
    def _assessment(self):
        return self

    # FIXME: Accept assessment when all responses have been accepted

    def employees_involved_users(self):
        """
        Return user objects of scope employees involved in this assessment
        TODO: fix the name of this
        """
        if self.project:
            filter_object = Q(
                id__in=(
                    self.project.customer_members.all().values_list(
                        "user_id", flat=True
                    )
                )
            )
            if self.project.contact:
                filter_object |= Q(id=(self.project.contact.user_id))
            involved_users = User.objects.filter(filter_object)
        else:
            involved_users = User.objects.none()
        return involved_users

    @property
    def commissioned_by(self):
        if self.project and self.project.customer:
            return self.project.customer.name
        else:
            return None

    @property
    def assessor_name(self):
        for assignment in self.assessmentassignments.all():
            return assignment.assessor.user.get_full_name()

    def scores_for_chapters(self, chapter_names):
        scores = dict(
            ((item.section_display_position, item.section_title.lower()), item.score)
            for item in (self.section_responses_for_selected_chapters)
        )
        return [
            scores.get((position, name), 0) or 0 for (position, name) in chapter_names
        ]

    def a2f_scores_for_chapters(self, chapter_names):
        scores = dict(
            (
                (item.section_display_position, item.section_title.lower()),
                item.a2f_score,
            )
            for item in (self.section_responses_for_selected_chapters)
        )
        return [
            scores.get((position, name), 0) or 0 for (position, name) in chapter_names
        ]

    @property
    def financial_score(self):
        try:
            return self.ratio_scores.total_score or ""
        except RatioScores.DoesNotExist:
            return ""

    @property
    def sector_names(self):
        return ", ".join(
            item.name for item in self.producing_organization.sectors.all()
        )

    @property
    def product_names(self):
        return ", ".join(
            f"{item.name} ({item.fao_item_code})" if item.fao_item_code else item.name
            for item in self.products.all()
        )

    def loan_history_tuple(self, max_loan_history_count, consolidate=None):
        loan_histories = self.loan_histories.all()
        result = []
        for loan_history in loan_histories:
            result.append(loan_history.amount.currency if loan_history.amount else "")
            result.append(loan_history.amount.amount if loan_history.amount else "")
            result.append("USD")
            result.append(
                ConversionRatio.convert(
                    loan_history.amount.amount,
                    loan_history.amount.currency,
                    "USD",
                    loan_history.assessment.canonical_date,
                )
                if loan_history.amount
                else ""
            )
            result.append(loan_history.purpose)
            result.append(
                loan_history.start_date and loan_history.start_date.isoformat()
            )
            result.append(loan_history.duration)
            if consolidate is not None and consolidate is True:
                result.append(loan_history.repayment_status)
                result.append(loan_history.financier)

        result = tuple(result)

        if consolidate is not None and consolidate is True:
            padding = ("", "", "", "", "", "", "", "", "") * (
                max_loan_history_count - len(loan_histories)
            )
        else:
            padding = ("", "", "", "", "", "", "") * (
                max_loan_history_count - len(loan_histories)
            )
        return result + padding

    def pre_finance_history_tuple(self, max_pre_finance_history_count):
        pre_finance_histories = self.pre_finance_histories.all()
        result = tuple(
            item
            for pre_finance_history in pre_finance_histories
            for item in (
                pre_finance_history.amount.currency
                if pre_finance_history.amount
                else "",
                pre_finance_history.amount.amount if pre_finance_history.amount else "",
                "USD",
                ConversionRatio.convert(
                    pre_finance_history.amount.amount,
                    pre_finance_history.amount.currency,
                    "USD",
                    pre_finance_history.assessment.canonical_date,
                )
                if pre_finance_history.amount
                else "",
                pre_finance_history.purpose,
            )
        )
        padding = ("", "", "", "", "") * (
            max_pre_finance_history_count - len(pre_finance_histories)
        )
        return result + padding

    def loan_requirement_tuple(self, max_loan_requirement_count):
        loan_requirements = self.loan_requirements.all()
        result = tuple(
            item
            for loan_requirement in loan_requirements
            for item in (
                loan_requirement.amount.currency if loan_requirement.amount else "",
                loan_requirement.amount.amount if loan_requirement.amount else "",
                "USD",
                ConversionRatio.convert(
                    loan_requirement.amount.amount,
                    loan_requirement.amount.currency,
                    "USD",
                    loan_requirement.assessment.canonical_date,
                )
                if loan_requirement.amount
                else "",
                loan_requirement.purpose,
                loan_requirement.duration_in_months,
            )
        )
        padding = ("", "", "", "", "", "") * (
            max_loan_requirement_count - len(loan_requirements)
        )
        return result + padding

    def capital_requirement_tuple(self, max_capital_requirement_count):
        capital_requirements = self.capital_requirements.all()
        result = tuple(
            item
            for capital_requirement in capital_requirements
            for item in (
                capital_requirement.amount.currency
                if capital_requirement.amount
                else "",
                capital_requirement.amount.amount if capital_requirement.amount else "",
                "USD",
                ConversionRatio.convert(
                    capital_requirement.amount.amount,
                    capital_requirement.amount.currency,
                    "USD",
                    capital_requirement.assessment.canonical_date,
                )
                if capital_requirement.amount
                else "",
                capital_requirement.status,
            )
        )
        padding = ("", "", "", "", "") * (
            max_capital_requirement_count - len(capital_requirements)
        )
        return result + padding

    def grant_history_tuple(self, max_grant_history_count, consolidate=None):
        grant_histories = self.grant_histories.all()
        result = []
        for grant_history in grant_histories:
            result.append(grant_history.amount.currency if grant_history.amount else "")
            result.append(grant_history.amount.amount if grant_history.amount else "")
            result.append("USD")
            result.append(
                ConversionRatio.convert(
                    grant_history.amount.amount,
                    grant_history.amount.currency,
                    "USD",
                    grant_history.assessment.canonical_date,
                )
                if grant_history.amount
                else ""
            )
            result.append(grant_history.purpose)
            result.append(
                grant_history.start_date and grant_history.start_date.isoformat()
            )
            if consolidate is not None and consolidate is True:
                result.append(grant_history.funder_type)

        result = tuple(result)

        if consolidate is not None and consolidate is True:
            padding = ("", "", "", "", "", "", "") * (
                max_grant_history_count - len(grant_histories)
            )
        else:
            padding = ("", "", "", "", "", "") * (
                max_grant_history_count - len(grant_histories)
            )
        return result + padding

    def bank_account_tuple(self, max_bank_account_count):
        bank_accounts = self.bank_accounts.all()
        result = tuple(
            item
            for bank_account in bank_accounts
            for item in (
                bank_account.current_balance.currency
                if bank_account.current_balance
                else "",
                bank_account.current_balance.amount
                if bank_account.current_balance
                else "",
                "USD",
                ConversionRatio.convert(
                    bank_account.current_balance.amount,
                    bank_account.current_balance.currency,
                    "USD",
                    bank_account.assessment.canonical_date,
                )
                if bank_account.current_balance
                else "",
                bank_account.bank_type,
            )
        )
        padding = ("", "", "", "", "") * (max_bank_account_count - len(bank_accounts))
        return result + padding

    def financial_info_tuple(self, financial_info_years):
        result = tuple()
        if self.tool_name == "SCOPE Pro" or self.tool_name == "SCOPE Pro SME":
            basic_financial_infos = dict(
                (item.year, item) for item in self.profitlossstatements.all()
            )
            for year in financial_info_years:
                basic_financial_info = basic_financial_infos.get(year)
                if basic_financial_info:
                    result += (
                        basic_financial_info.total_sales.currency
                        if basic_financial_info.total_sales
                        else "",
                        basic_financial_info.total_sales.amount
                        if basic_financial_info.total_sales
                        else "",
                        "USD",
                        ConversionRatio.convert(
                            basic_financial_info.total_sales.amount,
                            basic_financial_info.total_sales.currency,
                            "USD",
                            basic_financial_info.assessment.canonical_date,
                        )
                        if basic_financial_info.total_sales
                        else "",
                        basic_financial_info.net_profit.currency
                        if basic_financial_info.net_profit
                        else "",
                        basic_financial_info.net_profit.amount
                        if basic_financial_info.net_profit
                        else "",
                        "USD",
                        ConversionRatio.convert(
                            basic_financial_info.net_profit.amount,
                            basic_financial_info.net_profit.currency,
                            "USD",
                            basic_financial_info.assessment.canonical_date,
                        )
                        if basic_financial_info.net_profit
                        else "",
                        basic_financial_info.total_cost_of_sales.currency
                        if basic_financial_info.total_cost_of_sales
                        else "",
                        basic_financial_info.total_cost_of_sales.amount
                        if basic_financial_info.total_cost_of_sales
                        else "",
                        "USD",
                        ConversionRatio.convert(
                            basic_financial_info.total_cost_of_sales.amount,
                            basic_financial_info.total_cost_of_sales.currency,
                            "USD",
                            basic_financial_info.assessment.canonical_date,
                        )
                        if basic_financial_info.total_cost_of_sales
                        else "",
                        basic_financial_info.income_from_continuing_operations.currency
                        if basic_financial_info.income_from_continuing_operations
                        else "",
                        basic_financial_info.income_from_continuing_operations.amount
                        if basic_financial_info.income_from_continuing_operations
                        else "",
                        "USD",
                        ConversionRatio.convert(
                            basic_financial_info.income_from_continuing_operations.amount,
                            basic_financial_info.income_from_continuing_operations.currency,
                            "USD",
                            basic_financial_info.assessment.canonical_date,
                        )
                        if basic_financial_info.income_from_continuing_operations
                        else "",

                    )
                else:
                    result += ("", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "")
        else:
            alt_basic_financial_infos = dict(
                (item.year, item) for item in self.basicprofitlossstatements.all()
            )
            for year in financial_info_years:
                alt_basic_financial_info = alt_basic_financial_infos.get(year)
                if self.producing_organization.customer.name == "Asnikom":
                    print(alt_basic_financial_info)
                if alt_basic_financial_info:
                    if self.producing_organization.customer.name == "Asnikom":
                        print("OVDEEEEEE")
                        print("OVDEEEEEE")
                    result += (
                        alt_basic_financial_info.turnover.currency
                        if alt_basic_financial_info.turnover
                        else "",
                        alt_basic_financial_info.turnover.amount
                        if alt_basic_financial_info.turnover
                        else "",
                        "USD",
                        ConversionRatio.convert(
                            alt_basic_financial_info.turnover.amount,
                            alt_basic_financial_info.turnover.currency,
                            "USD",
                            alt_basic_financial_info.assessment.canonical_date,
                        )
                        if alt_basic_financial_info.turnover
                        else "",
                        alt_basic_financial_info.net_profit.currency
                        if alt_basic_financial_info.net_profit
                        else "",
                        alt_basic_financial_info.net_profit.amount
                        if alt_basic_financial_info.net_profit
                        else "",
                        "USD",
                        ConversionRatio.convert(
                            alt_basic_financial_info.net_profit.amount,
                            alt_basic_financial_info.net_profit.currency,
                            "USD",
                            alt_basic_financial_info.assessment.canonical_date,
                        )
                        if alt_basic_financial_info.net_profit
                        else "",
                        alt_basic_financial_info.cost_of_sales.currency
                        if alt_basic_financial_info.cost_of_sales
                        else "",
                        alt_basic_financial_info.cost_of_sales.amount
                        if alt_basic_financial_info.cost_of_sales
                        else "",
                        "USD",
                        ConversionRatio.convert(
                            alt_basic_financial_info.cost_of_sales.amount,
                            alt_basic_financial_info.cost_of_sales.currency,
                            "USD",
                            alt_basic_financial_info.assessment.canonical_date,
                        )
                        if alt_basic_financial_info.cost_of_sales
                        else "",
                        alt_basic_financial_info.operational_costs.currency
                        if alt_basic_financial_info.operational_costs
                        else "",
                        alt_basic_financial_info.operational_costs.amount
                        if alt_basic_financial_info.operational_costs
                        else "",
                        "USD",
                        ConversionRatio.convert(
                            alt_basic_financial_info.operational_costs.amount,
                            alt_basic_financial_info.operational_costs.currency,
                            "USD",
                            alt_basic_financial_info.assessment.canonical_date,
                        )
                        if alt_basic_financial_info.operational_costs
                        else "",
                    )
                else:
                    result += ("", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "")
        if self.producing_organization.customer.name == "Asnikom":
            print(result)
        return result

    @classmethod
    def export_assessment_summary(cls):
        items = cls.objects.exclude(project__customer__pk=1963)
        chapter_positions_and_names = (
            SectionResponse.objects.filter(assessment__in=items)
            .order_by()
            .values_list("section_display_position", "section_title")
            .distinct()
        )
        chapter_positions_and_names = sorted(
            set(
                (position, name.lower())
                for (position, name) in chapter_positions_and_names
            )
        )
        section_response_filter_object = reduce(
            operator.or_,
            (
                Q(section_title__iexact=name, section_display_position=position)
                for (position, name) in chapter_positions_and_names
            ),
            Q(),
        )
        items = (
            items.select_related(
                "producing_organization",
                "producing_organization__customer",
                "project__customer",
                "tool__type",
                "ratio_scores",
                "producing_organization_details",
            )
            .prefetch_related(
                Prefetch(
                    "section_responses",
                    queryset=(
                        SectionResponse.objects.filter(section_response_filter_object)
                    ),
                    to_attr="section_responses_for_selected_chapters",
                ),
                "producing_organization__sectors",
                "products",
            )
            .annotate(
                loan_requirement_amount=Sum("loan_requirements__amount"),
                loan_applications_amount=Sum("loan_applications__amount"),
                loan_history_amount=Sum("loan_histories__amount"),
                pre_finance_history_amount=Sum("pre_finance_histories__amount"),
            )
        )
        headers = (
            [
                "PO name",
                "PO id",
                "Commissioned by",
                "Assessment date",
                "Assessment ID",
                "Assessment type",
                "Global region",
                "Country",
                "Region",
                "Total score",
            ]
            + [
                "{} - {}".format(position, name)
                for (position, name) in chapter_positions_and_names
            ]
            + [
                "financial performance",
                "sectors",
                "products",
                "male members",
                "female members",
                "total members",
                "loan requirement amount",
                "loan application amount",
                "loan history amount",
                "pre finance history amount",
            ]
        )
        data = [
            (
                item.producing_organization.customer.name,
                item.producing_organization.id,
                item.commissioned_by or "-",
                item.date,
                item.id,
                item.tool.type.name,
                item.producing_organization.global_region,
                item.producing_organization.country,
                item.producing_organization.region,
                item.score,
            )
            + tuple(item.scores_for_chapters(chapter_positions_and_names))
            + (
                item.financial_score,
                item.sector_names,
                item.product_names,
                (
                    item.producing_organization_details
                    or ProducingOrganizationDetails()
                ).number_of_male_members,
                (
                    item.producing_organization_details
                    or ProducingOrganizationDetails()
                ).number_of_female_members,
                (
                    item.producing_organization_details
                    or ProducingOrganizationDetails()
                ).number_of_members,
                item.loan_requirement_amount or "",
                item.loan_applications_amount or "",
                item.loan_history_amount or "",
                item.pre_finance_history_amount or "",
            )
            for item in items
        ]
        return tablib.Dataset(*data, headers=headers)

    def products_by_name(self):
        response = {}
        for product in self.products.all():
            if product.name not in response:
                response[product.name] = []
            for sale in product.productionsales.all():
                response[product.name].append(
                    {
                        "year": sale.year,
                        "unit": sale.unit,
                        "volume": sale.volume,
                        "price": sale.price,
                    }
                )
            response[product.name].sort(key=lambda item: (item["year"], item["unit"]))
        return response

    def copy_answers_from(self, source_assessment):
        with transaction.atomic():
            source_subresponses = source_assessment.subresponses.all()
            for source_subresponse in source_subresponses:
                subresponse = self.subresponses.get(
                    subquestion=source_subresponse.subquestion
                )
                subresponse.comment = source_subresponse.comment
                subresponse.selected_option = source_subresponse.selected_option
                subresponse.checked_options.set(
                    source_subresponse.checked_options.all()
                )
                subresponse.value = source_subresponse.value
                subresponse.none_of_the_above = source_subresponse.none_of_the_above
                subresponse.not_relevant = source_subresponse.not_relevant
                subresponse.save()
            source_responses = source_assessment.responses.all()
            for source_response in source_responses:
                response = self.responses.get(question=source_response.question)
                response.comment = source_response.comment
                response.accepted = source_response.accepted
                response.save()
            source_unscored_responses = (
                source_assessment.unscored_section_responses.all()
            )
            for source_unscored_response in source_unscored_responses:
                unscored_response = self.unscored_section_responses.get(
                    question=source_unscored_response.question
                )
                unscored_response.answer = source_unscored_response.answer
                unscored_response.accepted = source_unscored_response.accepted
                unscored_response.save()
            source_document_availability_responses = (
                source_assessment.document_availability_responses.all()
            )
            for (
                source_document_availability_response
            ) in source_document_availability_responses:
                document_availability_response = (
                    self.document_availability_responses.get(
                        question=source_document_availability_response.question
                    )
                )
                document_availability_response.available = (
                    source_document_availability_response.available
                )
                document_availability_response.available_with_profile = (
                    source_document_availability_response.available_with_profile
                )
                document_availability_response.comment = (
                    source_document_availability_response.comment
                )
                document_availability_response.accepted = (
                    source_document_availability_response.accepted
                )
                document_availability_response.save()
            flush()

    @property
    def last_status_update(self):
        last_log = self.logs.filter(changed_field = 'status').order_by('-created_at').first()
        if last_log:
            return last_log.created_at
        else:
            return self.created_at


class AssessmentLog(BaseModel):
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    assessment = models.ForeignKey(
        Assessment, related_name="logs", db_index=True, on_delete=models.CASCADE
    )
    action_choices = (("create", "create"), ("update", "update"), ("delete", "delete"))
    action = models.CharField(
        max_length=max([len(item[0]) for item in action_choices]),
        choices=action_choices,
    )
    actor = models.ForeignKey(
        User,
        related_name="assessment_logs",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    target_model = models.TextField()
    target_id = models.PositiveIntegerField()
    changed_field = models.TextField()
    previous_data = models.TextField(blank=True)
    updated_data = models.TextField(blank=True)

    class Meta:
        ordering = ("-created_at", "-pk")


class Image(DocumentMixin, BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="images", on_delete=models.CASCADE
    )
    title = models.CharField(max_length=255)


class AssessmentFunder(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="funders", on_delete=models.CASCADE
    )
    name = models.TextField(blank=True)
    percentage = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    denorm_always_skip = ("modified_at",)

    class Meta:
        ordering = ("pk",)


class AssessmentAssignmentBase(BaseModel):
    AS_ASSESSOR = "assessor"
    AS_FINANCIAL_SPECIALIST = "financial_specialist"
    ASSIGNED_AS_CHOICES = (
        (AS_ASSESSOR, _("assessor")),
        (AS_FINANCIAL_SPECIALIST, _("financial specialist")),
    )
    assessment = models.ForeignKey(
        Assessment, related_name="%(class)ss", on_delete=models.CASCADE
    )
    assessor = models.ForeignKey(
        Assessor, related_name="%(class)ss", on_delete=models.PROTECT
    )
    assigned_as = models.CharField(
        choices=ASSIGNED_AS_CHOICES,
        max_length=max(len(item[0]) for item in ASSIGNED_AS_CHOICES),
        default=AS_ASSESSOR,
    )
    skill_types = models.ManyToManyField(AssessmentSkillType, related_name="+")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        abstract = True


class AssessmentInvitation(AssessmentAssignmentBase):
    STATUS_ACCEPTED = "accepted"
    STATUS_DECLINED = "declined"
    STATUS_NEW = "new"
    STATUS_ON_HOLD = "on hold"
    STATUS_CHOICES = (
        (STATUS_ACCEPTED, _("accepted")),
        (STATUS_DECLINED, _("declined")),
        (STATUS_NEW, _("new")),
        (STATUS_ON_HOLD, _("on hold")),
    )
    status = models.CharField(
        choices=STATUS_CHOICES,
        max_length=max(len(item[0]) for item in STATUS_CHOICES),
        default=STATUS_NEW,
    )
    status_reason = models.TextField(blank=True)

    def convert_to_assignment(self):
        already_exists = (
            AssessmentAssignment.objects.exclude(assessment__project__customer__pk=1963)
            .filter(
                assessment_id=self.assessment_id,
                assessor_id=self.assessor_id,
                assigned_as=self.assigned_as,
            )
            .exists()
        )
        if already_exists:
            raise ParseError("Invitation was already accepted")
        self.status = AssessmentInvitation.STATUS_ACCEPTED
        self.save()
        # Get fields
        fields = AssessmentInvitation._meta.concrete_fields
        # Remove auto fields
        fields = list(
            filter(lambda field: field.name not in ("id", "created_at"), fields)
        )
        # Remove fields not in target class
        assignment_fields = AssessmentAssignment._meta.concrete_fields
        fields = list(
            filter(
                lambda field: field.name in [f.name for f in assignment_fields], fields
            )
        )
        dict_representation = dict(
            (field.name, getattr(self, field.name)) for field in fields
        )
        dict_representation["invitation"] = self
        assignment = AssessmentAssignment.objects.create(**dict_representation)
        return assignment

    class Meta:
        unique_together = (("assessment", "assessor", "assigned_as"),)
        ordering = ["-pk"]


class AssessmentAssignment(AssessmentAssignmentBase):
    invitation = models.ForeignKey(
        AssessmentInvitation,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.CASCADE,
    )
    done = models.BooleanField(  # FIXME: Add trigger to send email to quality control
        default=False
    )
    locked_for_employee = models.BooleanField(default=True)
    locked_for_assessor = models.BooleanField(default=False)
    submitted_at_least_once = models.BooleanField(default=False)
    submitted_first = models.DateField(null=True, blank=True)

    class Meta:
        ordering = ("-assessment__date",)
        unique_together = (("assessment", "assessor", "assigned_as"),)

    @denormalized(models.BooleanField, default=False)
    @depend_on("assessment__in_edit")
    def in_edit(self):
        return self.assessment.reports.filter(in_edit=True).exists()

    @denormalized(
        models.PositiveSmallIntegerField, default=1, choices=Assessment.STATUS_CHOICES
    )
    @depend_on("assessment__report_status")
    @depend_on("assessment__rapid_finalized")
    def status(self):
        from reports.models import Report

        if "rapid" in self.assessment.tool.type.name.lower():
            print(self.assessment.tool.type.name.lower())
            print(self.assessment.rapid_finalized)
            if self.assessment.rapid_finalized:
                return Assessment.STATUS_HAS_FINAL
            return Assessment.STATUS_IN_PROGRESS

        if self.assessment.reports.filter(status=Report.STATUS_FINAL).exists():
            return Assessment.STATUS_HAS_FINAL
        elif self.assessment.reports.filter(status=Report.STATUS_DRAFT).exists():
            return Assessment.STATUS_HAS_DRAFT
        elif self.submitted_at_least_once:
            if self.locked_for_employee and not self.locked_for_assessor:
                return Assessment.STATUS_QC_ASSESSOR
            else:
                return Assessment.STATUS_QC_QUALITY_REVIEW
        else:
            return Assessment.STATUS_IN_PROGRESS

    def ax_can_edit(self, user):
        """User can edit assessments"""
        if user == self.assessor.user or user.is_employee:
            return True
        elif (
            self.assigned_as == "assessor"
            and user == self.assessment.display_quality_reviewer
        ):
            return True
        elif (
            self.assigned_as == "financial_specialist"
            and user == self.assessment.display_financial_quality_reviewer
        ):
            return True
        else:
            return False

    def ax_can_view_assignment_read_only(self, user):
        """User has view only rights on assessments"""
        if (
            (user.is_customer_admin or user.is_customer_user)
            and self.assessment.project
            and (self.assessment.project.customer == user.organization)
            and not (
                user.customer_contact == self.assessment.project.contact
                or user.customer_contact
                in self.assessment.project.customer_members.all()
            )
            and not (
                user == self.assessment.display_quality_reviewer
                or user == self.assessment.display_financial_quality_reviewer
            )
        ) or (
            (user.is_customer_admin or user.is_customer_user)
            or (
                self.assessment.project
                and (
                    user.organization.pk
                    in self.assessment.project.shared_with.all().values_list(
                        "pk", flat=True
                    )
                )
            )
        ):
            return True
        else:
            return False


class AssessmentEvaluation(BaseModel):
    assessment = models.OneToOneField(
        Assessment, related_name="evaluation", on_delete=models.CASCADE
    )
    rating = models.IntegerField(null=True, blank=True)
    global_quality_issues = models.TextField(blank=True)
    global_quality_issues_options = models.ManyToManyField(
        GlobalQualityIssuesOption, related_name="assessment_evaluations", blank=True
    )

    class Meta:
        ordering = ("pk",)


class SectionResponse(MPTTModel):
    assessment = models.ForeignKey(
        Assessment,
        related_name="section_responses",
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    parent = TreeForeignKey(
        "self", related_name="children", null=True, blank=True, on_delete=models.CASCADE
    )
    section = models.ForeignKey(
        Section, related_name="section_responses", on_delete=models.PROTECT
    )
    modified_at = models.DateTimeField(auto_now=True)
    _copy_score_and_weight_from = models.ForeignKey(
        "self", related_name="+", blank=True, null=True, on_delete=models.CASCADE
    )
    denorm_always_skip = (
        "modified_at",
        "tree_id",
        "lft",
        "rght",
        "level",
        "_assessment_id",
    )

    @denormalized(
        models.DecimalField, max_digits=4, decimal_places=3, blank=True, null=True
    )
    @depend_on("_copy_score_and_weight_from__score")
    @depend_on("children__score")
    @depend_on("children__weight")
    @depend_on("responses__score")
    @depend_on("responses__weight")
    def score(self):
        """
        Score of a section is the weighted average of the scores of responses and/or subsections
        """
        if self._copy_score_and_weight_from:
            return self._copy_score_and_weight_from.score
        items = list(self.children.all()) + list(self.responses.all())
        items = list(filter(lambda item: item.score and item.weight, items))
        if items:
            total_score = sum([item.score * item.weight for item in items])
            total_weight = sum([item.weight for item in items])
            return total_score / total_weight
        else:
            return None

    @denormalized(
        models.DecimalField, max_digits=6, decimal_places=3, blank=True, null=True
    )
    @depend_on("children__a2f_score")
    @depend_on("responses__a2f_score")
    def a2f_score(self):
        # parent sections are just an average of the child sections
        if self.parent is None:
            # Get all the children
            items = list(self.children.all())
            # Filter out None scores and calculate the average
            scores = [item.a2f_score for item in items if item.a2f_score is not None and item.a2f_score != 0]
            # If there are valid scores, calculate the average, otherwise return 0 or an appropriate value
            total_score = mean(scores) if scores else 0
            
            return total_score
        result_max = 5
        result_min = 1
        original_max = self.a2f_score_max if self.a2f_score_max else 0
        original_min = 0
        original_range = original_max - original_min
        new_range = result_max - result_min

        items = list(self.children.all()) + list(self.responses.all())
        items = list(filter(lambda item: item.a2f_score, items))
        if items:
            if original_max == original_min:
                # Handle the case where original_max equals original_min to avoid division by zero
                return Decimal(0)  # or any appropriate value or logic you want to apply
            
            total_score = sum([item.a2f_score for item in items])
            #return ((total_score - original_min) * (new_range)) / original_range + result_min
            a2f_score = 1 + ((total_score/360) * 4)
            ret_a2f_score = round(a2f_score, 2)
            return ret_a2f_score
        else:
            return Decimal(0)

    @denormalized(
        models.DecimalField, max_digits=10, decimal_places=3, blank=True, null=True
    )
    @depend_on("children__a2f_score")
    @depend_on("responses__a2f_score")
    def a2f_score_max(self):
        total_options_count = sum(
            subresponse.subquestion.options.count()
            for response in self.responses.all()
            for subresponse in response.subresponses.all()
        )

        # Calculate the a2f_score_max for children
        children_scores = sum(
            child.a2f_score_max for child in self.children.all() if child.a2f_score_max is not None
        )
        # Calculate the score for the current section
        current_score = Decimal(total_options_count * 2) if total_options_count else Decimal(0)

        return Decimal(current_score + children_scores)

    
    @denormalized(
        models.DecimalField, max_digits=6, decimal_places=4, blank=True, null=True
    )
    @depend_on("_copy_score_and_weight_from__weight")
    @depend_on("_copy_score_and_weight_from__parent_id")
    @depend_on("children__weight")
    @depend_on("responses__weight")
    @depend_on("section")
    def weight(self):
        """
        Weight of section is sum of weights of filled responses and sections
        """
        if self.section._weight_override is not None:
            return self.section._weight_override
        if self._copy_score_and_weight_from:
            try:
                return (
                    self._copy_score_and_weight_from.weight
                    / self._copy_score_and_weight_from.parent.summed_weight
                )
            except (InvalidOperation, DivisionByZero):
                return Decimal(0)
        items = list(self.children.all()) + list(self.responses.all())
        items = list(filter(lambda item: item.weight, items))
        if items:
            return sum(item.weight for item in items)
        else:
            return Decimal(0)

    @property
    def summed_weight(self):
        items = list(self.children.all()) + list(self.responses.all())
        items = list(filter(lambda item: item.weight, items))
        if items:
            return sum(item.weight for item in items)
        else:
            return Decimal(0)

    @denormalized(models.TextField, blank=True)
    @depend_on("section__title")
    def section_title(self):
        return self.section.title

    @denormalized(models.TextField, blank=True)
    @depend_on("section__display_position")
    def section_display_position(self):
        return self.section.display_position

    @denormalized(models.TextField, blank=True)
    @depend_on("section__explanation")
    def section_explanation(self):
        return self.section.explanation

    @property
    def has_responses(self):
        """
        Check if the SectionResponse object has any Response objects associated with it
        """
        return Response.objects.filter(
            _section_response__in=(self.get_descendants(include_self=True))
        ).exists()

    @denormalized(
        models.ForeignKey,
        Assessment,
        blank=True,
        null=True,
        related_name="all_section_responses",
        on_delete=models.CASCADE,
    )
    @depend_on("assessment_id")
    @depend_on("parent_id")
    def _assessment(self):
        if self.pk:
            if self.assessment_id:
                return self.assessment_id
            else:
                root_nodes = self.__class__.objects.filter(
                    tree_id=self.tree_id, parent=None
                )
                if root_nodes.exists():
                    return root_nodes.first().assessment_id
        return None

    class Meta:
        ordering = ("section_display_position", "section__position")

    class MPTTMeta:
        order_insertion_by = ["section_display_position"]


@commentable(assessment_denorm=True)
@documentable(assessment_denorm=True)
class Response(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="responses", on_delete=models.CASCADE
    )
    question = models.ForeignKey(
        Question, related_name="responses", on_delete=models.PROTECT
    )
    assessor = models.ForeignKey(
        Assessor,
        related_name="responses",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    comment = models.TextField(blank=True)
    accepted = models.BooleanField(default=True)
    _section_response = models.ForeignKey(
        SectionResponse,
        related_name="responses",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
    )
    denorm_always_skip = ("modified_at",)


    def get_is_completed(self):
        # (Fix for conditional question save ( incomplete response error when trying to save response whose subresponse is conditional and grayed out -> 3.1.2 Child labor)
        #Can't use is_completed because it is dependant on subresponses__not_empty so it won't always trigger.
        #It can happen that in database is_completed flag of response is false and when we save with new logic, is_completed stays false and can't be saved.
        my_list = list(self.subresponses.all())
        # not_empties = list(self.subresponses.all().values_list("not_empty", flat=True))
        not_empties = list(
            self.subresponses.filter(subquestion__is_conditional=False).values_list("not_empty", flat=True)
        )
        if not_empties:
            return all(not_empties)
        else:
            # No subresponses, completed by definition
            return True


    @denormalized(models.BooleanField, default=False)
    @depend_on("subresponses__not_empty")
    def is_completed(self):
        not_empties = list(self.subresponses.all().values_list("not_empty", flat=True))
        if not_empties:
            return all(not_empties)
        else:
            # No subresponses, completed by definition
            return True

    @denormalized(
        models.DecimalField, max_digits=4, decimal_places=3, blank=True, null=True
    )
    @depend_on("subresponses__weight")
    @depend_on("subresponses__score")
    def score(self):
        """
        If this response has subrespones, compute weighted average
        """
        subresponses = self.subresponses.all()
        if subresponses:
            weights = []
            values = []
            for subresponse in subresponses:
                weights.append(subresponse.weight)
                values.append(subresponse.score)
            return weighted_average(weights, values)
        else:
            return self.score

    @denormalized(
        models.DecimalField, max_digits=6, decimal_places=3, blank=True, null=True
    )
    @depend_on("subresponses__a2f_score")
    def a2f_score(self):
        """
        If this response has subrespones, compute weighted average
        """
        subresponses = self.subresponses.all()
        if subresponses:
            values = []
            for subresponse in subresponses:
                if subresponse.a2f_score is not None:
                    values.append(subresponse.a2f_score)
            return sum(values)
        else:
            return 0

    @denormalized(
        models.DecimalField, max_digits=6, decimal_places=4, blank=True, null=True
    )
    @depend_on("question__weight")
    def weight(self):
        """
        Weight of response is weight of question, unless response is null or 0
        """
        if self.score:
            return self.question.weight
        else:
            return Decimal(0)

    @denormalized(models.TextField, blank=True)
    @depend_on("question__title")
    def question_title(self):
        return self.question.title

    @denormalized(models.TextField, blank=True)
    @depend_on("question__description")
    def question_description(self):
        return self.question.description

    @denormalized(models.TextField, blank=True)
    @depend_on("question__explanation")
    def question_explanation(self):
        return self.question.explanation

    @denormalized(models.TextField, blank=True)
    @depend_on("question__scoring_indicators")
    def question_scoring_indicators(self):
        return self.question.scoring_indicators

    @denormalized(
        models.ForeignKey,
        ScaleType,
        related_name="+",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    @depend_on("question__scale_type_id")
    def question_scale_type(self):
        return self.question.scale_type

    @denormalized(models.TextField, blank=True)
    @depend_on("question__display_position")
    def question_display_position(self):
        return self.question.display_position

    @property
    def is_applicable(self):
        """
        Return True if a response is applicable or False when it is not

        Based on the conditions of the question
        """
        sectors = self.question.applicable_sectors.all()
        po_types = self.question.applicable_po_types.all()
        countries = self.question.applicable_countries.all()
        activities = self.question.applicable_activities.all()
        products = self.question.applicable_products.all()

        subresponses = self.subresponses.all()

        at_least_one_checked = False
        if subresponses:
            for subresponse in subresponses:
                if at_least_one_checked:
                    break
                if subresponse.subquestion.type == "checkbox" and not (
                    subresponse.not_relevant
                ):
                    if subresponse.checked_options.count():
                        at_least_one_checked = True
                elif subresponse.subquestion.type != "checkbox":
                    at_least_one_checked = True

        if not settings.TRAINEE and not at_least_one_checked:
            return False

        if not any((sectors, po_types, countries, activities, products)):
            # Not a conditional question
            return True
        else:
            if products and set(item.name for item in products) & set(
                item.name for item in self.assessment.products.all()
            ):
                return True

            elif sectors and set(sectors) & set(
                self.assessment.producing_organization.sectors.all()
            ):
                return True

            elif po_types and set(po_types) & set(
                [self.assessment.producing_organization.customer.type]
            ):
                return True
            elif countries and set(item.name for item in countries) & set(
                [self.assessment.producing_organization.country]
            ):
                return True
            elif activities and set(activities) & set(
                self.assessment.producing_organization.activities.all()
            ):
                return True
            else:
                return False

    @property
    def submitted(self):
        return self.assessment.assessmentassignments.filter(
            assigned_as=AssessmentAssignment.AS_ASSESSOR, submitted_at_least_once=True
        ).exists()

    class Meta:
        unique_together = (("assessment", "question"),)
        ordering = ("question__position",)

    class MPTTMeta:
        order_insertion_by = ["question_display_position"]


class SubResponse(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="subresponses", on_delete=models.CASCADE
    )
    subquestion = models.ForeignKey(
        SubQuestion, related_name="subresponses", on_delete=models.CASCADE
    )
    assessor = models.ForeignKey(
        Assessor,
        related_name="subresponses",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    checked_options = models.ManyToManyField(
        SubQuestionOption, related_name="+", blank=True
    )
    selected_option = models.ForeignKey(
        SubQuestionOption,
        related_name="+",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    value = models.IntegerField(blank=True, null=True)
    not_relevant = models.BooleanField(default=False)
    none_of_the_above = models.BooleanField(default=False)
    _response = models.ForeignKey(
        Response,
        related_name="subresponses",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
    )
    denorm_always_skip = ("modified_at",)


    @denormalized(models.TextField, default="")
    @depend_on("_response__comment")
    def comment(self):
        return self._response.comment

    @denormalized(models.BooleanField, default=True)
    @depend_on("_response__accepted")
    def accepted(self):
        return self._response.accepted

    @denormalized(models.BooleanField, default=False)
    @depend_on("not_relevant")
    @depend_on("none_of_the_above")
    @depend_on("subquestion__type")
    @depend_on("selected_option_id")
    @depend_on("value")
    def not_empty(self):
        """
        Check if the subresponse is no longer in the default, empty state
        """
        if self.not_relevant:
            return True
        if self.none_of_the_above:
            return True
        if self.subquestion.type == "mpc" and self.selected_option:
            return True
        if self.subquestion.type == "numeric" and self.value is not None:
            return True
        if self.subquestion.type == "checkbox":
            return True
        return False

    @denormalized(
        models.DecimalField, max_digits=4, decimal_places=3, blank=True, null=True
    )
    @depend_on("not_relevant")
    @depend_on("none_of_the_above")
    @depend_on("subquestion__type")
    @depend_on("selected_option__score")
    @depend_on("value")
    @depend_on("subquestion___option_ids")
    @depend_on("checked_options")
    @depend_on("checked_options__id")
    def score(self):
        """
        Compute score based on subquestion type and provided answer(s)
        """
        if self.not_relevant:
            return 0
        if self.none_of_the_above:
            return 1
        if self.subquestion.type == "mpc":
            if self.selected_option:
                return self.selected_option.score or 0
        if self.subquestion.type == "numeric":
            if self.value is not None:
                return (
                    self.subquestion.options.get(
                        Q(
                            Q(range_min__lt=self.value)
                            | Q(range_min__isnull=True)
                            | Q(
                                Q(range_min__isnull=False)
                                & Q(range_min=self.min_value)
                                & Q(range_min=self.value)
                            )
                        )
                        & Q(Q(range_max__gte=self.value) | Q(range_max__isnull=True))
                    ).score
                    or 0
                )
        if self.subquestion.type == "checkbox":
            if self.pk:
                return Decimal(1) + self.score_per_item * Decimal(
                    self.checked_options.count()
                )
        return 0

    @denormalized(
        models.DecimalField, max_digits=6, decimal_places=3, blank=True, null=True
    )
    @depend_on("not_relevant")
    @depend_on("none_of_the_above")
    @depend_on("subquestion__type")
    @depend_on("selected_option__score")
    @depend_on("value")
    @depend_on("subquestion___option_ids")
    @depend_on("checked_options")
    @depend_on("checked_options__id")
    def a2f_score(self):
        """
        Compute score based on subquestion type and provided answer(s)
        """
        if self.not_relevant:
            return 0
        if self.none_of_the_above:
            return 0
        if self.subquestion.type == "mpc":
            if self.selected_option:
                return self.selected_option.a2f_score or 0
        if self.subquestion.type == "numeric":
            if self.value is not None:
                return (
                    self.subquestion.options.get(
                        Q(
                            Q(range_min__lt=self.value)
                            | Q(range_min__isnull=True)
                            | Q(
                                Q(range_min__isnull=False)
                                & Q(range_min=self.min_value)
                                & Q(range_min=self.value)
                            )
                        )
                        & Q(Q(range_max__gte=self.value) | Q(range_max__isnull=True))
                    ).a2f_score
                    or 0
                )
        if self.subquestion.type == "checkbox":
            if self.pk:
                return sum(option.a2f_score for option in self.checked_options.all())
        return 0

    @property
    def score_map(self):
        options = self.subquestion.options.all()
        options = sorted(
            options,
            key=lambda item: (
                item.range_min or Decimal("-Infinity"),
                item.range_max or Decimal("+Infinity"),
            ),
        )
        return options

    @property
    def score_per_item(self):
        total_options = self.subquestion.options.count()
        if total_options:
            score_per_item = Decimal(4) / Decimal(total_options)
        else:
            score_per_item = Decimal(0)
        return score_per_item

    @property
    def pretty_score_per_item(self):
        return strip_zeroes(str(self.score_per_item))

    @property
    def pretty_score(self):
        """
        Pretty-printed score, used in pdf
        """
        if not self.score:
            return "0"
        else:
            pretty_score = strip_zeroes(str(self.score))
            return pretty_score

    @denormalized(
        models.DecimalField, max_digits=6, decimal_places=4, blank=True, null=True
    )
    @depend_on("score")
    @depend_on("subquestion__weight")
    def weight(self):
        """
        Weight of response is weight of question, unless response is null or 0
        """
        if self.score:
            return self.subquestion.weight
        else:
            return Decimal(0)

    @property
    def pretty_weight(self):
        """
        Pretty-printed weight, used in pdf
        """
        if not self.weight:
            return "0"
        else:
            total_weight = self._response.subresponses.all().aggregate(
                total_weight=Sum("weight")
            )["total_weight"]
            relative_weight = float(self.weight) / float(total_weight)
            pretty_weight = strip_zeroes(str(relative_weight * 100))
            return pretty_weight

    @denormalized(models.TextField, blank=True)
    @depend_on("subquestion__display_position")
    def subquestion_display_position(self):
        return self.subquestion.display_position

    @denormalized(models.BooleanField, default=False)
    @depend_on("subquestion__can_be_none_of_the_above")
    def can_be_none_of_the_above(self):
        return self.subquestion.can_be_none_of_the_above

    @denormalized(models.BooleanField, default=False)
    @depend_on("subquestion__can_be_not_relevant")
    def can_be_not_relevant(self):
        return self.subquestion.can_be_not_relevant

    @denormalized(models.BooleanField, default=False)
    @depend_on("subquestion__comment_mandatory")
    def comment_mandatory(self):
        return self.subquestion.comment_mandatory

    @denormalized(models.BooleanField, default=False)
    @depend_on("subquestion__not_relevant_is_hidden")
    def not_relevant_is_hidden(self):
        return self.subquestion.not_relevant_is_hidden

    @denormalized(
        models.DecimalField, max_digits=20, decimal_places=4, blank=True, null=True
    )
    @depend_on("subquestion__type")
    @depend_on("subquestion___option_ids")
    def min_value(self):
        if (
            self.subquestion.type == "numeric"
            and self.subquestion.options.all().exists()
        ):
            response = min(
                [
                    Decimal("-Infinity") if item.range_min is None else item.range_min
                    for item in self.subquestion.options.all()
                ]
            )
            if response == Decimal("-Infinity"):
                response = None
            return response
        else:
            return None

    @denormalized(
        models.DecimalField, max_digits=20, decimal_places=4, blank=True, null=True
    )
    @depend_on("subquestion__type")
    @depend_on("subquestion___option_ids")
    def max_value(self):
        if (
            self.subquestion.type == "numeric"
            and self.subquestion.options.all().exists()
        ):
            response = max(
                [
                    Decimal("Infinity") if item.range_max is None else item.range_max
                    for item in self.subquestion.options.all()
                ]
            )
            if response == Decimal("Infinity"):
                response = None
            return response
        else:
            return None

    @property
    def submitted(self):
        return self.assessment.assessmentassignments.filter(
            assigned_as=AssessmentAssignment.AS_ASSESSOR, submitted_at_least_once=True
        ).exists()

    class Meta:
        unique_together = (("assessment", "subquestion"),)
        ordering = ("subquestion__position",)

    class MPTTMeta:
        order_insertion_by = ["subquestion_display_position"]


class UnscoredSectionResponse(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="unscored_section_responses", on_delete=models.CASCADE
    )
    question = models.ForeignKey(
        UnscoredSectionQuestion, related_name="responses", on_delete=models.PROTECT
    )
    assessor = models.ForeignKey(
        Assessor,
        related_name="unscored_section_responses",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    answer = models.TextField(blank=True)
    accepted = models.BooleanField(default=True)
    _section_response = models.ForeignKey(
        SectionResponse,
        related_name="unscored_responses",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
    )

    @property
    def question_title(self):
        return self.question.title

    class Meta:
        ordering = ("question__title",)


class FinancialInfo(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="%(class)ss", on_delete=models.CASCADE
    )
    assessor = models.ForeignKey(
        Assessor,
        related_name="%(class)ss",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    year = models.PositiveSmallIntegerField()
    no_data = models.BooleanField(default=False)
    accountant_comments = models.TextField(blank=True)

    @denormalized(
        models.CharField,
        max_length=max(len(item[0]) for item in CURRENCY_CHOICES),
        choices=CURRENCY_CHOICES,
        default="USD",
    )
    @depend_on("assessment__currency")
    def _currency(self):
        try:
            return self.assessment.currency
        except Assessment.DoesNotExist:
            return "USD"

    class Meta:
        abstract = True
        ordering = ("-year",)
        unique_together = (("assessment", "year"),)


class FinancialStrategy(BaseModel):
    assessment = models.OneToOneField(
        Assessment, related_name="financial_strategy", on_delete=models.CASCADE
    )
    business_surplus = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    member_fee_capital = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    loan = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    pre_finance = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    grant = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    other = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    business_surplus_no_information_available = models.BooleanField(default=False)
    member_fee_capital_no_information_available = models.BooleanField(default=False)
    loan_no_information_available = models.BooleanField(default=False)
    pre_finance_no_information_available = models.BooleanField(default=False)
    grant_no_information_available = models.BooleanField(default=False)
    other_no_information_available = models.BooleanField(default=False)

    denorm_always_skip = ("modified_at",)

    @denormalized(
        models.DecimalField, max_digits=5, decimal_places=2, blank=True, null=True
    )
    def total(self):
        """
        Finance strategy fields need to sum to 100
        """
        finance_resources = [
            Decimal(self.business_surplus) if self.business_surplus else Decimal(0),
            Decimal(self.member_fee_capital) if self.member_fee_capital else Decimal(0),
            Decimal(self.loan) if self.loan else Decimal(0),
            Decimal(self.pre_finance) if self.pre_finance else Decimal(0),
            Decimal(self.grant) if self.grant else Decimal(0),
            Decimal(self.other) if self.other else Decimal(0),
        ]
        total = sum(finance_resources)
        return total


class BasicProfitLossStatement(FinancialInfo):
    turnover = MoneyField(
        verbose_name="Turnover", blank=True, null=True, max_digits=20, decimal_places=2
    )
    cost_of_sales = MoneyField(
        verbose_name="Cost of sales",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    operational_costs = MoneyField(
        verbose_name="Operational costs",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    # net_profit = MoneyField(
    #     verbose_name="Net profit",
    #     blank=True,
    #     null=True,
    #     max_digits=20,
    #     decimal_places=2,
    # )
    denorm_always_skip = ("modified_at",)

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2, verbose_name="Net profit")
    def net_profit(self):
        if self.gross_profit is not None and self.operational_costs is not None:
            value = self.gross_profit.amount - self.operational_costs.amount
            return value
        else:
            return None


    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def gross_profit(self):
        if self.turnover is not None and self.cost_of_sales is not None:
            value = self.turnover.amount - self.cost_of_sales.amount
            return value
        else:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("turnover")
    def turnover_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.turnover, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("cost_of_sales")
    def cost_of_sales_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.cost_of_sales, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("operational_costs")
    def operational_costs_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.operational_costs, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("net_profit")
    def net_profit_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.net_profit, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("gross_profit")
    def gross_profit_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.gross_profit, self.year)


class BalanceSheet(FinancialInfo):
    cash = MoneyField(
        verbose_name="Cash and cash equivalents",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    account_receivables = MoneyField(
        verbose_name="Account receivables",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    other_receivables = MoneyField(
        verbose_name="Other receivables",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    inventories = MoneyField(
        verbose_name="Inventories",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    other_current_assets = MoneyField(
        verbose_name="Other current assets",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    fixed_assets = MoneyField(
        verbose_name="Fixed assets",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    intangible_assets = MoneyField(
        verbose_name="Intangible assets",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    goodwill = MoneyField(
        verbose_name="Goodwill", blank=True, null=True, max_digits=20, decimal_places=2
    )
    other_non_current_assets = MoneyField(
        verbose_name="Other non-current assets",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    accounts_payable = MoneyField(
        verbose_name="Accounts payable",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    short_term_loans = MoneyField(
        verbose_name="Short term loans",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    overdrafts = MoneyField(
        verbose_name="Overdrafts",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    income_tax_payable = MoneyField(
        verbose_name="Income tax payable",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    short_term_provisions = MoneyField(
        verbose_name="Short term provisions",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    other_current_liabilities = MoneyField(
        verbose_name="Other current liabilities",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    long_term_loans = MoneyField(
        verbose_name="Long term loans",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    deferred_tax = MoneyField(
        verbose_name="Deferred tax",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    provisions = MoneyField(
        verbose_name="Provisions",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    other_non_current_liabilities = MoneyField(
        verbose_name="Other non current liabilities",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    share_capital = MoneyField(
        verbose_name="Share capital",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    share_premium = MoneyField(
        verbose_name="Share premium",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    retained_earnings = MoneyField(
        verbose_name="Retained earnings",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    grants = MoneyField(
        verbose_name="Grants (seed capital)",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    statutory_legal_reserves = MoneyField(
        verbose_name="Statutory/legal reserves",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    other_reserves = MoneyField(
        verbose_name="Other reserves",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    other = MoneyField(
        verbose_name="Other", blank=True, null=True, max_digits=20, decimal_places=2
    )
    manual_total_assets = MoneyField(
        verbose_name="Manually computed total assets",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    denorm_always_skip = ("modified_at",)

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def total_current_assets(self):
        values = (
            self.cash,
            self.account_receivables,
            self.other_receivables,
            self.inventories,
            self.other_current_assets,
        )
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def total_non_current_assets(self):
        values = (
            self.fixed_assets,
            self.intangible_assets,
            self.goodwill,
            self.other_non_current_assets,
        )
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def total_assets(self):
        values = (self.total_current_assets, self.total_non_current_assets)
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def total_current_liabilities(self):
        values = (
            self.accounts_payable,
            self.short_term_loans,
            self.overdrafts,
            self.income_tax_payable,
            self.short_term_provisions,
            self.other_current_liabilities,
        )
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def total_non_current_liabilities(self):
        values = (
            self.long_term_loans,
            self.deferred_tax,
            self.provisions,
            self.other_non_current_liabilities,
        )
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def total_liabilities(self):
        values = (self.total_current_liabilities, self.total_non_current_liabilities)
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def net_assets(self):
        values = (
            self.total_assets,
            -self.total_liabilities if self.total_liabilities else None,
        )
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def total_equity(self):
        values = (
            self.share_capital,
            self.share_premium,
            self.retained_earnings,
            self.grants,
            self.statutory_legal_reserves,
            self.other_reserves,
            self.other,
        )
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(
        models.OneToOneField,
        "self",
        related_name="_next",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    @depend_on("assessment___balancesheet_years")
    def _previous(self):
        try:
            return self.assessment.balancesheets.all().get(year=self.year - 1)
        except BalanceSheet.DoesNotExist:
            if self.year:
                try:
                    start_balance_sheet = self.assessment.balancesheets.all().get(
                        year=0
                    )
                except BalanceSheet.DoesNotExist:
                    return None
                else:
                    (
                        BalanceSheet.objects.filter(
                            _previous=start_balance_sheet
                        ).update(_previous=None)
                    )
                    return start_balance_sheet
            else:
                return None

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    @depend_on("_previous__total_current_assets")
    @depend_on("_previous__total_current_liabilities")
    @depend_on("total_current_assets")
    @depend_on("total_current_liabilities")
    def net_working_capital(self):
        if not any([self.total_current_assets, self.total_current_liabilities]):
            return None
        # At least one of total_current_assets, total_current_liabilities exists
        # Get currency
        if self.total_current_assets:
            currency = self.total_current_assets.currency
        else:
            currency = self.total_current_liabilities.currency
        # Potentially fake self totals
        self_total_current_assets = self.total_current_assets or Money(0, currency)
        self_total_current_liabilities = self.total_current_liabilities or Money(
            0, currency
        )
        # Get previous
        previous = self._previous
        # Potentially fake previous totals
        if previous:
            previous_total_current_assets = previous.total_current_assets or Money(
                0, currency
            )
            previous_total_current_liabilities = (
                previous.total_current_liabilities or Money(0, currency)
            )
        else:
            previous_total_current_assets = Money(0, currency)
            previous_total_current_liabilities = Money(0, currency)
        # Check if currencies match
        if (
            self_total_current_assets.currency != previous_total_current_assets.currency
            or self_total_current_liabilities.currency
            != previous_total_current_liabilities.currency
        ):
            return None
        value = Money(
            (
                (
                    self_total_current_assets.amount
                    + previous_total_current_assets.amount
                )
                / Decimal(2.0)
            )
            - (
                (
                    self_total_current_liabilities.amount
                    + previous_total_current_liabilities.amount
                )
                / Decimal(2.0)
            ),
            currency,
        )
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    @depend_on("_previous__inventories")
    @depend_on("inventories")
    def average_inventory(self):
        # Check if all required values exist
        previous = self._previous
        if not previous:
            return None
        if not (
            self.inventories is not None
            and self.inventories.amount is not None
            and previous.inventories is not None
            and previous.inventories.amount is not None
        ):
            return None
        # Check if currencies match
        if self.inventories.currency != previous.inventories.currency:
            return None
        value = Money(
            ((self.inventories.amount + previous.inventories.amount) / Decimal(2.0)),
            self.inventories.currency,
        )
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    @depend_on("_previous__account_receivables")
    @depend_on("account_receivables")
    def average_receivables(self):
        # Check if all required values exist
        previous = self._previous
        if not previous:
            return None
        if not (
            self.account_receivables is not None
            and self.account_receivables.amount is not None
            and previous.account_receivables is not None
            and previous.account_receivables.amount is not None
        ):
            return None
        # Check if currencies match
        if self.account_receivables.currency != previous.account_receivables.currency:
            return None
        value = Money(
            (
                (self.account_receivables.amount + previous.account_receivables.amount)
                / Decimal(2.0)
            ),
            self.account_receivables.currency,
        )
        return value

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("cash")
    def cash_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.cash, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("account_receivables")
    def account_receivables_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.account_receivables, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("other_receivables")
    def other_receivables_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.other_receivables, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("inventories")
    def inventories_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.inventories, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("other_current_assets")
    def other_current_assets_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.other_current_assets, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("fixed_assets")
    def fixed_assets_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.fixed_assets, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("intangible_assets")
    def intangible_assets_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.intangible_assets, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("goodwill")
    def goodwill_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.goodwill, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("other_non_current_assets")
    def other_non_current_assets_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.other_non_current_assets, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("accounts_payable")
    def accounts_payable_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.accounts_payable, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("short_term_loans")
    def short_term_loans_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.short_term_loans, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("overdrafts")
    def overdrafts_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.overdrafts, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("income_tax_payable")
    def income_tax_payable_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.income_tax_payable, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("short_term_provisions")
    def short_term_provisions_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.short_term_provisions, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("other_current_liabilities")
    def other_current_liabilities_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.other_current_liabilities, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("long_term_loans")
    def long_term_loans_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.long_term_loans, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("deferred_tax")
    def deferred_tax_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.deferred_tax, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("provisions")
    def provisions_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.provisions, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("other_non_current_liabilities")
    def other_non_current_liabilities_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.other_non_current_liabilities, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("share_capital")
    def share_capital_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.share_capital, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("share_premium")
    def share_premium_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.share_premium, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("retained_earnings")
    def retained_earnings_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.retained_earnings, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("grants")
    def grants_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.grants, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("statutory_legal_reserves")
    def statutory_legal_reserves_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.statutory_legal_reserves, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("other_reserves")
    def other_reserves_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.other_reserves, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("other")
    def other_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.other, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("manual_total_assets")
    def manual_total_assets_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.manual_total_assets, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("total_current_assets")
    def total_current_assets_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.total_current_assets, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("total_non_current_assets")
    def total_non_current_assets_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.total_non_current_assets, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("total_assets")
    def total_assets_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.total_assets, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("total_current_liabilities")
    def total_current_liabilities_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.total_current_liabilities, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("total_non_current_liabilities")
    def total_non_current_liabilities_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.total_non_current_liabilities, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("total_liabilities")
    def total_liabilities_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.total_liabilities, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("net_assets")
    def net_assets_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.net_assets, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("total_equity")
    def total_equity_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.total_equity, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("net_working_capital")
    def net_working_capital_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.net_working_capital, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("average_inventory")
    def average_inventory_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.average_inventory, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("average_receivables")
    def average_receivables_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.average_receivables, self.year)


class ProfitLossStatement(FinancialInfo):
    direct_sales = MoneyField(
        verbose_name="Direct sales",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    indirect_sales = MoneyField(
        verbose_name="Indirect sales",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    other_income = MoneyField(
        verbose_name="Other income",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    depreciation_and_amortization = MoneyField(
        verbose_name="Depreciation and amortization",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    interest_expense = MoneyField(
        verbose_name="Interest expense",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    income_tax_expense = MoneyField(
        verbose_name="Income tax expense",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    earnings_from_discontinued_operations = MoneyField(
        verbose_name="Earnings (losses) from discontinued operations (net of tax)",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    manual_net_profit = MoneyField(
        verbose_name="Manually computed net profit",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    audited = models.BooleanField(blank=True, null=True)
    denorm_always_skip = ("modified_at",)

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def total_sales(self):
        values = (self.direct_sales, self.indirect_sales)
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    @depend_on("costs_of_sales__value")
    def total_cost_of_sales(self):
        values = (item.value for item in self.costs_of_sales.all())
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def gross_profit(self):
        values = (
            self.total_sales,
            -self.total_cost_of_sales if self.total_cost_of_sales else None,
        )
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    @depend_on("expenses__value")
    def total_expenses(self):
        values = (item.value for item in self.expenses.all())
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def ebitda(self):
        values = (
            self.gross_profit,
            -self.total_expenses if self.total_expenses else None,
            self.other_income,
        )
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def ebit(self):
        values = (
            self.ebitda,
            -self.depreciation_and_amortization
            if self.depreciation_and_amortization
            else None,
        )
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def income_from_continuing_operations(self):
        values = (
            self.ebit,
            -self.interest_expense if self.interest_expense else None,
            -self.income_tax_expense if self.income_tax_expense else None,
        )
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def net_profit(self):
        values = (
            self.income_from_continuing_operations,
            +self.earnings_from_discontinued_operations
            if self.earnings_from_discontinued_operations
            else None,
        )
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("direct_sales")
    def direct_sales_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.direct_sales, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("indirect_sales")
    def indirect_sales_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.indirect_sales, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("other_income")
    def other_income_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.other_income, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("depreciation_and_amortization")
    def depreciation_and_amortization_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.depreciation_and_amortization, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("interest_expense")
    def interest_expense_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.interest_expense, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("income_tax_expense")
    def income_tax_expense_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.income_tax_expense, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("earnings_from_discontinued_operations")
    def earnings_from_discontinued_operations_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(
            self.earnings_from_discontinued_operations, self.year
        )

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("manual_net_profit")
    def manual_net_profit_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.manual_net_profit, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("total_sales")
    def total_sales_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.total_sales, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("total_cost_of_sales")
    def total_cost_of_sales_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.total_cost_of_sales, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("gross_profit")
    def gross_profit_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.gross_profit, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("total_expenses")
    def total_expenses_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.total_expenses, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("ebitda")
    def ebitda_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.ebitda, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("ebit")
    def ebit_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.ebit, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("income_from_continuing_operations")
    def income_from_continuing_operations_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(
            self.income_from_continuing_operations, self.year
        )

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("net_profit")
    def net_profit_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.net_profit, self.year)


class CostOfSale(BaseModel):
    profit_loss_statement = models.ForeignKey(
        ProfitLossStatement, related_name="costs_of_sales", on_delete=models.CASCADE
    )
    name = models.TextField()
    value = MoneyField(
        verbose_name="Value", blank=True, null=True, max_digits=20, decimal_places=2
    )

    @denormalized(
        models.CharField,
        max_length=max(len(item[0]) for item in CURRENCY_CHOICES),
        choices=CURRENCY_CHOICES,
        default="USD",
    )
    @depend_on("profit_loss_statement___currency")
    def _currency(self):
        return self.profit_loss_statement._currency

    @denormalized(
        models.ForeignKey,
        "Assessment",
        related_name="+",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
    )
    @depend_on("profit_loss_statement__assessment_id")
    def _assessment(self):
        return self.profit_loss_statement.assessment

    class Meta:
        ordering = ("pk",)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("value")
    def value_usd(self):
        from .utils import convert_to_usd

        return convert_to_usd(self.value, self._assessment)


class Expense(BaseModel):
    profit_loss_statement = models.ForeignKey(
        ProfitLossStatement, related_name="expenses", on_delete=models.CASCADE
    )
    name = models.TextField()
    value = MoneyField(
        verbose_name="Value", blank=True, null=True, max_digits=20, decimal_places=2
    )

    @denormalized(
        models.CharField,
        max_length=max(len(item[0]) for item in CURRENCY_CHOICES),
        choices=CURRENCY_CHOICES,
        default="USD",
    )
    @depend_on("profit_loss_statement___currency")
    def _currency(self):
        return self.profit_loss_statement._currency

    @denormalized(
        models.ForeignKey,
        "Assessment",
        related_name="+",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
    )
    @depend_on("profit_loss_statement__assessment_id")
    def _assessment(self):
        return self.profit_loss_statement.assessment

    class Meta:
        ordering = ("pk",)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("value")
    def value_usd(self):
        from .utils import convert_to_usd

        return convert_to_usd(self.value, self._assessment)


class CashFlowStatement(FinancialInfo):
    net_income = MoneyField(
        verbose_name="Net income",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    adjustment_for_tax = MoneyField(
        verbose_name="Adjustment for tax",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    depreciation_and_amortization = MoneyField(
        verbose_name="Depreciation and amortization",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    investment_income = MoneyField(
        verbose_name="Investment income",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    interest_expense = MoneyField(
        verbose_name="Interest expense",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    profit_on_sale_of_ppe = MoneyField(
        verbose_name="Profit / (loss) on sale of PP&E",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    working_capital_changes = MoneyField(
        verbose_name="Working capital changes",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    decrease_in_trade_and_other_receivables = MoneyField(
        verbose_name="(Increase) / Decrease in trade and other receivables",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    increase_in_inventories = MoneyField(
        verbose_name="Increase / (decrease) in inventories",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    increase_in_trade_and_other_payables = MoneyField(
        verbose_name="Increase / (decrease) in trade payables and other payables",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    interest_paid = MoneyField(
        verbose_name="Interest paid",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    income_taxes_paid = MoneyField(
        verbose_name="Income taxes paid",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    purchases_of_property_plant_and_equipment = MoneyField(
        verbose_name="Purchases of property, plant and equipment (PPE)",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    proceeds_from_sale_of_PPE = MoneyField(
        verbose_name="Proceeds from sale of PPE",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    purchases_of_intangible_assets = MoneyField(
        verbose_name="Purchases of intangible assets",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    purchases_of_financial_assets = MoneyField(
        verbose_name="Purchases of financial assets",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    loans_granted_to_associates_or_subsidiaries = MoneyField(
        verbose_name="Loans granted to associates or subsidiaries",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    lrrfaos = MoneyField(
        verbose_name="Loan repayments received from associates or subsidiaries",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    interest_received = MoneyField(
        verbose_name="Interest received",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    dividends_received = MoneyField(
        verbose_name="Dividends received",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    investing_other = MoneyField(
        verbose_name="Investing other",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    proceeds_from_sale_of_ordinary_shares = MoneyField(
        verbose_name="Proceeds from sale of ordinary shares",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    purchase_of_treasury_shares = MoneyField(
        verbose_name="Purchase of treasury shares",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    proceeds_from_borrowings = MoneyField(
        verbose_name="Proceeds from borrowings",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    repayments_from_borrowings = MoneyField(
        verbose_name="Repayments from borrowings",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    proceeds_from_loan_from_subsidiary_undertaking = MoneyField(
        verbose_name="Proceeds from loan from subsidiary undertaking",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    dividends_paid_to_organizations_shareholders = MoneyField(
        verbose_name="Dividends paid to organization's shareholders",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    financing_other = MoneyField(
        verbose_name="Financing other",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    cash_at_beginning_of_period = MoneyField(
        verbose_name="Cash at beginning of period",
        blank=True,
        null=True,
        max_digits=20,
        decimal_places=2,
    )
    denorm_always_skip = ("modified_at",)

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def cash_generated_from_operations(self):
        values = (
            self.net_income,
            self.adjustment_for_tax,
            self.depreciation_and_amortization,
            self.investment_income,
            self.interest_expense,
            self.profit_on_sale_of_ppe,
            self.working_capital_changes,
            self.decrease_in_trade_and_other_receivables,
            self.increase_in_inventories,
            self.increase_in_trade_and_other_payables,
        )
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def net_cash_from_operating_activities(self):
        values = (
            self.cash_generated_from_operations,
            self.interest_paid,
            self.income_taxes_paid,
        )
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def net_cash_used_in_investing_activities(self):
        values = (
            self.purchases_of_property_plant_and_equipment,
            self.proceeds_from_sale_of_PPE,
            self.purchases_of_intangible_assets,
            self.purchases_of_financial_assets,
            self.loans_granted_to_associates_or_subsidiaries,
            self.lrrfaos,
            self.interest_received,
            self.dividends_received,
            self.investing_other,
        )
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def net_cash_used_in_financing_activities(self):
        values = (
            self.proceeds_from_sale_of_ordinary_shares,
            self.purchase_of_treasury_shares,
            self.proceeds_from_borrowings,
            self.repayments_from_borrowings,
            self.proceeds_from_loan_from_subsidiary_undertaking,
            self.dividends_paid_to_organizations_shareholders,
            self.financing_other,
        )
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def net_cash_flow(self):
        values = (
            self.net_cash_from_operating_activities,
            self.net_cash_used_in_investing_activities,
            self.net_cash_used_in_financing_activities,
        )
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(MoneyField, blank=True, null=True, max_digits=20, decimal_places=2)
    def cash_at_end_of_period(self):
        values = (self.cash_at_beginning_of_period, self.net_cash_flow)
        values = list(filter(lambda item: item and item.currency.code != "XYZ", values))
        value = partial_sum(values)
        return value

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("net_income")
    def net_income_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.net_income, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("adjustment_for_tax")
    def adjustment_for_tax_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.adjustment_for_tax, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("depreciation_and_amortization")
    def depreciation_and_amortization_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.depreciation_and_amortization, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("investment_income")
    def investment_income_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.investment_income, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("interest_expense")
    def interest_expense_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.interest_expense, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("profit_on_sale_of_ppe")
    def profit_on_sale_of_ppe_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.profit_on_sale_of_ppe, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("working_capital_changes")
    def working_capital_changes_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.working_capital_changes, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("decrease_in_trade_and_other_receivables")
    def decrease_in_trade_and_other_receivables_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(
            self.decrease_in_trade_and_other_receivables, self.year
        )

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("increase_in_inventories")
    def increase_in_inventories_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.increase_in_inventories, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("increase_in_trade_and_other_payables")
    def increase_in_trade_and_other_payables_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(
            self.increase_in_trade_and_other_payables, self.year
        )

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("interest_paid")
    def interest_paid_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.interest_paid, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("income_taxes_paid")
    def income_taxes_paid_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.income_taxes_paid, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("purchases_of_property_plant_and_equipment")
    def purchases_of_property_plant_and_equipment_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(
            self.purchases_of_property_plant_and_equipment, self.year
        )

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("purchases_of_intangible_assets")
    def purchases_of_intangible_assets_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.purchases_of_intangible_assets, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("purchases_of_financial_assets")
    def purchases_of_financial_assets_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.purchases_of_financial_assets, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("loans_granted_to_associates_or_subsidiaries")
    def loans_granted_to_associates_or_subsidiaries_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(
            self.loans_granted_to_associates_or_subsidiaries, self.year
        )

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("interest_received")
    def interest_received_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.interest_received, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("dividends_received")
    def dividends_received_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.dividends_received, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("investing_other")
    def investing_other_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.investing_other, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("proceeds_from_sale_of_ordinary_shares")
    def proceeds_from_sale_of_ordinary_shares_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(
            self.proceeds_from_sale_of_ordinary_shares, self.year
        )

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("purchase_of_treasury_shares")
    def purchase_of_treasury_shares_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.purchase_of_treasury_shares, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("proceeds_from_borrowings")
    def proceeds_from_borrowings_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.proceeds_from_borrowings, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("repayments_from_borrowings")
    def repayments_from_borrowings_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.repayments_from_borrowings, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("proceeds_from_loan_from_subsidiary_undertaking")
    def proceeds_from_loan_from_subsidiary_undertaking_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(
            self.proceeds_from_loan_from_subsidiary_undertaking, self.year
        )

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("dividends_paid_to_organizations_shareholders")
    def dividends_paid_to_organizations_shareholders_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(
            self.dividends_paid_to_organizations_shareholders, self.year
        )

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("financing_other")
    def financing_other_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.financing_other, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("cash_at_beginning_of_period")
    def cash_at_beginning_of_period_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.cash_at_beginning_of_period, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("cash_generated_from_operations")
    def cash_generated_from_operations_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.cash_generated_from_operations, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("net_cash_from_operating_activities")
    def net_cash_from_operating_activities_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(
            self.net_cash_from_operating_activities, self.year
        )

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("net_cash_used_in_investing_activities")
    def net_cash_used_in_investing_activities_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(
            self.net_cash_used_in_investing_activities, self.year
        )

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("net_cash_used_in_financing_activities")
    def net_cash_used_in_financing_activities_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(
            self.net_cash_used_in_financing_activities, self.year
        )

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("net_cash_flow")
    def net_cash_flow_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.net_cash_flow, self.year)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("cash_at_end_of_period")
    def cash_at_end_of_period_usd(self):
        from .utils import convert_to_usd_year_avg

        return convert_to_usd_year_avg(self.cash_at_end_of_period, self.year)


class FinancialRatio(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="financial_ratios", on_delete=models.CASCADE
    )
    assessor = models.ForeignKey(
        Assessor,
        related_name="financial_ratios",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    year = models.PositiveSmallIntegerField()
    denorm_always_skip = ("modified_at",)

    @denormalized(
        models.ForeignKey,
        ProfitLossStatement,
        blank=True,
        null=True,
        related_name="_financial_ratios",
        on_delete=models.SET_NULL,
    )
    def _profitlossstatement(self):
        try:
            return self.assessment.profitlossstatements.get(year=self.year)
        except ProfitLossStatement.DoesNotExist:
            return None

    @denormalized(
        models.ForeignKey,
        ProfitLossStatement,
        blank=True,
        null=True,
        related_name="_previous_financial_ratios",
        on_delete=models.SET_NULL,
    )
    def _profitlossstatement_previous_year(self):
        try:
            return self.assessment.profitlossstatements.get(year=self.year - 1)
        except ProfitLossStatement.DoesNotExist:
            return None

    @denormalized(
        models.ForeignKey,
        BalanceSheet,
        blank=True,
        null=True,
        related_name="_financial_ratios",
        on_delete=models.SET_NULL,
    )
    def _balancesheet(self):
        try:
            return self.assessment.balancesheets.get(year=self.year)
        except BalanceSheet.DoesNotExist:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=1
    )
    @depend_on("_profitlossstatement__total_sales")
    @depend_on("_profitlossstatement_previous_year__total_sales")
    def revenue_growth(self):
        pl = self._profitlossstatement
        old_pl = self._profitlossstatement_previous_year
        if pl and old_pl and old_pl.total_sales and old_pl.total_sales.amount:
            return (
                (
                    ((pl.total_sales and pl.total_sales.amount) or 0)
                    / old_pl.total_sales.amount
                )
                - 1
            ) * 100
        else:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=1
    )
    @depend_on("_profitlossstatement__net_profit")
    @depend_on("_profitlossstatement_previous_year__net_profit")
    def net_profit_growth(self):
        pl = self._profitlossstatement
        old_pl = self._profitlossstatement_previous_year
        if pl and old_pl and old_pl.net_profit and old_pl.net_profit.amount:
            growth = (
                (
                    ((pl.net_profit and pl.net_profit.amount) or 0)
                    / old_pl.net_profit.amount
                )
                - 1
            ) * 100
            if (
                old_pl.net_profit.amount
                and not (old_pl.net_profit.amount.is_nan())
                and old_pl.net_profit.amount > 0
            ):
                response = growth
                if response > Decimal("99999999.9"):
                    response = Decimal("99999999.9")
            else:
                response = -1 * growth
                if response < Decimal("-99999999.9"):
                    response = Decimal("-99999999.9")
            return response
        else:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=1
    )
    @depend_on("_profitlossstatement__total_sales")
    @depend_on("_profitlossstatement__gross_profit")
    def gross_margin(self):
        pl = self._profitlossstatement
        if pl and pl.total_sales and pl.total_sales.amount >= Decimal("0.1"):
            return (
                ((pl.gross_profit and pl.gross_profit.amount) or 0)
                / pl.total_sales.amount
            ) * 100
        else:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=1
    )
    @depend_on("_profitlossstatement__total_sales")
    @depend_on("_profitlossstatement__ebitda")
    def operating_profit_margin(self):
        pl = self._profitlossstatement
        if pl and pl.total_sales and pl.total_sales.amount >= Decimal("0.1"):
            return (
                ((pl.ebitda and pl.ebitda.amount) or 0) / pl.total_sales.amount
            ) * 100
        else:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=1
    )
    @depend_on("_profitlossstatement__total_sales")
    @depend_on("_profitlossstatement__net_profit")
    def net_profit_margin(self):
        pl = self._profitlossstatement
        if pl and pl.total_sales and pl.total_sales.amount >= Decimal("0.1"):
            return (
                ((pl.net_profit and pl.net_profit.amount) or 0) / pl.total_sales.amount
            ) * 100
        else:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=10, decimal_places=1
    )
    def return_on_capital(self):
        return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=1
    )
    @depend_on("_profitlossstatement__net_profit")
    @depend_on("_balancesheet__net_assets")
    def return_on_equity(self):
        pl = self._profitlossstatement
        bs = self._balancesheet
        if pl and bs and bs.net_assets and bs.net_assets.amount:
            return (
                ((pl.net_profit and pl.net_profit.amount) or 0) / bs.net_assets.amount
            ) * 100
        else:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=1
    )
    @depend_on("_profitlossstatement__ebit")
    @depend_on("_profitlossstatement__income_tax_expense")
    @depend_on("_balancesheet__total_assets")
    def return_on_assets(self):
        pl = self._profitlossstatement
        bs = self._balancesheet
        if pl and bs and bs.total_assets and bs.total_assets.amount:
            return (
                (
                    ((pl.ebit and pl.ebit.amount) or 0)
                    - ((pl.income_tax_expense and pl.income_tax_expense.amount) or 0)
                )
                / bs.total_assets.amount
            ) * 100
        else:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=1
    )
    @depend_on("_profitlossstatement__total_sales")
    @depend_on("_balancesheet__total_assets")
    def asset_turnover(self):
        pl = self._profitlossstatement
        bs = self._balancesheet
        if pl and bs and bs.total_assets and bs.total_assets.amount:
            return (
                (pl.total_sales and pl.total_sales.amount) or 0
            ) / bs.total_assets.amount
        else:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=1
    )
    @depend_on("_profitlossstatement__total_sales")
    @depend_on("_balancesheet__net_working_capital")
    def working_capital_turnover(self):
        pl = self._profitlossstatement
        bs = self._balancesheet
        if pl and bs and bs.net_working_capital and bs.net_working_capital.amount:
            return (
                (pl.total_sales and pl.total_sales.amount) or 0
            ) / bs.net_working_capital.amount
        else:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=10, decimal_places=1
    )
    @depend_on("_balancesheet__short_term_loans")
    @depend_on("_balancesheet__long_term_loans")
    @depend_on("_balancesheet__other_non_current_liabilities")
    @depend_on("_balancesheet__total_assets")
    def debt_to_assets_ratio(self):
        bs = self._balancesheet
        if self.assessment.tool.version_tuple >= (3, 0, 9):
            # 3.0.9 changed the formula
            if (
                bs
                and (
                    bs.short_term_loans
                    or bs.long_term_loans
                    or bs.other_non_current_liabilities
                )
                and bs.total_assets
                and bs.total_assets.amount
                and (
                    bs.total_assets.currency == bs.short_term_loans.currency
                    if (bs.short_term_loans and bs.short_term_loans.amount)
                    else bs.total_assets.currency
                )
                and (
                    bs.total_assets.currency == bs.long_term_loans.currency
                    if (bs.long_term_loans and bs.long_term_loans.amount)
                    else bs.total_assets.currency
                )
                and (
                    bs.total_assets.currency
                    == bs.other_non_current_liabilities.currency
                    if (
                        bs.other_non_current_liabilities
                        and bs.other_non_current_liabilities.amount
                    )
                    else bs.total_assets.currency
                )
            ):
                return (
                    (
                        (bs.short_term_loans.amount if bs.short_term_loans else 0)
                        + (bs.long_term_loans.amount if bs.long_term_loans else 0)
                        + (
                            bs.other_non_current_liabilities.amount
                            if bs.other_non_current_liabilities
                            else 0
                        )
                    )
                    / bs.total_assets.amount
                ) * 100
            else:
                return None
        else:
            # <= 3.0.8 formula
            if (
                bs
                and (bs.short_term_loans or bs.long_term_loans)
                and bs.total_assets
                and bs.total_assets.amount
                and (
                    bs.total_assets.currency == bs.short_term_loans.currency
                    if (bs.short_term_loans and bs.short_term_loans.amount)
                    else bs.total_assets.currency
                )
                and (
                    bs.total_assets.currency == bs.long_term_loans.currency
                    if (bs.long_term_loans and bs.long_term_loans.amount)
                    else bs.total_assets.currency
                )
            ):
                return (
                    (
                        ((bs.short_term_loans and bs.short_term_loans.amount) or 0)
                        + ((bs.long_term_loans and bs.long_term_loans.amount) or 0)
                    )
                    / bs.total_assets.amount
                    * 100
                )
            else:
                return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=10, decimal_places=1
    )
    @depend_on("_balancesheet__short_term_loans")
    @depend_on("_balancesheet__long_term_loans")
    @depend_on("_balancesheet__other_non_current_liabilities")
    @depend_on("_balancesheet__total_equity")
    def debt_to_equity_ratio(self):
        bs = self._balancesheet
        if self.assessment.tool.version_tuple >= (3, 0, 9):
            # 3.0.9 changed the formula
            if (
                bs
                and (
                    bs.short_term_loans
                    or bs.long_term_loans
                    or bs.other_non_current_liabilities
                )
                and bs.total_equity
                and bs.total_equity.amount
                and (
                    bs.total_equity.currency == bs.short_term_loans.currency
                    if (bs.short_term_loans and bs.short_term_loans.amount)
                    else bs.total_equity.currency
                )
                and (
                    bs.total_equity.currency == bs.long_term_loans.currency
                    if (bs.long_term_loans and bs.long_term_loans.amount)
                    else bs.total_equity.currency
                )
                and (
                    bs.total_equity.currency
                    == bs.other_non_current_liabilities.currency
                    if (
                        bs.other_non_current_liabilities
                        and bs.other_non_current_liabilities.amount
                    )
                    else bs.total_equity.currency
                )
            ):
                return (
                    (
                        (bs.short_term_loans.amount if bs.short_term_loans else 0)
                        + (bs.long_term_loans.amount if bs.long_term_loans else 0)
                        + (
                            bs.other_non_current_liabilities.amount
                            if bs.other_non_current_liabilities
                            else 0
                        )
                    )
                    / bs.total_equity.amount
                ) * 100
            else:
                return None
        else:
            # <= 3.0.8 formula
            if (
                bs
                and (bs.short_term_loans or bs.long_term_loans)
                and bs.total_equity
                and bs.total_equity.amount
                and (
                    bs.total_equity.currency == bs.short_term_loans.currency
                    if (bs.short_term_loans and bs.short_term_loans.amount)
                    else bs.total_equity.currency
                )
                and (
                    bs.total_equity.currency == bs.long_term_loans.currency
                    if (bs.long_term_loans and bs.long_term_loans.amount)
                    else bs.total_equity.currency
                )
            ):
                return (
                    (bs.short_term_loans.amount + bs.long_term_loans.amount)
                    / bs.total_equity.amount
                ) * 100
            else:
                return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=10, decimal_places=1
    )
    @depend_on("_profitlossstatement__ebit")
    @depend_on("_balancesheet__short_term_loans")
    @depend_on("_balancesheet__long_term_loans")
    @depend_on("_balancesheet__other_non_current_liabilities")
    def debt_coverage_ratio(self):
        bs = self._balancesheet
        pl = self._profitlossstatement
        if self.assessment.tool.version_tuple >= (3, 0, 9):
            # 3.0.9 changed the formula
            if (
                bs
                and pl
                and (
                    bs.short_term_loans
                    or bs.long_term_loans
                    or bs.other_non_current_liabilities
                )
                and pl.ebit
                and pl.ebit.amount
                and (
                    pl.ebit.currency == bs.short_term_loans.currency
                    if (bs.short_term_loans and bs.short_term_loans.amount)
                    else pl.ebit.currency
                )
                and (
                    pl.ebit.currency == bs.long_term_loans.currency
                    if (bs.long_term_loans and bs.long_term_loans.amount)
                    else pl.ebit.currency
                )
                and (
                    pl.ebit.currency == bs.other_non_current_liabilities.currency
                    if (
                        bs.other_non_current_liabilities
                        and bs.other_non_current_liabilities.amount
                    )
                    else pl.ebit.currency
                )
            ):
                return (
                    (
                        ((bs.short_term_loans and bs.short_term_loans.amount) or 0)
                        + ((bs.long_term_loans and bs.long_term_loans.amount) or 0)
                        + (
                            (
                                bs.other_non_current_liabilities
                                and bs.other_non_current_liabilities.amount
                            )
                            or 0
                        )
                    )
                    / pl.ebit.amount
                    * 100
                )
            else:
                return None
        else:
            # <= 3.0.8 formula
            if (
                bs
                and pl
                and (bs.short_term_loans or bs.long_term_loans)
                and pl.ebit
                and pl.ebit.amount
                and (
                    pl.ebit.currency == bs.short_term_loans.currency
                    if (bs.short_term_loans and bs.short_term_loans.amount)
                    else pl.ebit.currency
                )
                and (
                    pl.ebit.currency == bs.long_term_loans.currency
                    if (bs.long_term_loans and bs.long_term_loans.amount)
                    else pl.ebit.currency
                )
            ):
                return (
                    ((bs.short_term_loans and bs.short_term_loans.amount) or 0)
                    + ((bs.long_term_loans and bs.long_term_loans.amount) or 0)
                    / pl.ebit.amount
                ) * 100
            else:
                return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=10, decimal_places=1
    )
    @depend_on("_profitlossstatement__interest_expense")
    @depend_on("_profitlossstatement__ebitda")
    def debt_servicing(self):
        pl = self._profitlossstatement
        if pl and pl.interest_expense and pl.interest_expense.amount:
            return abs(
                ((pl.ebitda and pl.ebitda.amount) or 0) / pl.interest_expense.amount
            ).quantize(Decimal("0.0"), rounding=ROUND_HALF_UP)
        else:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=10, decimal_places=1
    )
    @depend_on("_balancesheet__total_current_liabilities")
    @depend_on("_balancesheet__total_current_assets")
    def current_ratio(self):
        bs = self._balancesheet
        if bs and bs.total_current_liabilities and bs.total_current_liabilities.amount:
            value = (
                ((bs.total_current_assets and bs.total_current_assets.amount) or 0)
                / bs.total_current_liabilities.amount
            ).quantize(Decimal("0.0"), rounding=ROUND_HALF_UP)
            if value > Decimal("99999999.9"):
                value = Decimal("99999999.9")
            elif value < Decimal("-99999999.9"):
                value = Decimal("-99999999.9")
            return value
        else:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=10, decimal_places=1
    )
    @depend_on("_balancesheet__inventories")
    @depend_on("_balancesheet__total_current_liabilities")
    @depend_on("_balancesheet__total_current_assets")
    def quick_ratio(self):
        bs = self._balancesheet
        if bs and bs.total_current_liabilities and bs.total_current_liabilities.amount:
            value = (
                (
                    ((bs.total_current_assets and bs.total_current_assets.amount) or 0)
                    - ((bs.inventories and bs.inventories.amount) or 0)
                )
                / bs.total_current_liabilities.amount
            ).quantize(Decimal("0.0"), rounding=ROUND_HALF_UP)
            if value > Decimal("99999999.9"):
                value = Decimal("99999999.9")
            elif value < Decimal("-99999999.9"):
                value = Decimal("-99999999.9")
            return value
        else:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=1
    )
    @depend_on("_profitlossstatement__total_cost_of_sales")
    @depend_on("_balancesheet__average_inventory")
    def days_inventory_outstanding(self):
        pl = self._profitlossstatement
        bs = self._balancesheet
        if pl and bs and pl.total_cost_of_sales and pl.total_cost_of_sales.amount:
            return int(
                round(
                    (
                        ((bs.average_inventory and bs.average_inventory.amount) or 0)
                        / pl.total_cost_of_sales.amount
                    )
                    * 365
                )
            )
        else:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=1
    )
    @depend_on("_profitlossstatement__total_sales")
    @depend_on("_balancesheet__average_receivables")
    def days_sales_outstanding(self):
        pl = self._profitlossstatement
        bs = self._balancesheet
        if pl and bs and pl.total_sales and pl.total_sales.amount:
            return int(
                round(
                    (
                        (
                            (bs.average_receivables and bs.average_receivables.amount)
                            or 0
                        )
                        / pl.total_sales.amount
                    )
                    * 365
                )
            )
        else:
            return None

    class Meta:
        unique_together = (("assessment", "year"),)
        ordering = ("-year",)


class FinancialScore(BaseModel):
    status = models.DecimalField(max_digits=2, decimal_places=1, blank=True, null=True)
    trend = models.DecimalField(max_digits=2, decimal_places=1, blank=True, null=True)
    volatility = models.DecimalField(
        max_digits=2, decimal_places=1, blank=True, null=True
    )
    denorm_always_skip = ("modified_at",)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=2, decimal_places=1
    )
    def total_score(self):
        weights = (
            Decimal("0.5") if self.status else Decimal("0"),
            Decimal("0.25") if self.trend else Decimal("0"),
            Decimal("0.25") if self.volatility else Decimal("0"),  # FIXME
        )
        values = (
            Decimal(self.status) if self.status else Decimal("0"),
            Decimal(self.trend) if self.trend else Decimal("0"),
            Decimal(self.volatility) if self.volatility else Decimal("0"),
        )
        try:
            return weighted_average(weights, values)
        except InvalidOperation:
            return None

    @denormalized(
        models.ForeignKey,
        "Assessment",
        related_name="+",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
    )
    @depend_on("financialscores0__assessment_id")
    @depend_on("financialscores1__assessment_id")
    @depend_on("financialscores2__assessment_id")
    @depend_on("financialscores3__assessment_id")
    @depend_on("financialscores4__assessment_id")
    @depend_on("financialscores5__assessment_id")
    @depend_on("financialscores6__assessment_id")
    @depend_on("financialscores7__assessment_id")
    @depend_on("financialscores8__assessment_id")
    @depend_on("financialscores9__assessment_id")
    @depend_on("financialscores10__assessment_id")
    @depend_on("financialscores11__assessment_id")
    @depend_on("financialscores12__assessment_id")
    @depend_on("financialscores13__assessment_id")
    @depend_on("financialscores14__assessment_id")
    @depend_on("financialscores15__assessment_id")
    @depend_on("financialscores16__assessment_id")
    @depend_on("financialscores17__assessment_id")
    @depend_on("financialscores0__revenue_growth_id")
    @depend_on("financialscores1__net_profit_growth_id")
    @depend_on("financialscores2__gross_margin_id")
    @depend_on("financialscores3__operating_profit_margin_id")
    @depend_on("financialscores4__net_profit_margin_id")
    @depend_on("financialscores5__return_on_equity_id")
    @depend_on("financialscores6__return_on_assets_id")
    @depend_on("financialscores7__asset_turnover_id")
    @depend_on("financialscores8__working_capital_turnover_id")
    @depend_on("financialscores9__debt_to_assets_ratio_id")
    @depend_on("financialscores10__debt_to_equity_ratio_id")
    @depend_on("financialscores11__debt_coverage_ratio_id")
    @depend_on("financialscores12__debt_servicing_id")
    @depend_on("financialscores13__current_ratio_id")
    @depend_on("financialscores14__quick_ratio_id")
    @depend_on("financialscores15__days_inventory_outstanding_id")
    @depend_on("financialscores16__days_sales_outstanding_id")
    @depend_on("financialscores17__return_on_capital_id")
    def _assessment(self):

        names = [
            "financialscores0",
            "financialscores1",
            "financialscores2",
            "financialscores3",
            "financialscores4",
            "financialscores5",
            "financialscores6",
            "financialscores7",
            "financialscores8",
            "financialscores9",
            "financialscores10",
            "financialscores11",
            "financialscores12",
            "financialscores13",
            "financialscores14",
            "financialscores15",
            "financialscores16",
            "financialscores17",
        ]
        parent = None
        for name in names:
            try:
                parent = getattr(self, name)
            except FinancialScores.DoesNotExist:
                pass
        if parent:
            return parent.assessment
        else:
            return None

    def clean(self):
        if (self.trend is None and self.volatility is not None) or (
            self.trend is not None and self.volatility is None
        ):
            raise ValidationError(
                "Either both trend and volatility need to be filled, or neither"
            )

    class Meta:
        ordering = ("pk",)


class FinancialScores(BaseModel):
    assessment = models.OneToOneField(
        Assessment, related_name="financial_scores", on_delete=models.CASCADE
    )
    revenue_growth = models.OneToOneField(
        FinancialScore,
        blank=True,
        null=True,
        related_name="financialscores0",
        on_delete=models.CASCADE,
    )
    net_profit_growth = models.OneToOneField(
        FinancialScore,
        blank=True,
        null=True,
        related_name="financialscores1",
        on_delete=models.CASCADE,
    )
    gross_margin = models.OneToOneField(
        FinancialScore,
        blank=True,
        null=True,
        related_name="financialscores2",
        on_delete=models.CASCADE,
    )
    operating_profit_margin = models.OneToOneField(
        FinancialScore,
        blank=True,
        null=True,
        related_name="financialscores3",
        on_delete=models.CASCADE,
    )
    net_profit_margin = models.OneToOneField(
        FinancialScore,
        blank=True,
        null=True,
        related_name="financialscores4",
        on_delete=models.CASCADE,
    )
    return_on_capital = models.OneToOneField(
        FinancialScore,
        blank=True,
        null=True,
        related_name="financialscores17",
        on_delete=models.CASCADE,
    )
    return_on_equity = models.OneToOneField(
        FinancialScore,
        blank=True,
        null=True,
        related_name="financialscores5",
        on_delete=models.CASCADE,
    )
    return_on_assets = models.OneToOneField(
        FinancialScore,
        blank=True,
        null=True,
        related_name="financialscores6",
        on_delete=models.CASCADE,
    )
    asset_turnover = models.OneToOneField(
        FinancialScore,
        blank=True,
        null=True,
        related_name="financialscores7",
        on_delete=models.CASCADE,
    )
    working_capital_turnover = models.OneToOneField(
        FinancialScore,
        blank=True,
        null=True,
        related_name="financialscores8",
        on_delete=models.CASCADE,
    )
    debt_to_assets_ratio = models.OneToOneField(
        FinancialScore,
        blank=True,
        null=True,
        related_name="financialscores9",
        on_delete=models.CASCADE,
    )
    debt_to_equity_ratio = models.OneToOneField(
        FinancialScore,
        blank=True,
        null=True,
        related_name="financialscores10",
        on_delete=models.CASCADE,
    )
    debt_coverage_ratio = models.OneToOneField(
        FinancialScore,
        blank=True,
        null=True,
        related_name="financialscores11",
        on_delete=models.CASCADE,
    )
    debt_servicing = models.OneToOneField(
        FinancialScore,
        blank=True,
        null=True,
        related_name="financialscores12",
        on_delete=models.CASCADE,
    )
    current_ratio = models.OneToOneField(
        FinancialScore,
        blank=True,
        null=True,
        related_name="financialscores13",
        on_delete=models.CASCADE,
    )
    quick_ratio = models.OneToOneField(
        FinancialScore,
        blank=True,
        null=True,
        related_name="financialscores14",
        on_delete=models.CASCADE,
    )
    days_inventory_outstanding = models.OneToOneField(
        FinancialScore,
        blank=True,
        null=True,
        related_name="financialscores15",
        on_delete=models.CASCADE,
    )
    days_sales_outstanding = models.OneToOneField(
        FinancialScore,
        blank=True,
        null=True,
        related_name="financialscores16",
        on_delete=models.CASCADE,
    )

    def create_missing_subobjects(self):
        changed = False
        for field_name in [field.name for field in FinancialScores._meta.get_fields()]:
            if field_name.endswith("_id"):
                continue
            field = FinancialScores._meta.get_field(field_name)
            if (
                isinstance(field, models.OneToOneField)
                and field.related_model == FinancialScore
            ):
                if getattr(self, field_name) is None:
                    setattr(self, field_name, FinancialScore.objects.create())
                    changed = True
        if changed:
            self.save()

    def pdf_presentation(self):
        Header = namedtuple("Header", ["title", "score"])
        structure = [
            [
                ugettext("Growth"),
                [
                    [ugettext("Revenue growth (%)"), "revenue_growth"],
                    [ugettext("Net profit growth (%)"), "net_profit_growth"],
                ],
            ],
            [
                ugettext("Profitability"),
                [
                    [ugettext("Gross margin (%)"), "gross_margin"],
                    [
                        ugettext("Operating profit margin (%)"),
                        "operating_profit_margin",
                    ],
                    [ugettext("Net profit margin (%)"), "net_profit_margin"],
                    [ugettext("Return on equity (ROE) (%)"), "return_on_equity"],
                    [ugettext("Return on assets (ROA) (%)"), "return_on_assets"],
                ],
            ],
            [
                ugettext("Productivity"),
                [
                    [ugettext("Asset turnover"), "asset_turnover"],
                    [ugettext("Working capital turnover"), "working_capital_turnover"],
                ],
            ],
            [
                ugettext("Solvency & gearing"),
                [
                    [ugettext("Debt-to-assets ratio (%)"), "debt_to_assets_ratio"],
                    [ugettext("Debt-to-equity ratio (%)"), "debt_to_equity_ratio"],
                    [ugettext("Debt to EBIT ratio (%)"), "debt_coverage_ratio"],
                    [ugettext("Interest coverage ratio"), "debt_servicing"],
                ],
            ],
            [
                ugettext("Liquidity"),
                [
                    [ugettext("Current ratio"), "current_ratio"],
                    [ugettext("Quick ratio"), "quick_ratio"],
                ],
            ],
            [
                ugettext("Working capital management"),
                [
                    [
                        ugettext("Days inventory outstanding"),
                        "days_inventory_outstanding",
                    ],
                    [ugettext("Days sales outstanding"), "days_sales_outstanding"],
                ],
            ],
        ]
        financial_ratios = list(self.assessment.financial_ratios.all())
        return OrderedDict(
            [
                (
                    Header(
                        heading,
                        "{:.1f}".format(
                            mean(
                                [
                                    getattr(self, attribute).total_score
                                    for title, attribute in rows
                                ]
                            )
                        ),
                    ),
                    [
                        [title]
                        + [getattr(item, attribute) for item in financial_ratios]
                        + [
                            getattr(self, attribute).status,
                            getattr(self, attribute).trend,
                            getattr(self, attribute).volatility,
                            getattr(self, attribute).total_score,
                        ]
                        for title, attribute in rows
                    ],
                )
                for heading, rows in structure
            ]
        )

    class Meta:
        ordering = ("pk",)


class RatioScores(BaseModel):
    assessment = models.OneToOneField(
        Assessment, related_name="ratio_scores", on_delete=models.CASCADE
    )
    accountant_notes = models.TextField(blank=True)
    denorm_always_skip = ("modified_at",)

    def get_average_value(self, financial_scores):
        """
        Convenience function to compute average of named financial score attributes
        """
        values = []
        for attribute in financial_scores:
            financial_score = getattr(self, attribute)
            if financial_score and financial_score.total_score:
                values.append(financial_score.total_score)
        if values:
            return sum(values) / len(values)
        else:
            return None

    @denormalized(
        models.ForeignKey,
        FinancialScores,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    def financial_scores(self):
        try:
            return self.assessment.financial_scores
        except (Assessment.DoesNotExist, FinancialScores.DoesNotExist):
            return None

    @denormalized(
        models.ForeignKey,
        FinancialScore,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    def net_profit_growth(self):
        if self.financial_scores is not None:
            return self.financial_scores.net_profit_growth

    @denormalized(
        models.ForeignKey,
        FinancialScore,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    def revenue_growth(self):
        if self.financial_scores is not None:
            return self.financial_scores.revenue_growth

    @denormalized(
        models.ForeignKey,
        FinancialScore,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    def gross_margin(self):
        if self.financial_scores is not None:
            return self.financial_scores.gross_margin

    @denormalized(
        models.ForeignKey,
        FinancialScore,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    def operating_profit_margin(self):
        if self.financial_scores is not None:
            return self.financial_scores.operating_profit_margin

    @denormalized(
        models.ForeignKey,
        FinancialScore,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    def return_on_assets(self):
        if self.financial_scores is not None:
            return self.financial_scores.return_on_assets

    @denormalized(
        models.ForeignKey,
        FinancialScore,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    def return_on_equity(self):
        if self.financial_scores is not None:
            return self.financial_scores.return_on_equity

    @denormalized(
        models.ForeignKey,
        FinancialScore,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    def asset_turnover(self):
        if self.financial_scores is not None:
            return self.financial_scores.asset_turnover

    @denormalized(
        models.ForeignKey,
        FinancialScore,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    def working_capital_turnover(self):
        if self.financial_scores is not None:
            return self.financial_scores.working_capital_turnover

    @denormalized(
        models.ForeignKey,
        FinancialScore,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    def debt_to_assets_ratio(self):
        if self.financial_scores is not None:
            return self.financial_scores.debt_to_assets_ratio

    @denormalized(
        models.ForeignKey,
        FinancialScore,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    def debt_servicing(self):
        if self.financial_scores is not None:
            return self.financial_scores.debt_servicing

    @denormalized(
        models.ForeignKey,
        FinancialScore,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    def debt_to_equity_ratio(self):
        if self.financial_scores is not None:
            return self.financial_scores.debt_to_equity_ratio

    @denormalized(
        models.ForeignKey,
        FinancialScore,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    def debt_coverage_ratio(self):
        if self.financial_scores is not None:
            return self.financial_scores.debt_coverage_ratio

    @denormalized(
        models.ForeignKey,
        FinancialScore,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    def current_ratio(self):
        if self.financial_scores is not None:
            return self.financial_scores.current_ratio

    @denormalized(
        models.ForeignKey,
        FinancialScore,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    def quick_ratio(self):
        if self.financial_scores is not None:
            return self.financial_scores.quick_ratio

    @denormalized(
        models.ForeignKey,
        FinancialScore,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    def days_sales_outstanding(self):
        if self.financial_scores is not None:
            return self.financial_scores.days_sales_outstanding

    @denormalized(
        models.ForeignKey,
        FinancialScore,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    def days_inventory_outstanding(self):
        if self.financial_scores is not None:
            return self.financial_scores.days_inventory_outstanding

    @denormalized(
        models.ForeignKey,
        FinancialScore,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.SET_NULL,
    )
    def net_profit_margin(self):
        if self.financial_scores is not None:
            return self.financial_scores.net_profit_margin

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=6, decimal_places=1
    )
    @depend_on("net_profit_growth__total_score")
    @depend_on("revenue_growth__total_score")
    def growth(self):
        try:
            if self.assessment.tool.version_tuple >= (3, 0, 9):
                # 3.0.9 changed the formula
                return self.get_average_value(["revenue_growth"])
            else:
                # <= 3.0.8 formula
                return self.get_average_value(["revenue_growth", "net_profit_growth"])
        except Assessment.DoesNotExist:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=6, decimal_places=1
    )
    @depend_on("gross_margin__total_score")
    @depend_on("operating_profit_margin__total_score")
    @depend_on("return_on_assets__total_score")
    @depend_on("return_on_equity__total_score")
    @depend_on("net_profit_margin__total_score")
    def profitability(self):
        try:
            if self.assessment.tool.version_tuple >= (3, 0, 9):
                # 3.0.9 changed the formula
                return self.get_average_value(
                    ["gross_margin", "operating_profit_margin", "return_on_assets"]
                )
            else:
                # <= 3.0.8 formula
                return self.get_average_value(
                    [
                        "operating_profit_margin",
                        "net_profit_margin",
                        "return_on_equity",
                        "return_on_assets",
                    ]
                )
        except Assessment.DoesNotExist:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=6, decimal_places=1
    )
    @depend_on("asset_turnover__total_score")
    @depend_on("working_capital_turnover__total_score")
    def productivity(self):
        try:
            if self.assessment.tool.version_tuple >= (3, 0, 9):
                # 3.0.9 changed the formula
                return self.get_average_value(["asset_turnover"])
            else:
                # <= 3.0.8 formula
                return self.get_average_value(
                    ["asset_turnover", "working_capital_turnover"]
                )
        except Assessment.DoesNotExist:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=6, decimal_places=1
    )
    @depend_on("debt_to_assets_ratio__total_score")
    @depend_on("debt_servicing__total_score")
    @depend_on("debt_to_equity_ratio__total_score")
    @depend_on("debt_coverage_ratio__total_score")
    def solvency(self):
        try:
            if self.assessment.tool.version_tuple >= (3, 0, 9):
                # 3.0.9 changed the formula
                return self.get_average_value(
                    ["debt_to_assets_ratio", "debt_servicing"]
                )
            else:
                # <= 3.0.8 formula
                return self.get_average_value(
                    [
                        "debt_to_assets_ratio",
                        "debt_to_equity_ratio",
                        "debt_coverage_ratio",
                        "debt_servicing",
                    ]
                )
        except Assessment.DoesNotExist:
            return None

    @depend_on("current_ratio__total_score")
    @depend_on("quick_ratio__total_score")
    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=6, decimal_places=1
    )
    def liquidity(self):
        return self.get_average_value(["current_ratio", "quick_ratio"])

    @depend_on("days_sales_outstanding__total_score")
    @depend_on("days_inventory_outstanding__total_score")
    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=6, decimal_places=1
    )
    def working_capital(self):
        try:
            if self.assessment.tool.version_tuple >= (3, 0, 9):
                # 3.0.9 changed the formula
                return self.get_average_value(["days_sales_outstanding"])
            else:
                # <= 3.0.8 formula
                return self.get_average_value(
                    ["days_inventory_outstanding", "days_sales_outstanding"]
                )
        except Assessment.DoesNotExist:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=6, decimal_places=1
    )
    def total_score(self):
        values = [
            self.growth,
            self.profitability,
            self.productivity,
            self.solvency,
            self.liquidity,
            self.working_capital,
        ]
        base_weights = [
            Decimal("0.15"),
            Decimal("0.2"),
            Decimal("0.2"),
            Decimal("0.2"),
            Decimal("0.15"),
            Decimal("0.1"),
        ]
        weights = [
            weight if value else None for (weight, value) in zip(base_weights, values)
        ]
        values = [item for item in values if item is not None]
        weights = [item for item in weights if item is not None]
        total_weight = sum(weights)
        if not total_weight:
            return None
        weights = [weight / total_weight for weight in weights]
        return weighted_average(weights=weights, values=values)

    class Meta:
        ordering = ("pk",)


class GeneralCheck(BaseModel):
    NOT_GOOD = 0
    POTENTIAL_ISSUE = 1
    GOOD = 2
    CHOICES = (
        (NOT_GOOD, "Low Quality"),
        (POTENTIAL_ISSUE, "Possible Issues"),
        (GOOD, "Good Quality"),
    )
    available = models.BooleanField(blank=True, null=True)
    quality = models.PositiveSmallIntegerField(choices=CHOICES, blank=True, null=True)
    notes = models.TextField(blank=True)
    denorm_always_skip = ("modified_at",)

    @denormalized(
        models.ForeignKey,
        "Assessment",
        related_name="+",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
    )
    @depend_on("generalchecks0__assessment_id")
    @depend_on("generalchecks1__assessment_id")
    @depend_on("generalchecks2__assessment_id")
    @depend_on("generalchecks3__assessment_id")
    @depend_on("generalchecks4__assessment_id")
    @depend_on("generalchecks5__assessment_id")
    @depend_on("generalchecks6__assessment_id")
    @depend_on("generalchecks7__assessment_id")
    @depend_on("generalchecks8__assessment_id")
    @depend_on("generalchecks9__assessment_id")
    @depend_on("generalchecks10__assessment_id")
    @depend_on("generalchecks11__assessment_id")
    @depend_on("generalchecks12__assessment_id")
    @depend_on("generalchecks13__assessment_id")
    @depend_on("generalchecks14__assessment_id")
    @depend_on("generalchecks15__assessment_id")
    @depend_on("generalchecks16__assessment_id")
    @depend_on("generalchecks17__assessment_id")
    @depend_on("generalchecks18__assessment_id")
    def _assessment(self):
        names = [
            "generalchecks0",
            "generalchecks1",
            "generalchecks2",
            "generalchecks3",
            "generalchecks4",
            "generalchecks5",
            "generalchecks6",
            "generalchecks7",
            "generalchecks8",
            "generalchecks9",
            "generalchecks10",
            "generalchecks11",
            "generalchecks12",
            "generalchecks13",
            "generalchecks14",
            "generalchecks15",
            "generalchecks16",
            "generalchecks17",
            "generalchecks18",
        ]
        parent = None
        for name in names:
            try:
                parent = getattr(self, name)
            except GeneralChecks.DoesNotExist:
                pass
        if parent:
            return parent.assessment
        else:
            return None

    class Meta:
        ordering = ("pk",)


class GeneralChecks(BaseModel):
    assessment = models.OneToOneField(
        Assessment, related_name="general_checks", on_delete=models.CASCADE
    )
    financial_statements = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks0",
        on_delete=models.CASCADE,
    )
    balance_sheet = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks1",
        on_delete=models.CASCADE,
    )
    profit_and_loss = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks2",
        on_delete=models.CASCADE,
    )
    cash_flow = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks3",
        on_delete=models.CASCADE,
    )
    cash_flow_projections = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks4",
        on_delete=models.CASCADE,
    )
    accepted_format = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks5",
        on_delete=models.CASCADE,
    )
    audited = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks6",
        on_delete=models.CASCADE,
    )
    bank_statements = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks7",
        on_delete=models.CASCADE,
    )
    collateral = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks8",
        on_delete=models.CASCADE,
    )
    asset_register = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks9",
        on_delete=models.CASCADE,
    )
    depreciation_policy = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks10",
        on_delete=models.CASCADE,
    )
    external_valuation = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks11",
        on_delete=models.CASCADE,
    )
    record_keeping = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks12",
        on_delete=models.CASCADE,
    )
    software_system = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks13",
        on_delete=models.CASCADE,
    )
    loan_history = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks14",
        on_delete=models.CASCADE,
    )
    loan_recorded_in_financial_statement = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks15",
        on_delete=models.CASCADE,
    )
    loan_default_known = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks16",
        on_delete=models.CASCADE,
    )
    grant_history = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks17",
        on_delete=models.CASCADE,
    )
    grants_recorded_in_financial_statement = models.OneToOneField(
        GeneralCheck,
        blank=True,
        null=True,
        related_name="generalchecks18",
        on_delete=models.CASCADE,
    )
    quality_of_the_financial_statements = models.TextField(blank=True)

    def create_missing_subobjects(self):
        changed = False
        for field_name in [field.name for field in GeneralChecks._meta.get_fields()]:
            if field_name.endswith("_id"):
                continue
            field = GeneralChecks._meta.get_field(field_name)
            if (
                isinstance(field, models.OneToOneField)
                and field.related_model == GeneralCheck
            ):
                if getattr(self, field_name) is None:
                    setattr(self, field_name, GeneralCheck.objects.create())
                    changed = True
        if changed:
            self.save()

    class Meta:
        ordering = ("pk",)


class Product(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="products", on_delete=models.CASCADE
    )
    name = models.TextField(blank=True)  # this is the one in export_production_data
    priority = models.TextField(blank=True)
    fao_item_code = models.TextField(blank=True)
    type = models.TextField(blank=True)  # this does not come from a dropdown
    global_product_type = models.TextField(blank=True)
    fao_category_code = models.TextField(blank=True)
    production_unit = models.TextField(blank=True)
    unit = models.TextField(blank=True)
    quality = models.TextField(blank=True)
    land_size_under_production = models.DecimalField(
        max_digits=9,
        decimal_places=2,
        blank=True,
        null=True,
        validators=[MinValueValidator(0), MaxValueValidator(100000)],
    )
    land_size_under_production_unit = models.TextField(blank=True)
    land_used_for_product = models.DecimalField(
        max_digits=20, decimal_places=2, blank=True, null=True
    )
    land_unit = models.TextField(blank=True)
    number_of_production_units = models.PositiveIntegerField(blank=True, null=True)
    number_of_harvest_cycles_per_year = models.PositiveSmallIntegerField(
        blank=True, null=True
    )
    average_age_of_production_unit = models.DecimalField(
        max_digits=20, decimal_places=2, blank=True, null=True
    )
    average_production_unit_lifetime = models.DecimalField(
        max_digits=20, decimal_places=2, blank=True, null=True
    )
    average_yield = models.DecimalField(
        max_digits=20, decimal_places=2, blank=True, null=True
    )
    average_yield_unit = models.TextField(blank=True)
    percent_dead_diseased_production_units = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    percent_certified_crop = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    percent_irrigated = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    sustainable_production = models.BooleanField(blank=True, null=True)
    primary_production = models.BooleanField(blank=True, null=True)
    percent_of_members_or_outgrowers_production_sold_to_producing_organization = (
        models.DecimalField(
            max_digits=5,
            decimal_places=2,
            blank=True,
            null=True,
            db_column="pomoopstpo",
        )
    )
    total_prod_members = models.DecimalField(
        max_digits=20, decimal_places=2, blank=True, null=True
    )
    operational_month_january = models.BooleanField(null=True)
    operational_month_february = models.BooleanField(null=True)
    operational_month_march = models.BooleanField(null=True)
    operational_month_april = models.BooleanField(null=True)
    operational_month_may = models.BooleanField(null=True)
    operational_month_june = models.BooleanField(null=True)
    operational_month_july = models.BooleanField(null=True)
    operational_month_august = models.BooleanField(null=True)
    operational_month_september = models.BooleanField(null=True)
    operational_month_october = models.BooleanField(null=True)
    operational_month_november = models.BooleanField(null=True)
    operational_month_december = models.BooleanField(null=True)
    productionpurchases_no_information_available = models.BooleanField(default=False)
    otherproductionpurchases_no_information_available = models.BooleanField(
        default=False
    )
    productionsales_no_information_available = models.BooleanField(default=False)
    productionfigures_no_information_available = models.BooleanField(default=False)
    input_purchases_no_information_available = models.BooleanField(default=False)
    productionpurchases_completed = models.BooleanField(default=False)
    otherproductionpurchases_completed = models.BooleanField(default=False)
    productionsales_completed = models.BooleanField(default=False)
    productionfigures_completed = models.BooleanField(default=False)
    input_purchases_completed = models.BooleanField(default=False)

    @denormalized(
        models.DecimalField, max_digits=40, decimal_places=2, blank=True, null=True
    )
    def production_potential_estimate(self):
        if self.land_used_for_product and self.average_yield:
            return self.land_used_for_product * self.average_yield

    @property
    def operational_months(self):
        return [
            _(month_name)
            for month_name in settings.MONTH_NAMES
            if getattr(self, "operational_month_{}".format(month_name.lower()))
        ]

    @property
    def certification_names(self):
        return ", ".join([item.name for item in (self.certifications.all())])

    class Meta:
        ordering = ("name", "unit")


class Certification(BaseModel):
    product = models.ForeignKey(
        Product, related_name="certifications", on_delete=models.CASCADE
    )
    name = models.TextField()
    status = models.TextField(blank=True)
    start_year = models.PositiveSmallIntegerField(blank=True, null=True)
    end_year = models.PositiveSmallIntegerField(blank=True, null=True)
    percent_certified_crop = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )

    @denormalized(
        models.ForeignKey,
        "Assessment",
        related_name="+",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
    )
    @depend_on("product__assessment_id")
    def _assessment(self):
        try:
            return self.product.assessment
        except Product.DoesNotExist:
            return None

    class Meta:
        ordering = ("pk",)


class YearlyProductInfo(BaseModel):
    product = models.ForeignKey(
        Product, related_name="%(class)ss", on_delete=models.CASCADE
    )
    year = models.PositiveSmallIntegerField()
    unit = models.TextField(blank=True)

    @denormalized(
        models.ForeignKey,
        "Assessment",
        related_name="%(class)ss",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
    )
    @depend_on("product__assessment_id")
    def _assessment(self):
        try:
            return self.product.assessment
        except Product.DoesNotExist:
            return None

    class Meta:
        abstract = True


class ProductionFigure(YearlyProductInfo):
    volume = models.DecimalField(max_digits=20, decimal_places=2)
    percent_loss = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    percent_certified = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )

    class Meta:
        ordering = ("pk",)


class ProductionPurchase(YearlyProductInfo):
    volume = models.DecimalField(max_digits=20, decimal_places=2)
    price = MoneyField(blank=True, null=True, max_digits=20, decimal_places=2)
    percent_certified = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )

    class Meta:
        ordering = ("pk",)


class OtherProductionPurchase(YearlyProductInfo):
    volume = models.DecimalField(max_digits=20, decimal_places=2)
    price = MoneyField(blank=True, null=True, max_digits=20, decimal_places=2)
    percent_certified = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )

    class Meta:
        ordering = ("pk",)


class ProductionSale(YearlyProductInfo):
    volume = models.DecimalField(max_digits=20, decimal_places=2)
    price = MoneyField(blank=True, null=True, max_digits=20, decimal_places=2)
    percent_certified = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    percent_exported = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    gross_margin = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )

    class Meta:
        ordering = ("pk",)


class ProductionMargin(YearlyProductInfo):
    margin = MoneyField(blank=True, null=True, max_digits=20, decimal_places=2)

    class Meta:
        ordering = ("pk",)


class InputPurchase(BaseModel):
    product = models.ForeignKey(
        Product, related_name="input_purchases", on_delete=models.CASCADE
    )
    name = models.TextField()
    other = models.TextField(blank=True)
    unit = models.TextField()
    year = models.PositiveSmallIntegerField()
    volume = models.DecimalField(max_digits=20, decimal_places=2)
    price = MoneyField(blank=True, null=True, max_digits=20, decimal_places=2)
    percent_certified = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )

    @denormalized(
        models.ForeignKey,
        "Assessment",
        related_name="+",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
    )
    @depend_on("product__assessment_id")
    def _assessment(self):
        try:
            return self.product.assessment
        except Product.DoesNotExist:
            return None

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("price")
    def price_usd(self):
        from .utils import convert_to_usd

        return convert_to_usd(self.price, self._assessment)

    class Meta:
        ordering = ("pk",)


class SoldToFarmers(BaseModel):
    product = models.ForeignKey(
        Product, related_name="sold_to_farmers", on_delete=models.CASCADE
    )
    product_subtype = models.TextField(blank=True)
    specification = models.TextField(blank=True)
    sales_amount = MoneyField(blank=True, null=True, max_digits=20, decimal_places=2)
    sales_trend = models.TextField(blank=True)

    @denormalized(
        models.ForeignKey,
        "Assessment",
        related_name="+",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
    )
    @depend_on("product__assessment_id")
    def _assessment(self):
        try:
            return self.product.assessment
        except Product.DoesNotExist:
            return None

    class Meta:
        ordering = ("pk",)


class OrganizationRelation(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="%(class)ss", on_delete=models.CASCADE
    )
    customer = models.TextField()
    other = models.TextField(blank=True)
    customer_type = models.TextField(blank=True)
    relation_to_producing_organization = models.TextField(blank=True)
    description_of_relation = models.TextField(blank=True)
    payment_method = models.TextField(blank=True)
    number_of_years_in_relation = models.FloatField(blank=True, null=True)
    contract_in_place = models.TextField(blank=True, null=True)
    contract_start_year = models.PositiveSmallIntegerField(blank=True, null=True)
    contract_end_year = models.PositiveSmallIntegerField(blank=True, null=True)
    description_of_agreement = models.TextField(blank=True)
    percent_sold_to_customer = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        blank=True,
        null=True,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
    )
    amount_purchased = MoneyField(
        blank=True, null=True, max_digits=20, decimal_places=2
    )
    # nice, but not needed
    contact = models.ForeignKey(
        Contact,
        related_name="%(class)ss",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    product_types_purchased = models.ManyToManyField(
        ProductTypeOption, related_name="+", blank=True
    )

    class Meta:
        abstract = True
        ordering = ("pk",)


class ValueChainPlayer(
    OrganizationRelation
):  # FIXME: Check with SCOPE if this split is neccesary
    pass


class EnablingPlayer(OrganizationRelation):
    pass


class Supplier(OrganizationRelation):
    pass


class BasicFinancialInfo(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="basic_financial_infos", on_delete=models.CASCADE
    )
    turnover = MoneyField(blank=True, null=True, max_digits=20, decimal_places=2)
    cost_of_sales = MoneyField(blank=True, null=True, max_digits=20, decimal_places=2)
    net_profit = MoneyField(blank=True, null=True, max_digits=20, decimal_places=2)
    shareholders_equity = MoneyField(
        blank=True, null=True, max_digits=20, decimal_places=2
    )
    year = models.PositiveSmallIntegerField()

    @denormalized(
        models.CharField,
        max_length=max(len(item[0]) for item in CURRENCY_CHOICES),
        choices=CURRENCY_CHOICES,
        default="USD",
    )
    @depend_on("assessment__currency")
    def _currency(self):
        try:
            return self.assessment.currency
        except Assessment.DoesNotExist:
            return "USD"

    class Meta:
        ordering = ("-year",)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("turnover")
    def turnover_usd(self):
        from .utils import convert_to_usd

        return convert_to_usd(self.turnover, self.assessment)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("cost_of_sales")
    def cost_of_sales_usd(self):
        from .utils import convert_to_usd

        return convert_to_usd(self.cost_of_sales, self.assessment)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("net_profit")
    def net_profit_usd(self):
        from .utils import convert_to_usd

        return convert_to_usd(self.net_profit, self.assessment)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("shareholders_equity")
    def shareholders_equity_usd(self):
        from .utils import convert_to_usd

        return convert_to_usd(self.shareholders_equity, self.assessment)


class BankAccount(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="bank_accounts", on_delete=models.CASCADE
    )
    bank = models.TextField()
    bank_type = models.TextField()
    active_since = models.PositiveSmallIntegerField()
    current_balance = MoneyField(blank=True, null=True, max_digits=20, decimal_places=2)

    class Meta:
        ordering = ("pk",)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("current_balance")
    def current_balance_usd(self):
        from .utils import convert_to_usd

        return convert_to_usd(self.current_balance, self.assessment)


class LoanHistory(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="loan_histories", on_delete=models.CASCADE
    )
    amount = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    financier = models.TextField()
    financier_name = models.TextField(blank=True)
    start_date = models.DateField(blank=True, null=True)
    end_date = models.DateField(blank=True, null=True)
    duration = models.PositiveSmallIntegerField(blank=True, null=True)
    annual_interest_rate = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    repayment_status = models.TextField()
    purpose = models.TextField()
    comment = models.TextField(blank=True)

    class Meta:
        ordering = ("pk",)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("amount")
    def amount_usd(self):
        from .utils import convert_to_usd

        return convert_to_usd(self.amount, self.assessment)


class PreFinanceHistory(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="pre_finance_histories", on_delete=models.CASCADE
    )
    amount = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    provider = models.TextField(blank=True)
    provider_name = models.TextField(blank=True)
    start_date = models.DateField(blank=True, null=True)
    end_date = models.DateField(blank=True, null=True)
    duration = models.PositiveSmallIntegerField(blank=True, null=True)
    annual_interest_rate = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    repayment_modality = models.TextField(blank=True)
    purpose = models.TextField(blank=True)
    comment = models.TextField(blank=True)

    class Meta:
        ordering = ("pk",)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("amount")
    def amount_usd(self):
        from .utils import convert_to_usd

        return convert_to_usd(self.amount, self.assessment)


class CapitalRequirement(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="capital_requirements", on_delete=models.CASCADE
    )
    amount = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    share_type = models.TextField(blank=True)
    status = models.TextField(blank=True)
    description = models.TextField(blank=True)
    comment = models.TextField(blank=True)

    class Meta:
        ordering = ("pk",)


class GrantHistory(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="grant_histories", on_delete=models.CASCADE
    )
    amount = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    description = models.TextField(blank=True)
    in_cash_in_kind = models.TextField()
    funder = models.TextField()
    funder_type = models.TextField()
    start_date = models.DateField(blank=True, null=True)
    purpose = models.TextField(blank=True)
    comment = models.TextField(blank=True)

    class Meta:
        ordering = ("pk",)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("amount")
    def amount_usd(self):
        from .utils import convert_to_usd

        return convert_to_usd(self.amount, self.assessment)


@documentable(assessment_denorm=True)
class DocumentAvailabilityResponse(BaseModel):
    assessment = models.ForeignKey(
        Assessment,
        related_name="document_availability_responses",
        on_delete=models.CASCADE,
    )
    question = models.ForeignKey(
        DocumentAvailabilityQuestion, related_name="+", on_delete=models.PROTECT
    )
    assessor = models.ForeignKey(
        Assessor,
        related_name="document_availability_responses",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    available = models.CharField(
        max_length=12,
        choices=(
            ("yes", _("yes")),
            ("no", _("no")),
            ("not relevant", _("not relevant")),
        ),
        blank=True,
    )
    available_with_profile = models.BooleanField(blank=True, null=True)
    comment = models.TextField(blank=True)
    accepted = models.BooleanField(default=False)

    @denormalized(models.TextField, blank=True)
    @depend_on("question__title")
    def question_title(self):
        try:
            return self.question.title
        except DocumentAvailabilityQuestion.DoesNotExist:
            return ""

    denorm_always_skip = ("modified_at",)

    class Meta:
        ordering = ("question__position", "question_id")


class LoanRequirement(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="loan_requirements", on_delete=models.CASCADE
    )
    amount = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    duration_in_months = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    expected_annual_interest_percentage = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    collateral_to_pledge = models.TextField(blank=True)
    value_of_collateral_to_pledge = MoneyField(
        max_digits=20, decimal_places=2, blank=True, null=True
    )
    status = models.TextField(blank=True)
    comment = models.TextField(blank=True)
    purpose = models.TextField(blank=True)
    description = models.TextField(blank=True)

    class Meta:
        ordering = ("pk",)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("amount")
    def amount_usd(self):
        from .utils import convert_to_usd

        return convert_to_usd(self.amount, self.assessment)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("value_of_collateral_to_pledge")
    def value_of_collateral_to_pledge_usd(self):
        from .utils import convert_to_usd

        return convert_to_usd(self.amount, self.assessment)


class LoanApplication(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="loan_applications", on_delete=models.CASCADE
    )
    amount = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    duration_in_months = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    potential_financier = models.TextField(blank=True)
    purpose = models.TextField(blank=True)
    status = models.TextField(blank=True)
    comment = models.TextField(blank=True)

    class Meta:
        ordering = ("pk",)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("amount")
    def amount_usd(self):
        from .utils import convert_to_usd

        return convert_to_usd(self.amount, self.assessment)


class CollateralAsset(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="collateral_assets", on_delete=models.CASCADE
    )
    type = models.TextField(blank=True)
    description = models.TextField(blank=True)
    age_in_years = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    estimated_value = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    external_valuation_done = models.TextField(blank=True)
    external_valuation_by = models.TextField(blank=True)
    valuation_proof_available = models.BooleanField(blank=True, null=True)

    class Meta:
        ordering = ("pk",)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("estimated_value")
    def estimated_value_usd(self):
        from .utils import convert_to_usd

        return convert_to_usd(self.estimated_value, self.assessment)


class Insurance(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="insurances", on_delete=models.CASCADE
    )
    type = models.TextField(blank=True)
    insurance_company = models.TextField(blank=True)
    policy_effictive_date = models.DateField(blank=True, null=True)
    policy_end_date = models.DateField(blank=True, null=True)
    indemnity_limit = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    comment = models.TextField(blank=True)

    class Meta:
        ordering = ("pk",)


class Shareholder(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="shareholders", on_delete=models.CASCADE
    )
    name = models.TextField(blank=True)
    relation_to_producing_organization = models.TextField(blank=True)
    percent_of_shares = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    shareholder_since_year = models.PositiveSmallIntegerField(blank=True, null=True)

    class Meta:
        ordering = ("pk",)


class MonthlyProduction(BaseModel):  # potentially remove entirely, check with Ben
    assessment = models.ForeignKey(
        Assessment, related_name="%(class)s_set", on_delete=models.CASCADE
    )
    activity = LowerCaseTextField(blank=True)
    january = models.BooleanField(blank=True, null=True)
    february = models.BooleanField(blank=True, null=True)
    march = models.BooleanField(blank=True, null=True)
    april = models.BooleanField(blank=True, null=True)
    may = models.BooleanField(blank=True, null=True)
    june = models.BooleanField(blank=True, null=True)
    july = models.BooleanField(blank=True, null=True)
    august = models.BooleanField(blank=True, null=True)
    september = models.BooleanField(blank=True, null=True)
    october = models.BooleanField(blank=True, null=True)
    november = models.BooleanField(blank=True, null=True)
    december = models.BooleanField(blank=True, null=True)

    def update(self, other):
        changed = False
        if self.january != other.january:
            self.january = other.january
            changed = True
        if self.february != other.february:
            self.february = other.february
            changed = True
        if self.march != other.march:
            self.march = other.march
            changed = True
        if self.april != other.april:
            self.april = other.april
            changed = True
        if self.may != other.may:
            self.may = other.may
            changed = True
        if self.june != other.june:
            self.june = other.june
            changed = True
        if self.july != other.july:
            self.july = other.july
            changed = True
        if self.august != other.august:
            self.august = other.august
            changed = True
        if self.september != other.september:
            self.september = other.september
            changed = True
        if self.october != other.october:
            self.october = other.october
            changed = True
        if self.november != other.november:
            self.november = other.november
            changed = True
        if self.december != other.december:
            self.december = other.december
            changed = True
        if changed:
            self.save()

    class Meta:
        unique_together = (("assessment", "activity"),)
        ordering = ("pk",)


class TotalMonthlyIncomeProjection(BaseModel):
    assessment = models.OneToOneField(Assessment, on_delete=models.CASCADE)

    @denormalized(
        models.OneToOneField,
        "NetMonthlyIncomeProjection",
        related_name="total_income",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
    )
    @depend_on("assessment___netmonthlyincomeprojection_id")
    def _net_income(self):
        try:
            return self.assessment.netmonthlyincomeprojection
        except NetMonthlyIncomeProjection.DoesNotExist:
            return None

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__january")
    def january(self):
        return self.projections.all().aggregate(total=models.Sum("january"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__february")
    def february(self):
        return self.projections.all().aggregate(total=models.Sum("february"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__march")
    def march(self):
        return self.projections.all().aggregate(total=models.Sum("march"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__april")
    def april(self):
        return self.projections.all().aggregate(total=models.Sum("april"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__may")
    def may(self):
        return self.projections.all().aggregate(total=models.Sum("may"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__june")
    def june(self):
        return self.projections.all().aggregate(total=models.Sum("june"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__july")
    def july(self):
        return self.projections.all().aggregate(total=models.Sum("july"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__august")
    def august(self):
        return self.projections.all().aggregate(total=models.Sum("august"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__september")
    def september(self):
        return self.projections.all().aggregate(total=models.Sum("september"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__october")
    def october(self):
        return self.projections.all().aggregate(total=models.Sum("october"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__november")
    def november(self):
        return self.projections.all().aggregate(total=models.Sum("november"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__december")
    def december(self):
        return self.projections.all().aggregate(total=models.Sum("december"))["total"]

    def all(self):
        return [
            self.january,
            self.february,
            self.march,
            self.april,
            self.may,
            self.june,
            self.july,
            self.august,
            self.september,
            self.october,
            self.november,
            self.december,
        ]

    class Meta:
        ordering = ("pk",)


class TotalMonthlyExpensesProjection(BaseModel):
    assessment = models.OneToOneField(Assessment, on_delete=models.CASCADE)

    @denormalized(
        models.OneToOneField,
        "NetMonthlyIncomeProjection",
        related_name="total_expenses",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
    )
    @depend_on("assessment___netmonthlyincomeprojection_id")
    def _net_income(self):
        try:
            return self.assessment.netmonthlyincomeprojection
        except NetMonthlyIncomeProjection.DoesNotExist:
            return None

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__january")
    def january(self):
        return (
            self.projections.all().aggregate(total=models.Sum("january"))["total"] or 0
        )

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__february")
    def february(self):
        return self.projections.all().aggregate(total=models.Sum("february"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__march")
    def march(self):
        return self.projections.all().aggregate(total=models.Sum("march"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__april")
    def april(self):
        return self.projections.all().aggregate(total=models.Sum("april"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__may")
    def may(self):
        return self.projections.all().aggregate(total=models.Sum("may"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__june")
    def june(self):
        return self.projections.all().aggregate(total=models.Sum("june"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__july")
    def july(self):
        return self.projections.all().aggregate(total=models.Sum("july"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__august")
    def august(self):
        return self.projections.all().aggregate(total=models.Sum("august"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__september")
    def september(self):
        return self.projections.all().aggregate(total=models.Sum("september"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__october")
    def october(self):
        return self.projections.all().aggregate(total=models.Sum("october"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__november")
    def november(self):
        return self.projections.all().aggregate(total=models.Sum("november"))["total"]

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("projections__december")
    def december(self):
        return self.projections.all().aggregate(total=models.Sum("december"))["total"]

    def all(self):
        return [
            self.january,
            self.february,
            self.march,
            self.april,
            self.may,
            self.june,
            self.july,
            self.august,
            self.september,
            self.october,
            self.november,
            self.december,
        ]

    class Meta:
        ordering = ("pk",)


class NetMonthlyIncomeProjection(BaseModel):
    assessment = models.OneToOneField(Assessment, on_delete=models.CASCADE)

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("total_income__january")
    @depend_on("total_expenses__january")
    def january(self):
        try:
            total_income = self.total_income.january.amount
        except (TotalMonthlyIncomeProjection.DoesNotExist, AttributeError):
            total_income = 0
        try:
            total_expenses = self.total_expenses.january.amount
        except (TotalMonthlyExpensesProjection.DoesNotExist, AttributeError):
            total_expenses = 0
        return total_income - total_expenses

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("total_income__february")
    @depend_on("total_expenses__february")
    def february(self):
        try:
            total_income = self.total_income.february.amount
        except (TotalMonthlyIncomeProjection.DoesNotExist, AttributeError):
            total_income = 0
        try:
            total_expenses = self.total_expenses.february.amount
        except (TotalMonthlyExpensesProjection.DoesNotExist, AttributeError):
            total_expenses = 0
        return total_income - total_expenses

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("total_income__march")
    @depend_on("total_expenses__march")
    def march(self):
        try:
            total_income = self.total_income.march.amount
        except (TotalMonthlyIncomeProjection.DoesNotExist, AttributeError):
            total_income = 0
        try:
            total_expenses = self.total_expenses.march.amount
        except (TotalMonthlyExpensesProjection.DoesNotExist, AttributeError):
            total_expenses = 0
        return total_income - total_expenses

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("total_income__april")
    @depend_on("total_expenses__april")
    def april(self):
        try:
            total_income = self.total_income.april.amount
        except (TotalMonthlyIncomeProjection.DoesNotExist, AttributeError):
            total_income = 0
        try:
            total_expenses = self.total_expenses.april.amount
        except (TotalMonthlyExpensesProjection.DoesNotExist, AttributeError):
            total_expenses = 0
        return total_income - total_expenses

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("total_income__may")
    @depend_on("total_expenses__may")
    def may(self):
        try:
            total_income = self.total_income.may.amount
        except (TotalMonthlyIncomeProjection.DoesNotExist, AttributeError):
            total_income = 0
        try:
            total_expenses = self.total_expenses.may.amount
        except (TotalMonthlyExpensesProjection.DoesNotExist, AttributeError):
            total_expenses = 0
        return total_income - total_expenses

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("total_income__june")
    @depend_on("total_expenses__june")
    def june(self):
        try:
            total_income = self.total_income.june.amount
        except (TotalMonthlyIncomeProjection.DoesNotExist, AttributeError):
            total_income = 0
        try:
            total_expenses = self.total_expenses.june.amount
        except (TotalMonthlyExpensesProjection.DoesNotExist, AttributeError):
            total_expenses = 0
        return total_income - total_expenses

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("total_income__july")
    @depend_on("total_expenses__july")
    def july(self):
        try:
            total_income = self.total_income.july.amount
        except (TotalMonthlyIncomeProjection.DoesNotExist, AttributeError):
            total_income = 0
        try:
            total_expenses = self.total_expenses.july.amount
        except (TotalMonthlyExpensesProjection.DoesNotExist, AttributeError):
            total_expenses = 0
        return total_income - total_expenses

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("total_income__august")
    @depend_on("total_expenses__august")
    def august(self):
        try:
            total_income = self.total_income.august.amount
        except (TotalMonthlyIncomeProjection.DoesNotExist, AttributeError):
            total_income = 0
        try:
            total_expenses = self.total_expenses.august.amount
        except (TotalMonthlyExpensesProjection.DoesNotExist, AttributeError):
            total_expenses = 0
        return total_income - total_expenses

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("total_income__september")
    @depend_on("total_expenses__september")
    def september(self):
        try:
            total_income = self.total_income.september.amount
        except (TotalMonthlyIncomeProjection.DoesNotExist, AttributeError):
            total_income = 0
        try:
            total_expenses = self.total_expenses.september.amount
        except (TotalMonthlyExpensesProjection.DoesNotExist, AttributeError):
            total_expenses = 0
        return total_income - total_expenses

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("total_income__october")
    @depend_on("total_expenses__october")
    def october(self):
        try:
            total_income = self.total_income.october.amount
        except (TotalMonthlyIncomeProjection.DoesNotExist, AttributeError):
            total_income = 0
        try:
            total_expenses = self.total_expenses.october.amount
        except (TotalMonthlyExpensesProjection.DoesNotExist, AttributeError):
            total_expenses = 0
        return total_income - total_expenses

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("total_income__november")
    @depend_on("total_expenses__november")
    def november(self):
        try:
            total_income = self.total_income.november.amount
        except (TotalMonthlyIncomeProjection.DoesNotExist, AttributeError):
            total_income = 0
        try:
            total_expenses = self.total_expenses.november.amount
        except (TotalMonthlyExpensesProjection.DoesNotExist, AttributeError):
            total_expenses = 0
        return total_income - total_expenses

    @denormalized(MoneyField, max_digits=20, decimal_places=2, blank=True, null=True)
    @depend_on("total_income__december")
    @depend_on("total_expenses__december")
    def december(self):
        try:
            total_income = self.total_income.december.amount
        except (TotalMonthlyIncomeProjection.DoesNotExist, AttributeError):
            total_income = 0
        try:
            total_expenses = self.total_expenses.december.amount
        except (TotalMonthlyExpensesProjection.DoesNotExist, AttributeError):
            total_expenses = 0
        return total_income - total_expenses

    def all(self):
        return [
            self.january,
            self.february,
            self.march,
            self.april,
            self.may,
            self.june,
            self.july,
            self.august,
            self.september,
            self.october,
            self.november,
            self.december,
        ]

    class Meta:
        ordering = ("pk",)


class MonthlyCashflowProjection(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="%(class)s_set", on_delete=models.CASCADE
    )
    activity = LowerCaseTextField(blank=True)
    january = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    february = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    march = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    april = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    may = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    june = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    july = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    august = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    september = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    october = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    november = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)
    december = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)

    @denormalized(
        models.CharField,
        max_length=max(len(item[0]) for item in CURRENCY_CHOICES),
        choices=CURRENCY_CHOICES,
        default="USD",
    )
    @depend_on("assessment__currency")
    def _currency(self):
        return self.assessment.currency

    def all(self):
        return [
            self.january,
            self.february,
            self.march,
            self.april,
            self.may,
            self.june,
            self.july,
            self.august,
            self.september,
            self.october,
            self.november,
            self.december,
        ]

    class Meta:
        abstract = True
        unique_together = (("assessment", "activity"),)
        ordering = ("pk",)


class MonthlyIncomeProjection(MonthlyCashflowProjection):
    @denormalized(
        models.ForeignKey,
        TotalMonthlyIncomeProjection,
        related_name="projections",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    @depend_on("assessment___totalmonthlyincomeprojection_id")
    def _total(self):
        try:
            return self.assessment.totalmonthlyincomeprojection
        except TotalMonthlyIncomeProjection.DoesNotExist:
            return None


class MonthlyExpensesProjection(MonthlyCashflowProjection):
    @denormalized(
        models.ForeignKey,
        TotalMonthlyExpensesProjection,
        related_name="projections",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    @depend_on("assessment___totalmonthlyexpensesprojection_id")
    def _total(self):
        try:
            return self.assessment.totalmonthlyexpensesprojection
        except TotalMonthlyExpensesProjection.DoesNotExist:
            return None


class Person(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="%(class)s_set", on_delete=models.CASCADE
    )
    name = models.TextField()
    function = models.TextField(blank=True)
    MALE = "m"
    FEMALE = "f"
    MALE_FEMALE_CHOICES = ((MALE, "male"), (FEMALE, "female"))
    male_female = models.CharField(
        max_length=1, choices=MALE_FEMALE_CHOICES, blank=True
    )
    years_in_function = models.PositiveSmallIntegerField(blank=True, null=True)
    years_in_organization = models.PositiveSmallIntegerField(blank=True, null=True)
    years_in_sector = models.PositiveSmallIntegerField(blank=True, null=True)
    year_of_birth = models.PositiveSmallIntegerField(blank=True, null=True)
    end_of_term = models.TextField(
        blank=True, validators=[RegexValidator(r"[0-9]{2}-[0-9]{4}")]
    )
    shareholder = models.TextField(blank=True)
    qualifications = models.TextField(blank=True)

    class Meta:
        abstract = True
        ordering = ("pk",)


class Executive(Person):  # remove? ask ben
    pass


class NonExecutive(Person):  # remove? ask ben
    pass


class Governance(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="%(class)s_set", on_delete=models.CASCADE
    )
    name = models.TextField()
    present = models.BooleanField(null=True)
    number_of_people = models.PositiveIntegerField(blank=True, null=True)
    description = models.TextField(blank=True)
    auto_created = models.BooleanField(default=False)
    canonical_name = models.TextField(blank=True, null=True)

    class Meta:
        unique_together = (("assessment", "name"),)
        ordering = ("pk",)


class TermsAndConditions(DocumentMixin, BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="terms_and_conditions", on_delete=models.CASCADE
    )
    signed = models.BooleanField(default=False)

    class Meta:
        ordering = ("pk",)


class BaseTabComment(BaseComment):
    assessment = models.ForeignKey(
        Assessment, related_name="%(class)ss", on_delete=models.CASCADE
    )

    def get_location(self):
        return self.__class__.__name__

    class Meta:
        abstract = True
        ordering = ("pk",)


class AssessmentTabComment(BaseTabComment):
    pass


class OrganisationalTabComment(BaseTabComment):
    pass


class ValueChainTabComment(BaseTabComment):
    pass


class FinanceTabComment(BaseTabComment):
    pass


class FinancialHistoryTabComment(BaseTabComment):
    pass


class FinancialPerformanceTabComment(BaseTabComment):
    pass


class FinancialOverviewTabComment(BaseTabComment):
    pass


class FinancialProductionTabComment(BaseTabComment):
    pass


class ProfitLossTabComment(BaseTabComment):
    pass


class BalanceSheetTabComment(BaseTabComment):
    pass


class CashflowTabComment(BaseTabComment):
    pass


class ProductionTabComment(BaseTabComment):
    pass


class MonthlyProductionTabComment(BaseTabComment):
    pass


class GovernanceTabComment(BaseTabComment):
    pass


class ObservationsTabComment(BaseTabComment):
    pass


class DocumentationTabComment(BaseTabComment):
    pass


class TermsAndConditionsTabComment(BaseTabComment):
    pass


class AgentBankAccountLoanHistory(BaseModel):
    assessment = models.OneToOneField(
        Assessment,
        related_name="agent_bank_account_loan_history",
        on_delete=models.CASCADE,
    )
    has_bank_account = models.BooleanField(null=True)
    has_mobile_account = models.BooleanField(null=True)
    has_loan_history = models.BooleanField(null=True)
    has_paid_back_loans = models.BooleanField(null=True)

    class Meta:
        ordering = ("pk",)


class AgentIncome(BaseModel):
    assessment = models.ForeignKey(
        Assessment, related_name="agent_income", on_delete=models.CASCADE
    )
    year = models.PositiveSmallIntegerField()
    income = MoneyField(max_digits=20, decimal_places=2, blank=True, null=True)

    class Meta:
        ordering = ("pk",)

    @denormalized(
        models.DecimalField, blank=True, null=True, max_digits=20, decimal_places=2
    )
    @depend_on("income")
    def income_usd(self):
        from .utils import convert_to_usd

        return convert_to_usd(self.income, self.assessment)


class InputValidationLimits(BaseModel):
    name = models.TextField()
    value = models.PositiveIntegerField()

    class Meta:
        ordering = ("pk",)


@receiver(post_save, sender=Assessment)
def build_response_tree(instance, created, **kwargs):
    from .tasks import build_response_tree_task

    if created:
        print('poceli smo bildovanje assessmenta')
        build_response_tree_task.delay(instance.pk)
        print('zavrsili smo bildovanje assessmenta')


@receiver(pre_save, sender=Assessment)
def check_language(instance, **kwargs):
    if instance.pk:
        try:
            db_instance = Assessment._base_manager.get(pk=instance.pk)
            instance._language_changed = db_instance.language != instance.language
        except Assessment.DoesNotExist:
            instance._language_changed = True
    else:
        instance._language_changed = True


@receiver(pre_save, sender=Assessment)
def prefill_date(instance, **kwargs):
    if not instance.start_date:
        instance.start_date = instance.date


@receiver(post_save, sender=Assessment)
def language_changed(instance, **kwargs):
    if instance._language_changed:
        with transaction.atomic():
            with translation.override(instance.language):
                default_governances = [
                    (item.canonical_name, _(item.canonical_name))
                    for item in instance.governance_set.all().filter(auto_created=True)
                ]
                for canonical_name, name in default_governances:
                    instance.governance_set.filter(auto_created=True).exclude(
                        canonical_name=""
                    ).filter(canonical_name=canonical_name).update(name=name)
                flush()


@receiver(post_save)
def build_financial_ratio_objects(instance, created, sender, **kwargs):
    """
    When creating a new FinancialInfo subclass instance
    make sure a FinancialRatio object exists for that year
    """
    if created:
        if issubclass(sender, FinancialInfo):
            financial_ratio, created = FinancialRatio.objects.get_or_create(
                assessment=instance.assessment, year=instance.year
            )
            if not created:
                financial_ratio.save()


@receiver(post_delete)
def destroy_financial_ratio_objects(instance, sender, **kwargs):
    """
    When destroying a FinancialInfo subclass instance
    make sure that it wasn't the last one for it's year
    """
    if issubclass(sender, FinancialInfo):
        assessment = instance.assessment
        if assessment.pk:
            financialinfo_years = set(
                list(assessment.balancesheets.all().values_list("year", flat=True))
                + list(
                    assessment.profitlossstatements.all().values_list("year", flat=True)
                )
                + list(
                    assessment.cashflowstatements.all().values_list("year", flat=True)
                )
            )
            financialratio_years = set(
                list(assessment.financial_ratios.all().values_list("year", flat=True))
            )
            extra_years = financialratio_years - financialinfo_years
            for year in extra_years:
                assessment.financial_ratios.filter(year=year).delete()


def translatable_fields(model):
    return {
        AssessmentPurpose: ["name"],
        AssessmentEvaluation: ["global_quality_issues"],
        Product: ["name", "global_product_type", "production_unit"],
        ValueChainPlayer: ["relation_to_producing_organization", "contract_in_place"],
        EnablingPlayer: ["relation_to_producing_organization", "contract_in_place"],
        BankAccount: ["bank_type"],
        AssessmentInvitation: ["status"],
        CollateralAsset: ["type"],
        LoanHistory: ["financier", "repayment_status", "purpose"],
        PreFinanceHistory: ["provider", "repayment_modality", "purpose"],
        GrantHistory: ["funder_type", "in_cash_in_kind"],
        ProducingOrganizationDetails: [
            "access_roads",
            "power_electricity",
            "internet_access",
            "public_transportation",
            "mobile_network_coverage",
            "running_water",
            "demonstration_plot",
            "training_space",
            "warehousing",
        ],
    }.get(model, [])


import logging

logger = logging.getLogger(__name__)


# @receiver(post_save, sender=AssessmentPurpose)
# @receiver(post_save, sender=Product)
# @receiver(post_save, sender=ValueChainPlayer)
# @receiver(post_save, sender=EnablingPlayer)
# @receiver(post_save, sender=BankAccount)
# @receiver(post_save, sender=AssessmentInvitation)
# @receiver(post_save, sender=CollateralAsset)
# @receiver(post_save, sender=LoanHistory)
# @receiver(post_save, sender=PreFinanceHistory)
# @receiver(post_save, sender=GrantHistory)
# @receiver(post_save, sender=ProducingOrganizationDetails)
# def add_strings_to_translation_files(instance, sender, **kwargs):
#     logger.info("started add_strings_to_translation_files function...")
#     fields = translatable_fields(sender)
#     occurence_prefix = sender.__name__
#     _add_strings_to_translation_files(instance, fields, occurence_prefix)
#     logger.info("ended add_strings_to_translation_files function...")


def get_translable_strings(model, current_only=False):
    translatable_strings = set()

    fields = translatable_fields(model)
    queryset = model.objects.all()
    if current_only:
        if model == AssessmentPurpose:
            pass
        elif [field for field in model._meta.fields if field.name == "assessment"]:
            # via assessment model
            queryset = queryset.filter(assessment__tool__lokalise=True)
        elif [field for field in model._meta.fields if field.name == "_assessment"]:
            # via denormed assessment model
            queryset = queryset.filter(_assessment__tool__lokalise=True)
        else:
            raise NotImplementedError(model)
    string_tuples = queryset.values_list(*fields)

    for string_tuple in string_tuples:
        translatable_strings.update(string_tuple)

    return translatable_strings

assessment_built = Signal(providing_args=["instance", "created"])
questionnaire_built = Signal(providing_args=["instance", "created"])
@receiver(assessment_built)
def prefill_assessment(sender, instance, created, **kwargs):
    if created:
        print('poceli smo kopiranje starih podataka')
        # Get the last assessment done on the organization of the new assessment
        query = Assessment.objects.filter(
                producing_organization=instance.producing_organization,
                status=Assessment.STATUS_HAS_FINAL,
                tool__type__name=instance.tool.type.name
            )
        old_assessment = None
        if query.exists():
            old_assessment = query.latest("date")

        if old_assessment:
            print(f'old assessment id: {old_assessment.id}, new assessment id: {instance.id}')
            # Set the currency to the previous one
            print('adding currency')
            instance.currency = old_assessment.currency
            print('currency done')

            print('adding infrastructure')
            old_details = old_assessment.producing_organization_details
            current_details = instance.producing_organization_details

            if current_details:
                # Update existing details
                current_details.access_roads = old_details.access_roads
                current_details.distance_to_hub = old_details.distance_to_hub
                current_details.public_transportation = old_details.public_transportation
                current_details.power_electricity = old_details.power_electricity
                current_details.internet_access = old_details.internet_access
                current_details.mobile_network_coverage = old_details.mobile_network_coverage
                current_details.running_water = old_details.running_water

                #Bord and Management
                #Board member
                current_details.number_of_male_non_executives = old_details.number_of_male_non_executives
                current_details.number_of_female_non_executives = old_details.number_of_male_non_executives
                current_details.percent_of_non_executives_under_30 = old_details.percent_of_non_executives_under_30
                current_details.comment_number_of_non_executives = old_details.comment_number_of_non_executives
                #Manager / key staff
                current_details.number_of_male_executives = old_details.number_of_male_executives
                current_details.number_of_female_executives = old_details.number_of_female_executives
                current_details.percent_of_executives_under_30 = old_details.percent_of_executives_under_30
                current_details.comment_number_of_executives = old_details.comment_number_of_executives

                #Employees
                #Full-time employees
                current_details.number_of_male_full_time_employees = old_details.number_of_male_full_time_employees
                current_details.number_of_female_full_time_employees = old_details.number_of_female_full_time_employees
                current_details.percent_of_full_time_employees_under_30 = old_details.percent_of_full_time_employees_under_30
                current_details.comment_number_of_full_time_employees = old_details.comment_number_of_full_time_employees
                #Part-time employees
                current_details.number_of_male_part_time_employees = old_details.number_of_male_part_time_employees
                current_details.number_of_female_part_time_employees = old_details.number_of_female_part_time_employees
                current_details.percent_of_part_time_employees_under_30 = old_details.percent_of_part_time_employees_under_30
                current_details.comment_number_of_part_time_employees = old_details.comment_number_of_part_time_employees
                #Seasonal employees
                current_details.number_of_male_seasonal_employees = old_details.number_of_male_seasonal_employees
                current_details.number_of_female_seasonal_employees = old_details.number_of_female_seasonal_employees
                current_details.percent_of_seasonal_employees_under_30 = old_details.percent_of_seasonal_employees_under_30
                current_details.comment_number_of_seasonal_employees = old_details.comment_number_of_seasonal_employees

                #Membership and outgrowers
                #Member
                current_details.number_of_male_members = old_details.number_of_male_members
                current_details.number_of_female_members = old_details.number_of_female_members
                current_details.percent_of_members_under_30 = old_details.percent_of_members_under_30
                current_details.comment_number_of_members = old_details.comment_number_of_members
                #Active member
                current_details.number_of_male_active_members = old_details.number_of_male_active_members
                current_details.number_of_female_active_members = old_details.number_of_female_active_members
                current_details.percent_of_active_members_under_30 = old_details.percent_of_active_members_under_30
                current_details.comment_number_of_active_members = old_details.comment_number_of_active_members
                #Outgrower
                current_details.number_of_male_outgrowers = old_details.number_of_male_outgrowers
                current_details.number_of_female_outgrowers = old_details.number_of_female_outgrowers
                current_details.percent_of_outgrowers_under_30 = old_details.percent_of_outgrowers_under_30
                current_details.comment_number_of_outgrowers = old_details.comment_number_of_outgrowers
                #Active outgrower
                current_details.number_of_male_active_outgrowers = old_details.number_of_male_active_outgrowers
                current_details.number_of_female_active_outgrowers = old_details.number_of_female_active_outgrowers
                current_details.percent_of_active_outgrowers_under_30 = old_details.percent_of_active_outgrowers_under_30
                current_details.comment_number_of_active_outgrowers = old_details.comment_number_of_active_outgrowers

                current_details.save()
            else:
                # If current details do not exist, create new ones
                ProducingOrganizationDetails.objects.create(
                    assessment=instance,
                    access_roads=old_details.access_roads,
                    distance_to_hub=old_details.distance_to_hub,
                    public_transportation=old_details.public_transportation,
                    power_electricity=old_details.power_electricity,
                    internet_access=old_details.internet_access,
                    mobile_network_coverage=old_details.mobile_network_coverage,
                    running_water=old_details.running_water,
                    # Bord and Management
                    # Board member
                    number_of_male_non_executives = old_details.number_of_male_non_executives,
                    number_of_female_non_executives = old_details.number_of_male_non_executives,
                    percent_of_non_executives_under_30 = old_details.percent_of_non_executives_under_30,
                    comment_number_of_non_executives = old_details.comment_number_of_non_executives,
                    # Manager / key staff
                    number_of_male_executives = old_details.number_of_male_executives,
                    number_of_female_executives = old_details.number_of_female_executives,
                    percent_of_executives_under_30 = old_details.percent_of_executives_under_30,
                    comment_number_of_executives = old_details.comment_number_of_executives,

                    # Employees
                    # Full-time employees
                    number_of_male_full_time_employees = old_details.number_of_male_full_time_employees,
                    number_of_female_full_time_employees = old_details.number_of_female_full_time_employees,
                    percent_of_full_time_employees_under_30 = old_details.percent_of_full_time_employees_under_30,
                    comment_number_of_full_time_employees = old_details.comment_number_of_full_time_employees,
                    # Part-time employees
                    number_of_male_part_time_employees = old_details.number_of_male_part_time_employees,
                    number_of_female_part_time_employees = old_details.number_of_female_part_time_employees,
                    percent_of_part_time_employees_under_30 = old_details.percent_of_part_time_employees_under_30,
                    comment_number_of_part_time_employees = old_details.comment_number_of_part_time_employees,
                    # Seasonal employees
                    number_of_male_seasonal_employees = old_details.number_of_male_seasonal_employees,
                    number_of_female_seasonal_employees = old_details.number_of_female_seasonal_employees,
                    percent_of_seasonal_employees_under_30 = old_details.percent_of_seasonal_employees_under_30,
                    comment_number_of_seasonal_employees = old_details.comment_number_of_seasonal_employees,

                    # Membership and outgrowers
                    # Member
                    number_of_male_members = old_details.number_of_male_members,
                    number_of_female_members = old_details.number_of_female_members,
                    percent_of_members_under_30 = old_details.percent_of_members_under_30,
                    comment_number_of_members = old_details.comment_number_of_members,
                    # Active member
                    number_of_male_active_members = old_details.number_of_male_active_members,
                    number_of_female_active_members = old_details.number_of_female_active_members,
                    percent_of_active_members_under_30 = old_details.percent_of_active_members_under_30,
                    comment_number_of_active_members = old_details.comment_number_of_active_members,
                    # Outgrower
                    number_of_male_outgrowers = old_details.number_of_male_outgrowers,
                    number_of_female_outgrowers = old_details.number_of_female_outgrowers,
                    percent_of_outgrowers_under_30 = old_details.percent_of_outgrowers_under_30,
                    comment_number_of_outgrowers = old_details.comment_number_of_outgrowers,
                    # Active outgrower
                    number_of_male_active_outgrowers = old_details.number_of_male_active_outgrowers,
                    number_of_female_active_outgrowers = old_details.number_of_female_active_outgrowers,
                    percent_of_active_outgrowers_under_30 = old_details.percent_of_active_outgrowers_under_30,
                    comment_number_of_active_outgrowers = old_details.comment_number_of_active_outgrowers,
                )
            print('infrastructure done')
            # Retrieve all BasicProfitLossStatement objects related to the old Assessment
            if old_assessment.tool.type.name not in ['SCOPE Pro', 'SCOPE Pro SME']:
                print('non pro assessment')
                print('adding basic profit loss statements')
                old_statements = BasicProfitLossStatement.objects.filter(
                    assessment=old_assessment
                )[:2]
                # Duplicate each BasicProfitLossStatement object for the new Assessment
                for statement in old_statements:

                    BasicProfitLossStatement.objects.create(
                        assessment=instance,
                        turnover=statement.turnover,
                        cost_of_sales=statement.cost_of_sales,
                        operational_costs=statement.operational_costs,
                        net_profit=statement.net_profit,
                        year=statement.year,
                    )
                print('basic profit loss statements done')
            else:
                print('pro assessment')
                old_statements = ProfitLossStatement.objects.filter(
                    assessment=old_assessment
                )
                # Fetch all the existing profit/loss statements for the new assessment
                existing_statements = ProfitLossStatement.objects.filter(assessment=instance)
                print(existing_statements, 'these are the old statements')
                existing_years = set(existing_statements.values_list('year', flat=True))
                print(existing_years, 'these are the existing years')

                for statement in old_statements:
                    if statement.year in existing_years:
                        print('updating profit/loss statement', statement.year)
                        ProfitLossStatement.objects.filter(assessment=instance, year=statement.year).update(
                            no_data=statement.no_data,
                            direct_sales=statement.direct_sales,
                            indirect_sales=statement.indirect_sales,
                            other_income=statement.other_income,
                            depreciation_and_amortization=statement.depreciation_and_amortization,
                            interest_expense=statement.interest_expense,
                            income_tax_expense=statement.income_tax_expense,
                            earnings_from_discontinued_operations=statement.earnings_from_discontinued_operations,
                            manual_net_profit=statement.manual_net_profit,
                            audited=statement.audited
                        )
                    print('pro statement done')

                old_sheets = BalanceSheet.objects.filter(assessment=old_assessment)
                # Fetch all the existing balance sheets for the new assessment
                existing_sheets = BalanceSheet.objects.filter(assessment=instance)
                existing_years = set(existing_sheets.values_list('year', flat=True))

                for sheet in old_sheets:
                    if sheet.year in existing_years:
                        print('updating balanceSheet', sheet.year)
                        BalanceSheet.objects.filter(assessment=instance, year=sheet.year).update(
                            no_data=sheet.no_data,
                            accountant_comments=sheet.accountant_comments,
                            cash=sheet.cash,
                            account_receivables=sheet.account_receivables,
                            other_receivables=sheet.other_receivables,
                            inventories=sheet.inventories,
                            other_current_assets=sheet.other_current_assets,
                            fixed_assets=sheet.fixed_assets,
                            intangible_assets=sheet.intangible_assets,
                            goodwill=sheet.goodwill,
                            other_non_current_assets=sheet.other_non_current_assets,
                            accounts_payable=sheet.accounts_payable,
                            short_term_loans=sheet.short_term_loans,
                            overdrafts=sheet.overdrafts,
                            income_tax_payable=sheet.income_tax_payable,
                            short_term_provisions=sheet.short_term_provisions,
                            other_current_liabilities=sheet.other_current_liabilities,
                            long_term_loans=sheet.long_term_loans,
                            deferred_tax=sheet.deferred_tax,
                            provisions=sheet.provisions,
                            other_non_current_liabilities=sheet.other_non_current_liabilities,
                            share_capital=sheet.share_capital,
                            share_premium=sheet.share_premium,
                            retained_earnings=sheet.retained_earnings,
                            grants=sheet.grants,
                            statutory_legal_reserves=sheet.statutory_legal_reserves,
                            other_reserves=sheet.other_reserves,
                            other=sheet.other,
                            manual_total_assets=sheet.manual_total_assets,
                        )
                    print('balance sheet done')

                old_cashflows = CashFlowStatement.objects.filter(assessment=old_assessment)
                # Fetch all the existing cash flow statements for the assessment
                existing_flows = CashFlowStatement.objects.filter(assessment=instance)
                existing_years = set(existing_flows.values_list('year', flat=True))

                for flow in old_cashflows:
                    if flow.year in existing_years:
                        CashFlowStatement.objects.filter(assessment=instance, year=flow.year).update(
                            no_data=flow.no_data,
                            accountant_comments=flow.accountant_comments,
                            net_income=flow.net_income,
                            adjustment_for_tax=flow.adjustment_for_tax,
                            depreciation_and_amortization=flow.depreciation_and_amortization,
                            investment_income=flow.investment_income,
                            interest_expense=flow.interest_expense,
                            profit_on_sale_of_ppe=flow.profit_on_sale_of_ppe,
                            working_capital_changes=flow.working_capital_changes,
                            decrease_in_trade_and_other_receivables=flow.decrease_in_trade_and_other_receivables,
                            increase_in_inventories=flow.increase_in_inventories,
                            increase_in_trade_and_other_payables=flow.increase_in_trade_and_other_payables,
                            interest_paid=flow.interest_paid,
                            income_taxes_paid=flow.income_taxes_paid,
                            purchases_of_property_plant_and_equipment=flow.purchases_of_property_plant_and_equipment,
                            proceeds_from_sale_of_PPE=flow.proceeds_from_sale_of_PPE,
                            purchases_of_intangible_assets=flow.purchases_of_intangible_assets,
                            purchases_of_financial_assets=flow.purchases_of_financial_assets,
                            loans_granted_to_associates_or_subsidiaries=flow.loans_granted_to_associates_or_subsidiaries,
                            lrrfaos=flow.lrrfaos,
                            interest_received=flow.interest_received,
                            dividends_received=flow.dividends_received,
                            investing_other=flow.investing_other,
                            proceeds_from_sale_of_ordinary_shares=flow.proceeds_from_sale_of_ordinary_shares,
                            purchase_of_treasury_shares=flow.purchase_of_treasury_shares,
                            proceeds_from_borrowings=flow.proceeds_from_borrowings,
                            repayments_from_borrowings=flow.repayments_from_borrowings,
                            proceeds_from_loan_from_subsidiary_undertaking=flow.proceeds_from_loan_from_subsidiary_undertaking,
                            dividends_paid_to_organizations_shareholders=flow.dividends_paid_to_organizations_shareholders,
                            financing_other=flow.financing_other,
                            cash_at_beginning_of_period=flow.cash_at_beginning_of_period,
                        )
                    print('cash flow done')

            # Get all the loan data from the old assessment
            old_loans = LoanHistory.objects.filter(assessment=old_assessment)
            for loan in old_loans:
                LoanHistory.objects.create(
                    assessment=instance,
                    amount=loan.amount,
                    financier=loan.financier,
                    financier_name=loan.financier_name,
                    start_date=loan.start_date,
                    end_date=loan.end_date,
                    duration=loan.duration,
                    annual_interest_rate=loan.annual_interest_rate,
                    repayment_status=loan.repayment_status,
                    purpose=loan.purpose,
                    comment=loan.comment
                )

            #grant history
            old_grants = GrantHistory.objects.filter(assessment=old_assessment)
            for grant in old_grants:
                GrantHistory.objects.create(
                    assessment=instance,
                    amount=grant.amount,
                    description=grant.description,
                    in_cash_in_kind=grant.in_cash_in_kind,
                    funder=grant.funder,
                    funder_type=grant.funder_type,
                    start_date=grant.start_date,
                    purpose=grant.purpose,
                    comment=grant.comment
                )

            #pre finance history
            old_pre_finances = PreFinanceHistory.objects.filter(assessment=old_assessment)
            for pf in old_pre_finances:
                PreFinanceHistory.objects.create(
                    assessment=instance,
                    amount=pf.amount,
                    provider=pf.provider,
                    provider_name=pf.provider_name,
                    start_date=pf.start_date,
                    end_date=pf.end_date,
                    duration=pf.duration,
                    annual_interest_rate=pf.annual_interest_rate,
                    repayment_modality=pf.repayment_modality,
                    purpose=pf.purpose,
                    comment=pf.comment
                )

            current_documents_availability = DocumentAvailabilityResponse.objects.filter(assessment=instance)
            old_documents_availability = DocumentAvailabilityResponse.objects.filter(assessment=old_assessment)
            for current_document_availability in current_documents_availability:
                for old_document_availability in old_documents_availability:
                    print(current_document_availability.question.id)
                    if current_document_availability.question.id == old_document_availability.question.id:
                        current_document_availability.assessor = old_document_availability.assessor
                        current_document_availability.available = old_document_availability.available
                        current_document_availability.available_with_profile = old_document_availability.available_with_profile
                        current_document_availability.comment = old_document_availability.comment
                        current_document_availability.accepted = old_document_availability.accepted
                        current_document_availability.save()
                        break

            #production and sales info
            old_products = Product.objects.filter(assessment=old_assessment)
            for product in old_products:
                new_product = Product.objects.create(
                    assessment=instance,
                    global_product_type=product.global_product_type,
                    name=product.name,
                    priority=product.priority,
                    average_yield=product.average_yield,
                    land_size_under_production=product.land_size_under_production,
                    average_yield_unit=product.average_yield_unit,
                    land_size_under_production_unit=product.land_size_under_production_unit,
                    total_prod_members=product.total_prod_members,
                    land_used_for_product=product.land_used_for_product,
                    percent_of_members_or_outgrowers_production_sold_to_producing_organization=product.percent_of_members_or_outgrowers_production_sold_to_producing_organization
                )
                for certification in product.certifications.all():
                    new_certification = Certification.objecs.create(
                        product=new_product,
                        name=certification.name,
                        start_year=certification.start_year,
                        end_year=certification.end_year,
                        percent_certified_crop=certification.percent_certified_crop,
                    )

                for production_figure in product.productionfigures.all():
                    new_production_figure = ProductionFigure.objects.create(
                        product=new_product,
                        price=production_figure.price,
                        year=production_figure.year,
                        volume=production_figure.volume,
                        unit=production_figure.unit,
                        percent_certified=production_figure.percent_certified,
                        percent_loss=production_figure.percent_loss
                    )

                for production_purchase in product.productionpurchases.all():
                    new_purchase = ProductionPurchase.objects.create(
                        product=new_product,
                        price=production_purchase.price,
                        year=production_purchase.year,
                        volume=production_purchase.volume,
                        unit=production_purchase.unit,
                        percent_certified=production_purchase.percent_certified,
                    )

                for other in product.otherproductionpurchases.all():
                    new_other = OtherProductionPurchase.objects.create(
                        product=new_product,
                        price=other.price,
                        year=other.year,
                        volume=other.volume,
                        unit=other.unit,
                        percent_certified=other.percent_certified,
                    )

                for sale in product.productionsales.all():
                    new_sale = ProductionSale.objects.create(
                        product=new_product,
                        price=sale.price,
                        year=sale.year,
                        volume=sale.volume,
                        unit=sale.unit,
                        percent_certified=sale.percent_certified,
                        gross_margin=sale.gross_margin,
                        percent_exported=sale.percent_exported,
                    )

                for margin in product.productionmargins.all():
                    new_margin = ProductionMargin.objects.create(
                        product=new_product,
                        year=margin.year,
                        unit=margin.unit,
                        margin = margin.margin
                    )

                for input in product.input_purchases.all():
                    new_input = InputPurchase.objects.create(
                        product=new_product,
                        name=input.name,
                        other=input.other,
                        unit=input.unit,
                        year=input.year,
                        volume=input.volume,
                        price=input.price,
                        percent_certified=input.percent_certified
                    )

            #
        instance.save()
        print('zavrsili smo kopiranje starih podataka')
        questionnaire_built.send(sender=None, instance=instance, created=True)


@receiver(questionnaire_built)
def prefill_basic_and_pro_assessment(sender, instance, created, **kwargs):
    print("rapid -> basic&pro")
    if created:
        new_tool_title = instance.tool.title
        print("New tool title: " + new_tool_title)
        print("PRODUCING ORG:  " + str(instance.producing_organization))
        print("PRODUCING ORG:  " + str(instance.producing_organization))
        old_rapid_assessment = None  # name only
        input_retailer_or_sme = False

        if new_tool_title == 'SCOPE Pro 20.24.4':
            print("PROOOOOOO->")
            query_pro_pro = Assessment.objects.filter(
                producing_organization=instance.producing_organization,
                status=Assessment.STATUS_HAS_FINAL,
                tool__title=new_tool_title
            )
            if query_pro_pro.exists():
                print(" PRO")
                old_rapid_assessment = query_pro_pro.latest("date")
            else:
                query_pro_basic = Assessment.objects.filter(
                    producing_organization=instance.producing_organization,
                    status=Assessment.STATUS_HAS_FINAL,
                    tool__title='SCOPE Basic 20.24.4'
                )

                if query_pro_basic.exists():
                    print(" BASIC")
                    old_rapid_assessment = query_pro_basic.latest("date")
                else:
                    query_pro_rapid = Assessment.objects.filter(
                        producing_organization=instance.producing_organization,
                        status=Assessment.STATUS_HAS_FINAL,
                        tool__title='SCOPE Rapid 20.24.4'
                    )

                    if query_pro_rapid.exists():
                        print(" RAPID")
                        old_rapid_assessment = query_pro_rapid.latest("date")

        elif new_tool_title == 'SCOPE Basic 20.24.4':
            print("BASIC")
            query_basic_basic = Assessment.objects.filter(
                producing_organization=instance.producing_organization,
                status=Assessment.STATUS_HAS_FINAL,
                tool__title=new_tool_title
            )
            if query_basic_basic.exists():
                old_rapid_assessment = query_basic_basic.latest("date")
            else:
                query_basic_pro = Assessment.objects.filter(
                    producing_organization=instance.producing_organization,
                    status=Assessment.STATUS_HAS_FINAL,
                    tool__title='SCOPE Pro 20.24.4'
                )
                if query_basic_pro.exists():
                    old_rapid_assessment = query_basic_pro.latest("date")
                else:
                    query_basic_rapid = Assessment.objects.filter(
                        producing_organization=instance.producing_organization,
                        status=Assessment.STATUS_HAS_FINAL,
                        tool__title='SCOPE Rapid 20.24.4'
                    )

                    if query_basic_rapid.exists():
                        old_rapid_assessment = query_basic_rapid.latest("date")

        elif new_tool_title == 'SCOPE SACCO 20.22.0' or new_tool_title == 'SCOPE SACCO 20.22.1':
            query_sacco = Assessment.objects.filter(
                producing_organization=instance.producing_organization,
                status=Assessment.STATUS_HAS_FINAL,
                tool__title=new_tool_title
            )
            if query_sacco.exists():
                old_rapid_assessment = query_sacco.latest("date")

        elif 'SME' in new_tool_title:
            query_SME = Assessment.objects.filter(
                producing_organization=instance.producing_organization,
                status=Assessment.STATUS_HAS_FINAL,
                tool__title=new_tool_title
            )
            if query_SME.exists():
                input_retailer_or_sme = True
                old_rapid_assessment = query_SME.latest("date")

        elif new_tool_title == 'SCOPE Input Retailer 1.0.2':
            query_input_retailer = Assessment.objects.filter(
                producing_organization=instance.producing_organization,
                status=Assessment.STATUS_HAS_FINAL,
                tool__title=new_tool_title
            )
            if query_input_retailer.exists():
                input_retailer_or_sme = True
                old_rapid_assessment = query_input_retailer.latest("date")

        elif new_tool_title == 'SCOPE Rapid 20.24.4':
            query_rapid = Assessment.objects.filter(
                producing_organization=instance.producing_organization,
                status=Assessment.STATUS_HAS_FINAL,
                tool__title=new_tool_title
            )
            if query_rapid.exists():
                old_rapid_assessment = query_rapid.latest("date")
            else:
                query_rapid_basic = Assessment.objects.filter(
                    producing_organization=instance.producing_organization,
                    status=Assessment.STATUS_HAS_FINAL,
                    tool__title='SCOPE Basic 20.24.4'
                )

                if query_rapid_basic.exists():
                    old_rapid_assessment = query_rapid_basic.latest("date")
                else:
                    query_rapid_pro = Assessment.objects.filter(
                        producing_organization=instance.producing_organization,
                        status=Assessment.STATUS_HAS_FINAL,
                        tool__title='SCOPE Pro 20.24.4'
                    )

                    if query_rapid_pro.exists():
                        old_rapid_assessment = query_rapid_pro.latest("date")

        if old_rapid_assessment:# and (instance.tool.title == 'SCOPE Basic 20.24.4' or instance.tool.title == 'SCOPE Pro 20.24.4'):
            for section_response in instance.section_responses.all():
                new_sub_sections = section_response.get_descendants()

                for rapid_section_response in old_rapid_assessment.section_responses.all():
                    old_rapid_sub_sections = rapid_section_response.get_descendants()

                    for new_sub_section in new_sub_sections:
                        for old_rapid_sub_section in old_rapid_sub_sections:

                            if rapid_section_response.section.canonical.id == section_response.section.canonical.id:  #.section_title == section_title
                            #odavde pa dalje se razlikuju
                                if not input_retailer_or_sme and old_rapid_sub_section.section.canonical.id == new_sub_section.section.canonical.id:
                                    rapid_responses = old_rapid_sub_section.responses.all()
                                    new_responses = new_sub_section.responses.all()

                                    for rapid_response in rapid_responses:
                                        for new_response in new_responses:
                                            if rapid_response.question.canonical.id == new_response.question.canonical.id:
                                                new_response.comment = rapid_response.comment #????

                                                rapid_subresponses = rapid_response.subresponses.all()
                                                new_subresponses = new_response.subresponses.all()

                                                for rapid_subresponse in rapid_subresponses:
                                                    for new_subresponse in new_subresponses:
                                                        if rapid_subresponse.subquestion.canonical.id == new_subresponse.subquestion.canonical.id:
                                                            new_subresponse.comment = rapid_subresponse.comment

                                                            if rapid_subresponse.not_relevant:
                                                                new_subresponse.not_relevant = True
                                                                print("NON RELEVANTE")
                                                                new_subresponse.save()

                                                            if rapid_subresponse.none_of_the_above:
                                                                new_subresponse.none_of_the_above = True
                                                                print("NONE OF THE ABOVE")
                                                                new_subresponse.save()

                                                            if rapid_subresponse.selected_option:
                                                                prefill_option_selected_canonical = rapid_subresponse.selected_option.canonical
                                                                prefill_option_selected = prefill_option_selected_canonical.subquestionoption_set.filter(subquestion=new_subresponse.subquestion).first()
                                                                new_subresponse.selected_option = prefill_option_selected
                                                                new_subresponse.save()
                                                                print("SELECTED")
                                                            else:
                                                                rapid_options = rapid_subresponse.checked_options.all()
                                                                new_assessment_options = new_subresponse.subquestion.options.all()
                                                                new_assessment_checked_options = new_subresponse.checked_options.all()
                                                                for rapid_option in rapid_options:
                                                                    prefill_option_canonical = rapid_option.canonical
                                                                    prefill_option = prefill_option_canonical.subquestionoption_set.filter(subquestion=new_subresponse.subquestion).first()

                                                                    if prefill_option in new_assessment_options and prefill_option not in new_assessment_checked_options:
                                                                        new_subresponse.checked_options.add(prefill_option)
                                                                        new_subresponse.save()
                                                                        print("CHECKED")

                                                            if rapid_subresponse.value or rapid_subresponse.value == 0:
                                                                new_subresponse.value = rapid_subresponse.value
                                                                new_subresponse.save()
                                            new_response.save()
                                elif old_rapid_sub_section.section.id == new_sub_section.section.id:
                                    rapid_responses = old_rapid_sub_section.responses.all()
                                    new_responses = new_sub_section.responses.all()

                                    for rapid_response in rapid_responses:
                                        for new_response in new_responses:
                                            if rapid_response.question.id == new_response.question.id:
                                                new_response.comment = rapid_response.comment  # ????

                                                rapid_subresponses = rapid_response.subresponses.all()
                                                new_subresponses = new_response.subresponses.all()

                                                for rapid_subresponse in rapid_subresponses:
                                                    for new_subresponse in new_subresponses:
                                                        if rapid_subresponse.subquestion.id == new_subresponse.subquestion.id:
                                                            new_subresponse.comment = rapid_subresponse.comment

                                                            if rapid_subresponse.not_relevant:
                                                                new_subresponse.not_relevant = True
                                                                print("NON RELEVANTE")
                                                                new_subresponse.save()

                                                            if rapid_subresponse.none_of_the_above:
                                                                new_subresponse.none_of_the_above = True
                                                                print("NONE OF THE ABOVE")
                                                                new_subresponse.save()

                                                            if rapid_subresponse.selected_option:
                                                                new_subresponse.selected_option = rapid_subresponse.selected_option
                                                                new_subresponse.save()
                                                                print("SELECTED")
                                                            else:
                                                                rapid_options = rapid_subresponse.checked_options.all()
                                                                new_assessment_options = new_subresponse.subquestion.options.all()
                                                                new_assessment_checked_options = new_subresponse.checked_options.all()
                                                                for rapid_option in rapid_options:
                                                                    if rapid_option in new_assessment_options and rapid_option not in new_assessment_checked_options:
                                                                        new_subresponse.checked_options.add(
                                                                            rapid_option)
                                                                        new_subresponse.save()
                                                                        print("CHECKED")
                                                            if rapid_subresponse.value or rapid_subresponse.value == 0:
                                                                new_subresponse.value = rapid_subresponse.value
                                                                new_subresponse.save()
                                            new_response.save()

                        new_sub_section.save()

                section_response.save()
            instance.save()


@receiver(post_save, sender=BalanceSheet)
@receiver(post_save, sender=ProfitLossStatement)
@receiver(post_save, sender=CashFlowStatement)
@receiver(post_save, sender=Expense)
@receiver(post_save, sender=CostOfSale)
@receiver(post_save, sender=MonthlyIncomeProjection)
@receiver(post_save, sender=MonthlyExpensesProjection)
@receiver(post_save, sender=BasicFinancialInfo)
@receiver(post_save, sender=BasicProfitLossStatement)
def update_currencies(instance, raw, sender, **kwargs):
    """
    Make sure the currencies of all the moneyfields are always in sync
    """
    if raw:
        return
    if not instance._currency:
        return
    currency_fields = dict(
        [
            (field_name, instance._currency)
            for field_name in [field.name for field in instance._meta.get_fields()]
            if field_name.endswith("_currency")
        ]
    )
    sender.objects.filter(pk=instance.pk).update(**currency_fields)


@receiver(pre_delete, sender=AssessmentAssignment)
def send_unassignment_notification(instance, **kwargs):
    # Send alert to assessor
    if instance:
        if instance.assigned_as == AssessmentAssignment.AS_FINANCIAL_SPECIALIST:
            ax_type = _("financial assessment")
        else:
            ax_type = _("assessment")
        MessageType.send_message(
            from_user=None,
            to_user=instance.assessor.user,
            slug="unassignment",
            template="assessments/email/unassignment.txt",
            subject_template="assessments/email/unassignment_subject.txt",
            context={
                "toUser": instance.assessor.user,
                "assessment": instance.assessment,
                "date": instance.assessment.date.strftime("%b. %d, %Y"),
                "ax_type": ax_type,
            },
            language=instance.assessor.user.language,
        )


@receiver(post_save, sender=AssessmentInvitation)
def send_invite_created_notification(instance, created, **kwargs):
    logger.info("started send_invite_created_notification function...")
    if created:
        logger.info("stepped into send_invite_created_notification function...")
        # Send alert to assessor
        if instance.assigned_as == AssessmentAssignment.AS_FINANCIAL_SPECIALIST:
            ax_type = _("financial assessment")
        else:
            ax_type = _("assessment")

        MessageType.send_message(
            from_user=None,
            to_user=instance.assessor.user,
            slug="invite_created",
            template="assessments/email/invite_created.txt",
            subject_template="assessments/email/invite_created_subject.txt",
            context={
                "toUser": instance.assessor.user,
                "invitation": instance,
                "frontend": settings.FRONTEND_APP,
                "trainee": settings.TRAINEE,
                "ax_type": ax_type,
            },
            language=instance.assessor.user.language,
        )
        logger.info("ended send_invite_created_notification function...")


@receiver(pre_save, sender=AssessmentInvitation)
def send_invite_declined_notification(instance, **kwargs):
    logger.info("started send_invite_declined_notification function...")
    if instance.pk:
        logger.info("stepped into send_invite_declined_notification function...")
        if instance.status == AssessmentInvitation.STATUS_DECLINED:
            old_status = AssessmentInvitation.objects.get(pk=instance.pk).status
            if old_status != AssessmentInvitation.STATUS_DECLINED:
                # Invitation is currently being declined
                if instance.assessment.project_id:
                    if (
                        instance.assigned_as
                        == AssessmentAssignment.AS_FINANCIAL_SPECIALIST
                    ):
                        ax_type = _("financial assessment")
                    else:
                        ax_type = _("assessment")

                    project_lead = instance.assessment.project.contact.user
                    MessageType.send_message(
                        from_user=None,
                        to_user=project_lead,
                        slug="invite_declined",
                        template="assessments/email/invite_declined.txt",
                        subject_template="assessments/email/invite_declined_subject.txt",
                        context={"invitation": instance, "ax_type": ax_type},
                        language=project_lead.language,
                    )
        logger.info("ended into send_invite_declined_notification function...")


@receiver(post_save, sender=ProfitLossStatement)
def auto_create_costs_of_sales(instance, created, **kwargs):
    if created:
        names = (
            CostOfSale.objects.filter(
                profit_loss_statement__assessment=instance.assessment
            )
            .order_by()
            .values_list("name", flat=True)
            .distinct()
        )
        for name in names:
            instance.costs_of_sales.create(name=name)


@receiver(post_save, sender=ProfitLossStatement)
def auto_create_expenses(instance, created, **kwargs):
    if created:
        names = (
            Expense.objects.filter(
                profit_loss_statement__assessment=instance.assessment
            )
            .order_by()
            .values_list("name", flat=True)
            .distinct()
        )
        for name in names:
            instance.expenses.create(name=name)


@receiver(pre_save, sender=AssessmentAssignment)
def set_submitted_at_least_once(instance, **kwargs):
    if instance.pk:
        db_instance = AssessmentAssignment.objects.get(pk=instance.pk)
        if instance.locked_for_assessor and not db_instance.locked_for_assessor:
            instance.submitted_at_least_once = True
            instance.submitted_first = datetime.date.today()


@receiver(pre_save)
@receiver(pre_delete)
@receiver(post_save)
def create_assessment_log(instance, signal, **kwargs):
    # Don't log creation of log objects
    if isinstance(instance, AssessmentLog):
        return
    # Create will be handled by post_save
    if signal == pre_save and hasattr(instance, "id") and instance.id is None:
        return
    # Update was already handled by pre_save
    if signal == post_save and not kwargs["created"]:
        return
    # Don't log assessment delete
    if signal == pre_delete and isinstance(instance, Assessment):
        return

    # Determine assessment
    if isinstance(instance, Assessment):
        assessment = instance
    elif hasattr(instance, "assessment"):
        assessment = instance.assessment
    elif hasattr(instance, "_assessment"):
        assessment = instance._assessment
    else:
        assessment = None

    # No assessment, no log
    if assessment is None:
        return

    # Not submitted yet, no log
    if not assessment.submitted_at_least_once:
        return

    # Determine action
    if signal == post_save:
        action = "create"
    elif signal == pre_save:
        action = "update"
    elif signal == pre_delete:
        action = "delete"
    else:
        raise NotImplementedError

    target_model = "{}.{}".format(instance._meta.app_label, instance._meta.model_name)
    target_id = instance.id

    # Determine changes
    keys_to_ignore = ["id", "url", "modified_at", "assessment"]

    if action == "update" or action == "delete":
        old_data = serialize_instance(instance._meta.model.objects.get(id=instance.id))
    if action == "update" or action == "create":
        new_data = serialize_instance(instance)
    if action == "create":
        old_data = {}
    if action == "delete":
        new_data = {}
    for key in keys_to_ignore:
        old_data.pop(key, None)
        new_data.pop(key, None)
    added, removed, modified, _ = dict_compare(new_data, old_data)
    all_keys = set(old_data.keys()) | set(new_data.keys())

    for key in sorted(all_keys):
        if isinstance(instance, Assessment) and key == "status":
            # Status logging is already handled in status denorm
            continue

        if key in removed or key in modified:
            previous_data = str(old_data[key])
        else:
            previous_data = None

        if key in added or key in modified:
            updated_data = str(new_data[key])
        else:
            updated_data = None

        if previous_data or updated_data:
            AssessmentLog.objects.create(
                assessment=assessment,
                action=action,
                target_model=target_model,
                target_id=target_id,
                changed_field=key,
                previous_data=previous_data or "-",
                updated_data=updated_data or "-",
            )


@receiver(pre_save, sender=Product)
def fill_fao_item_code(instance, **kwargs):
    lower_case_name_to_name = {key.lower(): key for key in name_to_item_code.keys()}
    instance.name = lower_case_name_to_name.get(instance.name.lower(), instance.name)
    instance.fao_item_code = name_to_item_code.get(instance.name, "")
    productoption = (
        ProductPerTypeOption.objects.filter(name=instance.name)
        .select_related("product_type")
        .first()
    )
    if productoption:
        instance.global_product_type = productoption.product_type.name
    instance.fao_category_code = category_name_to_code.get(
        instance.global_product_type, ""
    )
