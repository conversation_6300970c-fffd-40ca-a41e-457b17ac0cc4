# Generated by Django 1.10.3 on 2016-11-24 18:25


from django.db import migrations
from django.db.models import Count


def cleanup_duplicate_products(apps, schema_editor):
    related_fields = [
        "comments",
        "certifications",
        "productionfigures",
        "productionpurchases",
        "productionsales",
        "productionmargins",
        "input_purchases",
    ]
    Product = apps.get_model("assessments", "Product")
    duplicates = (
        Product.objects.order_by()
        .values_list("assessment", "name", "unit")
        .annotate(count=Count("id"))
        .filter(count__gte=2)
    )
    for assessment_id, name, unit, count in duplicates:
        products = Product.objects.filter(
            assessment_id=assessment_id, name=name, unit=unit
        ).order_by("pk")
        product_to_keep = products[0]
        for product in products[1:]:
            for field in related_fields:
                for related_item in getattr(product, field).all():
                    related_item.product = product_to_keep
                    related_item.save()
            for field in related_fields:
                assert getattr(product, field).all().count() == 0
            product.delete()


class Migration(migrations.Migration):

    dependencies = [("assessments", "0235_auto_20161111_1110")]

    operations = [migrations.RunPython(cleanup_duplicate_products)]
