# Generated by Django 1.11.13 on 2018-05-31 18:54


from django.db import migrations


def move_services(apps, schema_editor):
    Service = apps.get_model("assessments.Service")
    ProducingOrganizationDetails = apps.get_model(
        "assessments.ProducingOrganizationDetails"
    )
    ServiceOption = apps.get_model("products.ServiceOption")
    Tool = apps.get_model("products.Tool")
    services = Service.objects.all()
    tools = Tool.objects.all()
    for tool in tools:
        for service in services:
            ServiceOption.objects.create(name=service.name, tool=tool)
    producing_organization_details = ProducingOrganizationDetails.objects.filter(
        assessment__isnull=False
    )
    for obj in producing_organization_details:
        for service in obj.old_services.all():
            obj.services.add(
                ServiceOption.objects.get(tool=obj.assessment.tool, name=service.name)
            )


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0329_producingorganizationdetails_services"),
        ("products", "0134_auto_20180531_1852"),
    ]

    operations = [migrations.RunPython(move_services)]
