from django.db import migrations, models

import libs.field_helpers


class Migration(migrations.Migration):

    dependencies = [
        ("hrm", "0013_assessor_second_region"),
        ("assessments", "0126_documentavailabilityresponsedocument"),
    ]

    operations = [
        migrations.CreateModel(
            name="MonthlyProduction",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("accepted", models.BooleanField(default=False)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("activity", libs.field_helpers.LowerCaseTextField(blank=True)),
                ("january", models.NullBooleanField()),
                ("february", models.NullBooleanField()),
                ("march", models.NullBooleanField()),
                ("april", models.NullBooleanField()),
                ("may", models.NullBooleanField()),
                ("june", models.NullBooleanField()),
                ("july", models.NullBooleanField()),
                ("august", models.NullBooleanField()),
                ("september", models.NullBooleanField()),
                ("october", models.NullBooleanField()),
                ("november", models.NullBooleanField()),
                ("december", models.NullBooleanField()),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE, to="assessments.Assessment"
                    ),
                ),
                (
                    "quality_controller",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="monthlyproductions_quality_controlled",
                        blank=True,
                        to="hrm.Employee",
                        null=True,
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.AlterUniqueTogether(
            name="monthlyproduction", unique_together=set([("assessment", "activity")])
        ),
    ]
