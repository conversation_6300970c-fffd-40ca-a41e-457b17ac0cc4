from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0068_auto_20150208_0921")]

    operations = [
        migrations.AddField(
            model_name="assessmentdocument",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="assessmentdocumentcomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="balancesheetdocument",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="balancesheetdocumentcomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="costofsaledocument",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="costofsaledocumentcomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="expensedocument",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="expensedocumentcomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="profitlossstatementdocument",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="profitlossstatementdocumentcomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="responsedocument",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="responsedocumentcomment",
            name="_assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="+",
                blank=True,
                editable=False,
                to="assessments.Assessment",
                null=True,
            ),
            preserve_default=True,
        ),
    ]
