from django.db import migrations


def set_completion_check_required_to_false(apps, schema_editor):
    """
    Set completion_check_required to False for all existing assessments
    """
    Assessment = apps.get_model("assessments", "Assessment")
    Assessment.objects.all().update(completion_check_required=False)


class Migration(migrations.Migration):

    dependencies = [("assessments", "0203_assessment_completion_check_required")]

    operations = [migrations.RunPython(set_completion_check_required_to_false)]
