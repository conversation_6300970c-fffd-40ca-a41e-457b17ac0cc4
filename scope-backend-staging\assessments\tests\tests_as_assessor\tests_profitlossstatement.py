import json
from decimal import Decimal

from moneyed import Money
from rest_framework.reverse import reverse

from assessments.factories import (
    AssessmentAssignmentFactory,
    AssessmentFactory,
    CostOfSaleFactory,
    ExpenseFactory,
    ProfitLossStatementFactory,
)
from assessments.models import (
    AssessmentAssignment,
    CostOfSale,
    Expense,
    ProfitLossStatement,
)
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class ProfitLossStatementTestCase(DenormMixin, AssessorJWTTestCase):
    maxDiff = None

    def test_all_costs_of_sales(self):
        """
        Test that the all_costs_of_sales endpoint works for reading and writing
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor, currency="EUR"
        )
        years = sorted(list(range(2010, 2015)), reverse=True)
        for year in years:
            ProfitLossStatementFactory.create(assessment=assessment, year=year)
        url = reverse("assessments:assessment-detail", (assessment.pk,))
        # Get assessment detail
        assessment_response = json.loads(self.client.get(url).content)
        # Check that all_costs_of_sales exists and is empty
        self.assertIn("all_costs_of_sales", list(assessment_response.keys()))
        all_costs_of_sales = assessment_response["all_costs_of_sales"]
        self.assertDictEqual({}, all_costs_of_sales)
        # Create some costs of sales
        names = ("a", "b", "c", "d")
        for name in names[:-1]:
            for profit_loss_statement in assessment.profitlossstatements.all():
                CostOfSaleFactory.create(
                    profit_loss_statement=profit_loss_statement,
                    name=name,
                    value=Money(1, "EUR"),
                )
        # Get assessment detail
        assessment_response = json.loads(self.client.get(url).content)
        # Check that all_costs_of_sales exists and contains the created objects
        self.assertIn("all_costs_of_sales", list(assessment_response.keys()))
        all_costs_of_sales = assessment_response["all_costs_of_sales"]
        self.assertListEqual(list(names[:-1]), list(all_costs_of_sales.keys()))
        for name in names[:-1]:
            costs_of_sales = all_costs_of_sales[name]
            for year, cost_of_sale in zip(years, costs_of_sales):
                self.assertIn("year", list(cost_of_sale.keys()))
                self.assertEqual(year, cost_of_sale["year"])
                self.assertDictContainsSubset(
                    {"value": {"amount": "1.00", "currency": "EUR"}}, cost_of_sale
                )
        # Post modified all_costs_of_sales
        assessment_response = self.client.patch(
            url,
            data=json.dumps(
                {
                    "all_costs_of_sales": dict(
                        (
                            name,
                            [
                                {
                                    "year": year,
                                    "value": {"amount": "2.00", "currency": "EUR"},
                                }
                                for year in years
                            ],
                        )
                        for name in names[1:]
                    )
                }
            ),
            content_type="application/json",
        )
        # Check status code
        self.assertEqual(200, assessment_response.status_code)
        assessment_response = json.loads(assessment_response.content)
        # Check that all_costs_of_sales exists and contains the created objects
        self.assertIn("all_costs_of_sales", list(assessment_response.keys()))
        all_costs_of_sales = assessment_response["all_costs_of_sales"]
        self.assertListEqual(list(names[1:]), list(all_costs_of_sales.keys()))
        for name in names[1:]:
            costs_of_sales = all_costs_of_sales[name]
            for cost_of_sale in costs_of_sales:
                self.assertDictContainsSubset(
                    {"value": {"amount": "2.00", "currency": "EUR"}}, cost_of_sale
                )

    def test_incremental_cost_of_sales(self):
        """
        It should be possible to incrementally build up all_costs_of_sales
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor, currency="EUR"
        )
        ProfitLossStatementFactory.create(assessment=assessment, year=2010)
        url = reverse("assessments:assessment-detail", (assessment.pk,))
        data = {
            "all_costs_of_sales": {
                "a": [{"year": 2010, "value": {"amount": "1.00", "currency": "EUR"}}]
            }
        }
        response = self.client.patch(
            url, data=json.dumps(data), content_type="application/json"
        )
        response_dict = json.loads(response.content)
        self.assertIn("all_costs_of_sales", response_dict)
        self.assertSetEqual({"a"}, set(response_dict["all_costs_of_sales"].keys()))
        data = {
            "all_costs_of_sales": {
                "a": [{"year": 2010, "value": {"amount": "1.00", "currency": "EUR"}}],
                "b": [{"year": 2010, "value": {"amount": "1.00", "currency": "EUR"}}],
            }
        }
        response = self.client.patch(
            url, data=json.dumps(data), content_type="application/json"
        )
        response_dict = json.loads(response.content)
        self.assertIn("all_costs_of_sales", response_dict)
        self.assertSetEqual({"a", "b"}, set(response_dict["all_costs_of_sales"].keys()))

    def test_all_expenses(self):
        """
        Test that the all_expenses endpoint works for reading and writing
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor, currency="EUR"
        )
        years = sorted(list(range(2010, 2015)), reverse=True)
        for year in years:
            ProfitLossStatementFactory.create(assessment=assessment, year=year)
        url = reverse("assessments:assessment-detail", (assessment.pk,))
        # Get assessment detail
        assessment_response = json.loads(self.client.get(url).content)
        # Check that all_expenses exists and is empty
        self.assertIn("all_expenses", list(assessment_response.keys()))
        all_expenses = assessment_response["all_expenses"]
        self.assertDictEqual({}, all_expenses)
        # Create some expenses
        names = ("a", "b", "c", "d")
        for name in names[:-1]:
            for profit_loss_statement in assessment.profitlossstatements.all():
                ExpenseFactory.create(
                    profit_loss_statement=profit_loss_statement,
                    name=name,
                    value=Money(1, "EUR"),
                )
        # Get assessment detail
        assessment_response = json.loads(self.client.get(url).content)
        # Check that all_expenses exists and contains the created objects
        self.assertIn("all_expenses", list(assessment_response.keys()))
        all_expenses = assessment_response["all_expenses"]
        self.assertListEqual(list(names[:-1]), list(all_expenses.keys()))
        for name in names[:-1]:
            expenses = all_expenses[name]
            for year, expense in zip(years, expenses):
                self.assertIn("year", list(expense.keys()))
                self.assertEqual(year, expense["year"])
                self.assertDictContainsSubset(
                    {"value": {"amount": "1.00", "currency": "EUR"}}, expense
                )
        # Post modified all_expenses
        assessment_response = self.client.patch(
            url,
            data=json.dumps(
                {
                    "all_expenses": dict(
                        (
                            name,
                            [
                                {
                                    "year": year,
                                    "value": {"amount": "2.00", "currency": "EUR"},
                                }
                                for year in years
                            ],
                        )
                        for name in names[1:]
                    )
                }
            ),
            content_type="application/json",
        )
        # Check status code
        self.assertEqual(200, assessment_response.status_code)
        assessment_response = json.loads(assessment_response.content)
        # Check that all_expenses exists and contains the created objects
        self.assertIn("all_expenses", list(assessment_response.keys()))
        all_expenses = assessment_response["all_expenses"]
        self.assertListEqual(list(names[1:]), list(all_expenses.keys()))
        for name in names[1:]:
            expenses = all_expenses[name]
            for expense in expenses:
                self.assertDictContainsSubset(
                    {"value": {"amount": "2.00", "currency": "EUR"}}, expense
                )

    def test_create_costs_of_sale_from_scratch_through_api(self):
        """
        It should be possible to create all_costs_of_sales from scratch through the api
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor, currency="EUR"
        )
        years = sorted(list(range(2010, 2015)), reverse=True)
        for year in years:
            ProfitLossStatementFactory.create(assessment=assessment, year=year)
        url = reverse("assessments:assessment-detail", (assessment.pk,))
        # Get assessment detail
        assessment_response = json.loads(self.client.get(url).content)
        # Check that all_costs_of_sales exists and is empty
        self.assertIn("all_costs_of_sales", list(assessment_response.keys()))
        all_costs_of_sales = assessment_response["all_costs_of_sales"]
        self.assertDictEqual({}, all_costs_of_sales)
        # Check that the really are no cost of sales objects
        self.assertEqual(0, CostOfSale.objects.count())
        # Create some costs of sales
        names = ("a", "b", "c", "d")
        years = sorted(list(range(2010, 2015)), reverse=True)
        assessment_response = self.client.patch(
            url,
            data=json.dumps(
                {
                    "all_costs_of_sales": dict(
                        (
                            name,
                            [
                                {
                                    "year": year,
                                    "value": {"amount": "2.00", "currency": "EUR"},
                                }
                                for year in years
                            ],
                        )
                        for name in names[1:]
                    )
                }
            ),
            content_type="application/json",
        )
        # Check status code
        self.assertEqual(200, assessment_response.status_code)
        # Check that objects were created
        self.assertEqual(15, CostOfSale.objects.count())
        assessment_response = json.loads(assessment_response.content)
        # Check that all_costs_of_sales exists and contains the created objects
        self.assertIn("all_costs_of_sales", list(assessment_response.keys()))
        all_costs_of_sales = assessment_response["all_costs_of_sales"]
        self.assertListEqual(list(names[1:]), list(all_costs_of_sales.keys()))
        for name in names[1:]:
            costs_of_sales = all_costs_of_sales[name]
            for year, cost_of_sale in zip(years, costs_of_sales):
                self.assertIn("year", list(cost_of_sale.keys()))
                self.assertEqual(year, cost_of_sale["year"])
                self.assertDictContainsSubset(
                    {"value": {"amount": "2.00", "currency": "EUR"}}, cost_of_sale
                )

    def test_patching_all_costs_of_sales_updates_total_cost_of_sales(self):
        """
        When patching all_costs_of_sales, the total_cost_of_sales field of the
        profitlossstatement should update
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor, currency="USD"
        )
        ProfitLossStatementFactory.create(assessment=assessment, year=2016)
        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {
            "all_costs_of_sales": {"test": [{"year": 2016, "value": {"amount": 10}}]}
        }
        self.client.patch(url, data=json.dumps(data), content_type="application/json")
        profitlossstatement = assessment.profitlossstatements.get(year=2016)
        self.assertEqual(10, profitlossstatement.total_cost_of_sales.amount)
        data = {
            "all_costs_of_sales": {"test": [{"year": 2016, "value": {"amount": 20}}]}
        }
        response = self.client.patch(
            url, data=json.dumps(data), content_type="application/json"
        )
        profitlossstatement = assessment.profitlossstatements.get(year=2016)
        self.assertEqual(20, profitlossstatement.total_cost_of_sales.amount)
        response_dict = json.loads(response.content)
        self.assertIn("profitlossstatements", response_dict)
        self.assertIn("objects", response_dict["profitlossstatements"])
        self.assertEqual(1, len(response_dict["profitlossstatements"]["objects"]))
        self.assertIn(
            "total_cost_of_sales", response_dict["profitlossstatements"]["objects"][0]
        )
        self.assertDictEqual(
            {"currency": "USD", "amount": "20"},
            response_dict["profitlossstatements"]["objects"][0]["total_cost_of_sales"],
        )

    def test_patching_all_costs_of_sales_returns_profitloss_links(self):
        """
        When patching all_costs_of_sales, profitloss should still have links
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        ProfitLossStatementFactory.create(assessment=assessment, year=2016)
        url = (
            reverse("assessments:assessment-detail", [assessment.pk])
            + "?fields=profitlossstatements&fields=all_costs_of_sales&fields=all_expenses"
        )
        data = {
            "all_costs_of_sales": {"test": [{"year": 2016, "value": {"amount": 10}}]}
        }
        response = self.client.patch(
            url, data=json.dumps(data), content_type="application/json"
        )
        response_content = json.loads(response.content)
        self.assertIn("profitlossstatements", response_content)
        self.assertIn("links", response_content["profitlossstatements"])

    def test_manual_net_profit_default(self):
        """
        Manual net profit should default to None
        """
        profit_loss_statement = ProfitLossStatementFactory.create()
        self.assertIsNone(profit_loss_statement.manual_net_profit)

    def test_get_net_profit(self):
        """
        It should be possible to get net profit from api
        """
        profit_loss_statement = ProfitLossStatementFactory.create(
            manual_net_profit=100,
            assessment__assessmentassignments__assessor=self.assessor,
        )
        url = reverse(
            "assessments:profitlossstatement-detail", [profit_loss_statement.pk]
        )
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(200, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("manual_net_profit", response_dict)
        self.assertEqual(
            {"currency": "XYZ", "amount": "100"}, response_dict["manual_net_profit"]
        )

    def test_set_net_profit(self):
        """
        It should be possible to set net profit through api
        """
        profit_loss_statement = ProfitLossStatementFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            assessment__assessmentassignments__assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        data = {"manual_net_profit": 100}
        url = reverse(
            "assessments:profitlossstatement-detail", [profit_loss_statement.pk]
        )
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        response_dict = json.loads(response.content)
        self.assertEqual(200, response.status_code)
        profit_loss_statement.refresh_from_db()
        self.assertEqual(100, profit_loss_statement.manual_net_profit.amount)

    def test_all_costs_of_sales_null(self):
        """
        all_costs_of_sales should accept null values
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor, currency="EUR"
        )
        years = sorted(list(range(2010, 2015)), reverse=True)
        for year in years:
            ProfitLossStatementFactory.create(assessment=assessment, year=year)
        url = reverse("assessments:assessment-detail", (assessment.pk,))
        # Get assessment detail
        assessment_response = json.loads(self.client.get(url).content)
        # Check that all_costs_of_sales exists and is empty
        self.assertIn("all_costs_of_sales", list(assessment_response.keys()))
        all_costs_of_sales = assessment_response["all_costs_of_sales"]
        self.assertDictEqual({}, all_costs_of_sales)
        # Check that the really are no cost of sales objects
        self.assertEqual(0, CostOfSale.objects.count())
        # Create some costs of sales
        names = ("a", "b", "c", "d")
        years = sorted(list(range(2010, 2015)), reverse=True)
        assessment_response = self.client.patch(
            url,
            data=json.dumps(
                {
                    "all_costs_of_sales": dict(
                        (name, [{"year": year, "value": None} for year in years])
                        for name in names[1:]
                    )
                }
            ),
            content_type="application/json",
        )
        # Check status code
        self.assertEqual(200, assessment_response.status_code)
        # Check that objects were created
        self.assertEqual(15, CostOfSale.objects.count())
        assessment_response = json.loads(assessment_response.content)
        # Check that all_costs_of_sales exists and contains the created objects
        self.assertIn("all_costs_of_sales", list(assessment_response.keys()))
        all_costs_of_sales = assessment_response["all_costs_of_sales"]
        self.assertListEqual(list(names[1:]), list(all_costs_of_sales.keys()))
        for name in names[1:]:
            costs_of_sales = all_costs_of_sales[name]
            for year, cost_of_sale in zip(years, costs_of_sales):
                self.assertIn("year", list(cost_of_sale.keys()))
                self.assertEqual(year, cost_of_sale["year"])
                self.assertDictContainsSubset({"value": None}, cost_of_sale)

    def test_all_expenses_null(self):
        """
        all_expenses should accept null values
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor, currency="EUR"
        )
        years = sorted(list(range(2010, 2015)), reverse=True)
        for year in years:
            ProfitLossStatementFactory.create(assessment=assessment, year=year)
        url = reverse("assessments:assessment-detail", (assessment.pk,))
        # Get assessment detail
        assessment_response = json.loads(self.client.get(url).content)
        # Check that all_expenses exists and is empty
        self.assertIn("all_expenses", list(assessment_response.keys()))
        all_expenses = assessment_response["all_expenses"]
        self.assertDictEqual({}, all_expenses)
        # Check that the really are no expenses objects
        self.assertEqual(0, Expense.objects.count())
        # Create some expenses
        names = ("a", "b", "c", "d")
        years = sorted(list(range(2010, 2015)), reverse=True)
        assessment_response = self.client.patch(
            url,
            data=json.dumps(
                {
                    "all_expenses": dict(
                        (name, [{"year": year, "value": None} for year in years])
                        for name in names[1:]
                    )
                }
            ),
            content_type="application/json",
        )
        # Check status code
        self.assertEqual(200, assessment_response.status_code)
        # Check that objects were created
        self.assertEqual(15, Expense.objects.count())
        assessment_response = json.loads(assessment_response.content)
        # Check that all_expenses exists and contains the created objects
        self.assertIn("all_expenses", list(assessment_response.keys()))
        all_expenses = assessment_response["all_expenses"]
        self.assertListEqual(list(names[1:]), list(all_expenses.keys()))
        for name in names[1:]:
            expenses = all_expenses[name]
            for year, expense in zip(years, expenses):
                self.assertIn("year", list(expense.keys()))
                self.assertEqual(year, expense["year"])
                self.assertDictContainsSubset({"value": None}, expense)

    def test_all_costs_of_sales_amount_null(self):
        """
        all_costs_of_sales amount should accept null values
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor, currency="EUR"
        )
        years = sorted(list(range(2010, 2015)), reverse=True)
        for year in years:
            ProfitLossStatementFactory.create(assessment=assessment, year=year)
        url = reverse("assessments:assessment-detail", (assessment.pk,))
        # Get assessment detail
        assessment_response = json.loads(self.client.get(url).content)
        # Check that all_costs_of_sales exists and is empty
        self.assertIn("all_costs_of_sales", list(assessment_response.keys()))
        all_costs_of_sales = assessment_response["all_costs_of_sales"]
        self.assertDictEqual({}, all_costs_of_sales)
        # Check that the really are no cost of sales objects
        self.assertEqual(0, CostOfSale.objects.count())
        # Create some costs of sales
        names = ("a", "b", "c", "d")
        years = sorted(list(range(2010, 2015)), reverse=True)
        assessment_response = self.client.patch(
            url,
            data=json.dumps(
                {
                    "all_costs_of_sales": dict(
                        (
                            name,
                            [
                                {
                                    "year": year,
                                    "value": {"amount": None, "currency": "EUR"},
                                }
                                for year in years
                            ],
                        )
                        for name in names[1:]
                    )
                }
            ),
            content_type="application/json",
        )
        # Check status code
        self.assertEqual(200, assessment_response.status_code)
        # Check that objects were created
        self.assertEqual(15, CostOfSale.objects.count())
        assessment_response = json.loads(assessment_response.content)
        # Check that all_costs_of_sales exists and contains the created objects
        self.assertIn("all_costs_of_sales", list(assessment_response.keys()))
        all_costs_of_sales = assessment_response["all_costs_of_sales"]
        self.assertListEqual(list(names[1:]), list(all_costs_of_sales.keys()))
        for name in names[1:]:
            costs_of_sales = all_costs_of_sales[name]
            for year, cost_of_sale in zip(years, costs_of_sales):
                self.assertIn("year", list(cost_of_sale.keys()))
                self.assertEqual(year, cost_of_sale["year"])
                self.assertDictContainsSubset({"value": None}, cost_of_sale)

    def test_all_expenses_amount_null(self):
        """
        all_expenses amount should accept null values
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor, currency="EUR"
        )
        years = sorted(list(range(2010, 2015)), reverse=True)
        for year in years:
            ProfitLossStatementFactory.create(assessment=assessment, year=year)
        url = reverse("assessments:assessment-detail", (assessment.pk,))
        # Get assessment detail
        assessment_response = json.loads(self.client.get(url).content)
        # Check that all_expenses exists and is empty
        self.assertIn("all_expenses", list(assessment_response.keys()))
        all_expenses = assessment_response["all_expenses"]
        self.assertDictEqual({}, all_expenses)
        # Check that the really are no expenses objects
        self.assertEqual(0, Expense.objects.count())
        # Create some expenses
        names = ("a", "b", "c", "d")
        years = sorted(list(range(2010, 2015)), reverse=True)
        assessment_response = self.client.patch(
            url,
            data=json.dumps(
                {
                    "all_expenses": dict(
                        (
                            name,
                            [
                                {
                                    "year": year,
                                    "value": {"amount": None, "currency": "EUR"},
                                }
                                for year in years
                            ],
                        )
                        for name in names[1:]
                    )
                }
            ),
            content_type="application/json",
        )
        # Check status code
        self.assertEqual(200, assessment_response.status_code)
        # Check that objects were created
        self.assertEqual(15, Expense.objects.count())
        assessment_response = json.loads(assessment_response.content)
        # Check that all_expenses exists and contains the created objects
        self.assertIn("all_expenses", list(assessment_response.keys()))
        all_expenses = assessment_response["all_expenses"]
        self.assertListEqual(list(names[1:]), list(all_expenses.keys()))
        for name in names[1:]:
            expenses = all_expenses[name]
            for year, expense in zip(years, expenses):
                self.assertIn("year", list(expense.keys()))
                self.assertEqual(year, expense["year"])
                self.assertDictContainsSubset({"value": None}, expense)

    def test_can_delete(self):
        """
        Fin Assessor should be able to delete the PL statement
        """
        profit_loss_statement = ProfitLossStatementFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            assessment__assessmentassignments__assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        url = reverse(
            "assessments:profitlossstatement-detail", [profit_loss_statement.pk]
        )
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(204, response.status_code)
        self.assertEqual(0, ProfitLossStatement.objects.count())

    def test_can_delete_double_role(self):
        """
        Assessor should be able to delete the PL statement, when the assessor
        is both financial assessor and regular assessor and the regular kind
        has been submitted.
        """
        profit_loss_statement = ProfitLossStatementFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            assessment__assessmentassignments__assigned_as=AssessmentAssignment.AS_ASSESSOR,
        )
        assignment = profit_loss_statement.assessment.assessmentassignments.get()
        AssessmentAssignmentFactory.create(
            assessment=assignment.assessment,
            assessor=assignment.assessor,
            assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        assignment.locked_for_assessor = True
        assignment.locked_for_employee = False
        assignment.save()
        url = reverse(
            "assessments:profitlossstatement-detail", [profit_loss_statement.pk]
        )
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(204, response.status_code)
        self.assertEqual(0, ProfitLossStatement.objects.count())

    def test_can_not_delete(self):
        """
        Regular Assessor should not be able to delete the PL statement
        """
        profit_loss_statement = ProfitLossStatementFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            assessment__assessmentassignments__assigned_as=AssessmentAssignment.AS_ASSESSOR,
        )
        url = reverse(
            "assessments:profitlossstatement-detail", [profit_loss_statement.pk]
        )
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(403, response.status_code)
        self.assertEqual(1, ProfitLossStatement.objects.count())

    def test_can_not_delete_double_role(self):
        """
        Assessor should not be able to delete the PL statement, when the assessor
        is both financial assessor and regular assessor and the financial kind
        has been submitted.
        """
        profit_loss_statement = ProfitLossStatementFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            assessment__assessmentassignments__assigned_as=AssessmentAssignment.AS_ASSESSOR,
        )
        assignment = profit_loss_statement.assessment.assessmentassignments.get()
        fin_assignment = AssessmentAssignmentFactory.create(
            assessment=assignment.assessment,
            assessor=assignment.assessor,
            assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        fin_assignment.locked_for_assessor = True
        fin_assignment.locked_for_employee = False
        fin_assignment.save()
        url = reverse(
            "assessments:profitlossstatement-detail", [profit_loss_statement.pk]
        )
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(403, response.status_code)
        self.assertEqual(1, ProfitLossStatement.objects.count())

    def test_costs_of_sales_empty_dict(self):
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor, currency="EUR"
        )
        years = sorted(list(range(2014, 2019)), reverse=True)
        for year in years:
            ProfitLossStatementFactory.create(assessment=assessment, year=year)
        url = reverse("assessments:assessment-detail", (assessment.pk,))
        data = {
            "all_costs_of_sales": {
                "Costos de Venta": [
                    {"value": None, "year": 2018},
                    {"value": None, "year": 2017},
                    {"value": None, "year": 2016},
                    {"value": {"amount": 72943818}, "year": 2015},
                    {"value": {}, "year": 2014},
                ]
            }
        }
        assessment_response = self.client.patch(
            url, data=json.dumps(data), content_type="application/json"
        )
        # Check status code
        self.assertEqual(200, assessment_response.status_code)
        # Check that objects were created
        self.assertEqual(5, CostOfSale.objects.count())
        expected_values = [
            {
                "name": "Costos de Venta",
                "value": None,
                "profit_loss_statement__year": 2014,
            },
            {
                "name": "Costos de Venta",
                "value": Decimal("72943818.00"),
                "profit_loss_statement__year": 2015,
            },
            {
                "name": "Costos de Venta",
                "value": None,
                "profit_loss_statement__year": 2016,
            },
            {
                "name": "Costos de Venta",
                "value": None,
                "profit_loss_statement__year": 2017,
            },
            {
                "name": "Costos de Venta",
                "value": None,
                "profit_loss_statement__year": 2018,
            },
        ]
        real_values = list(
            CostOfSale.objects.order_by("profit_loss_statement__year").values(
                "name", "value", "profit_loss_statement__year"
            )
        )
        self.assertListEqual(expected_values, real_values)
