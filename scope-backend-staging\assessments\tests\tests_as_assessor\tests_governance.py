import json

from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import GovernanceFactory
from libs.test_helpers import AssessorJ<PERSON>TTestCase, DenormMixin


class GovernanceTestCase(DenormMixin, AssessorJWTTestCase):
    def test_patch_to_list_throws_405(self):
        url = reverse("assessments:governance-list")
        data = {}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_405_METHOD_NOT_ALLOWED, response.status_code)

    def test_auto_created_visible(self):
        """
        auto_created should be visible in api
        """
        governance = GovernanceFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:governance-detail", [governance.pk])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("auto_created", response_dict)

    def test_auto_created_read_only(self):
        """
        auto_created should be should be read-only in api
        """
        governance = GovernanceFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:governance-detail", [governance.pk])
        self.assertFalse(governance.auto_created)
        data = {"auto_created": True}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code, response.content)
        governance.refresh_from_db()
        self.assertFalse(governance.auto_created)
