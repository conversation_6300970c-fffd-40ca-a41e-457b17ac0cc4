# Generated by Django 3.1.14 on 2022-10-26 09:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0417_auto_20220831_1121"),
    ]

    operations = [
        migrations.CreateModel(
            name="InputValidationLimits",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("name", models.TextField()),
                ("value", models.PositiveIntegerField()),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
