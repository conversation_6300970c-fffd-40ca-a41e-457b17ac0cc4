# Generated by Django 1.10.4 on 2016-12-27 19:18


from django.db import migrations


def fill_temp_available(apps, schema_editor):
    DocumentAvailabilityResponse = apps.get_model(
        "assessments", "DocumentAvailabilityResponse"
    )
    value_map = {True: "yes", False: "no", None: "not relevant"}
    for obj in DocumentAvailabilityResponse.objects.all():
        obj.temp_available = value_map[obj.available]
        obj.save()


class Migration(migrations.Migration):

    dependencies = [("assessments", "0246_documentavailabilityresponse_temp_available")]

    operations = [migrations.RunPython(fill_temp_available)]
