# Generated by Django 2.1.13 on 2019-12-03 10:18

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0387_auto_20191202_1133")]

    operations = [
        migrations.AddField(
            model_name="assessmentevaluation",
            name="global_quality_issues",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="assessmentevaluation",
            name="rating",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="assessmentevaluation",
            name="assessment",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="evaluation",
                to="assessments.Assessment",
            ),
        ),
    ]
