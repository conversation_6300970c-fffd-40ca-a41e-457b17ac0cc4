from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [("assessments", "0113_auto_20150218_1019")]

    operations = [
        migrations.AlterModelOptions(
            name="assessment", options={"ordering": ("-date",)}
        ),
        migrations.AlterModelOptions(
            name="assessmentassignment", options={"ordering": ("-assessment__date",)}
        ),
        migrations.AlterModelOptions(
            name="assessmentpurpose", options={"ordering": ("name",)}
        ),
        migrations.AlterModelOptions(name="banktype", options={"ordering": ("name",)}),
        migrations.AlterModelOptions(
            name="incashinkindtype", options={"ordering": ("name",)}
        ),
        migrations.AlterModelOptions(
            name="purposetype", options={"ordering": ("name",)}
        ),
        migrations.AlterModelOptions(
            name="repaymentstatustype", options={"ordering": ("name",)}
        ),
        migrations.AlterModelOptions(name="service", options={"ordering": ("name",)}),
    ]
