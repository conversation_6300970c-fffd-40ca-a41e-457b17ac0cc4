import json
from decimal import ROUND_HALF_UP, Decimal

from denorm import flush
from django.core.exceptions import ValidationError
from django.db import models
from moneyed import Money
from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import (
    AssessmentFactory,
    BalanceSheetFactory,
    CostOfSaleFactory,
    ExpenseFactory,
    FinancialScoreFactory,
    FinancialScoresFactory,
    ProfitLossStatementFactory,
)
from assessments.models import (
    AssessmentAssignment,
    BalanceSheet,
    FinancialScore,
    FinancialScores,
    ProfitLossStatement,
)
from assessments.tasks import build_response_tree_task
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class FinancialTestCase(DenormMixin, AssessorJWTTestCase):
    maxDiff = None

    def testBalanceSheetDenorms(self):
        # Create balance sheet
        balance_sheet = BalanceSheetFactory.create(
            assessment__currency="EUR",
            cash=Money(1, "EUR"),
            account_receivables=Money(1, "EUR"),
            other_receivables=Money(1, "EUR"),
            inventories=Money(1, "EUR"),
            other_current_assets=Money(1, "EUR"),
            fixed_assets=Money(1, "EUR"),
            intangible_assets=Money(1, "EUR"),
            goodwill=Money(1, "EUR"),
            other_non_current_assets=Money(1, "EUR"),
            accounts_payable=Money(1, "EUR"),
            short_term_loans=Money(1, "EUR"),
            overdrafts=Money(1, "EUR"),
            income_tax_payable=Money(1, "EUR"),
            short_term_provisions=Money(1, "EUR"),
            other_current_liabilities=Money(1, "EUR"),
            long_term_loans=Money(1, "EUR"),
            deferred_tax=Money(1, "EUR"),
            provisions=Money(1, "EUR"),
            other_non_current_liabilities=Money(1, "EUR"),
            share_capital=Money(1, "EUR"),
            share_premium=Money(1, "EUR"),
            retained_earnings=Money(1, "EUR"),
            grants=Money(1, "EUR"),
            statutory_legal_reserves=Money(1, "EUR"),
            other_reserves=Money(1, "EUR"),
            other=Money(1, "EUR"),
        )
        # Fetch balance sheet from database
        balance_sheet = BalanceSheet.objects.get(pk=balance_sheet.pk)
        # Check denorms
        self.assertEqual(balance_sheet.total_current_assets, Money(5, "EUR"))
        self.assertEqual(balance_sheet.total_non_current_assets, Money(4, "EUR"))
        self.assertEqual(balance_sheet.total_assets, Money(9, "EUR"))
        self.assertEqual(balance_sheet.total_current_liabilities, Money(6, "EUR"))
        self.assertEqual(balance_sheet.total_non_current_liabilities, Money(4, "EUR"))
        self.assertEqual(balance_sheet.total_liabilities, Money(10, "EUR"))
        self.assertEqual(balance_sheet.net_assets, Money(-1, "EUR"))
        self.assertEqual(balance_sheet.total_equity, Money(7, "EUR"))

    def testBalanceSheetDenormsWithMissingValues(self):
        # Create balance sheet
        balance_sheet = BalanceSheetFactory.create(
            assessment__currency="EUR",
            cash=Money(1, "EUR"),
            inventories=Money(1, "EUR"),
            other_current_assets=Money(1, "EUR"),
            fixed_assets=Money(1, "EUR"),
            intangible_assets=Money(1, "EUR"),
            other_non_current_assets=Money(1, "EUR"),
        )
        # Fetch balance sheet from database
        balance_sheet = BalanceSheet.objects.get(pk=balance_sheet.pk)
        # Check denorms
        self.assertEqual(balance_sheet.total_current_assets, Money(3, "EUR"))
        self.assertEqual(balance_sheet.total_non_current_assets, Money(3, "EUR"))
        self.assertEqual(balance_sheet.total_assets, Money(6, "EUR"))

    def testBalanceSheetDenormsWithMissingTotal(self):
        # Create balance sheet
        balance_sheet = BalanceSheetFactory.create(
            assessment__currency="EUR",
            fixed_assets=Money(1, "EUR"),
            intangible_assets=Money(1, "EUR"),
            other_non_current_assets=Money(1, "EUR"),
        )
        # Fetch balance sheet from database
        balance_sheet = BalanceSheet.objects.get(pk=balance_sheet.pk)
        # Check denorms
        self.assertEqual(balance_sheet.total_current_assets, None)
        self.assertEqual(balance_sheet.total_non_current_assets, Money(3, "EUR"))
        self.assertEqual(balance_sheet.total_assets, Money(3, "EUR"))
        self.assertEqual(balance_sheet.net_assets, Money(3, "EUR"))

    def testBalanceSheetDenormsWithNoValues(self):
        # Create balance sheet
        balance_sheet = BalanceSheetFactory.create()
        # Fetch balance sheet from database
        balance_sheet = BalanceSheet.objects.get(pk=balance_sheet.pk)
        # Check denorms
        self.assertEqual(balance_sheet.total_current_assets, None)
        self.assertEqual(balance_sheet.total_non_current_assets, None)
        self.assertEqual(balance_sheet.total_assets, None)

    def testProfitLossDenorms(self):
        # Create profit loss statement
        profit_loss_statement = ProfitLossStatementFactory.create(
            assessment__currency="EUR",
            direct_sales=Money(1, "EUR"),
            indirect_sales=Money(1, "EUR"),
            other_income=Money(1, "EUR"),
            depreciation_and_amortization=Money(1, "EUR"),
            interest_expense=Money(1, "EUR"),
            income_tax_expense=Money(1, "EUR"),
            earnings_from_discontinued_operations=Money(1, "EUR"),
        )
        # Create multiple costs of sales
        for i in range(5):
            CostOfSaleFactory.create(
                profit_loss_statement=profit_loss_statement, value=Money(1, "EUR")
            )
        # Create multiple expenses
        for i in range(5):
            ExpenseFactory.create(
                profit_loss_statement=profit_loss_statement, value=Money(1, "EUR")
            )
        # Flush denormalizations
        flush()
        # Fetch profit loss statement from database
        profit_loss_statement = ProfitLossStatement.objects.get(
            pk=profit_loss_statement.pk
        )
        # Check denorms
        self.assertEqual(profit_loss_statement.total_sales, Money(2, "EUR"))
        self.assertEqual(profit_loss_statement.total_cost_of_sales, Money(5, "EUR"))
        self.assertEqual(profit_loss_statement.gross_profit, Money(-3, "EUR"))
        self.assertEqual(profit_loss_statement.total_expenses, Money(5, "EUR"))
        self.assertEqual(profit_loss_statement.ebitda, Money(-7, "EUR"))
        self.assertEqual(profit_loss_statement.ebit, Money(-8, "EUR"))
        self.assertEqual(
            profit_loss_statement.income_from_continuing_operations, Money(-10, "EUR")
        )
        self.assertEqual(profit_loss_statement.net_profit, Money(-9, "EUR"))

    def testFinancialScoreTotalScore(self):
        """
        FinancialScore.total_score should be computed correctly
        """
        # Create FinancialScore object with all values set
        full_financial_score = FinancialScoreFactory.create(
            status=Decimal("1"), trend=Decimal("2"), volatility=Decimal("3")
        )
        # Check that clean does not throw an error
        full_financial_score.clean()
        # Flush denormalizations
        flush()
        # Refetch from db
        full_financial_score = FinancialScore.objects.get(pk=full_financial_score.pk)
        # Check denorm
        expected_value = (
            Decimal("0.5") * Decimal("1")
            + Decimal("0.25") * Decimal("2")
            + Decimal("0.25") * Decimal("3")
        ).quantize(Decimal("0.1"), rounding=ROUND_HALF_UP)
        self.assertEqual(full_financial_score.total_score, expected_value)
        # Create FinancialScore object with only status set
        status_financial_score = FinancialScore.objects.create(status=Decimal("2"))
        # Check that clean does not throw an error
        status_financial_score.clean()
        # Flush denormalizations
        flush()
        # Refetch from db
        status_financial_score = FinancialScore.objects.get(
            pk=status_financial_score.pk
        )
        # Check denorm
        expected_value = Decimal("2.0")
        self.assertEqual(status_financial_score.total_score, expected_value)
        # Create FinancialScore object with status and trend set
        status_trend_financial_score = FinancialScore(
            status=Decimal("1"), trend=Decimal("2")
        )
        # Check that clean throws an error
        self.assertRaisesMessage(
            ValidationError,
            "Either both trend and volatility need to be filled, or neither",
            status_trend_financial_score.clean,
        )
        # Create FinancialScore object with status and volatility set
        status_volatility_financial_score = FinancialScore(
            status=Decimal("1"), volatility=Decimal("2")
        )
        # Check that clean throws an error
        self.assertRaisesMessage(
            ValidationError,
            "Either both trend and volatility need to be filled, or neither",
            status_volatility_financial_score.clean,
        )

    def test_balancesheet_create_without_year_should_autogenerate_year(self):
        """
        When a balancesheet is created without a year, it should autofill the year field
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor,
            assessmentassignments__assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        data = {
            "assessment": "http://testserver/assessments/assessments/{}/".format(
                assessment.pk
            ),
            "assessor": "http://testserver/hrm/assessors/{}/".format(self.assessor.pk),
        }
        url = reverse("assessments:balancesheet-list")
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_cashflowstatement_create_without_year_should_autogenerate_year(self):
        """
        When a cashflowstatement is created without a year, it should autofill the year field
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor,
            assessmentassignments__assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        data = {
            "assessment": "http://testserver/assessments/assessments/{}/".format(
                assessment.pk
            ),
            "assessor": "http://testserver/hrm/assessors/{}/".format(self.assessor.pk),
        }
        url = reverse("assessments:cashflowstatement-list")
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_profitlossstatement_create_without_year_should_autogenerate_year(self):
        """
        When a profitlossstatement is created without a year, it should autofill the year field
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor,
            assessmentassignments__assigned_as=AssessmentAssignment.AS_FINANCIAL_SPECIALIST,
        )
        data = {
            "assessment": "http://testserver/assessments/assessments/{}/".format(
                assessment.pk
            ),
            "assessor": "http://testserver/hrm/assessors/{}/".format(self.assessor.pk),
        }
        url = reverse("assessments:profitlossstatement-list")
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_financialscores_should_exist(self):
        """
        A full FinancialScores object should have been created when the assessment was created
        """
        assessment = AssessmentFactory.create(build_response_tree_completed=False)
        build_response_tree_task(assessment.pk)
        try:
            financialscores = assessment.financial_scores
        except FinancialScores.DoesNotExist:
            financialscores = None
        self.assertNotEqual(financialscores, None)
        for field in FinancialScores._meta.get_fields():
            if (
                isinstance(field, models.OneToOneField)
                and field.related_model == FinancialScore
            ):
                financialscore = getattr(financialscores, field.name)
                self.assertNotEqual(financialscore, None)

    def test_balancesheets_should_exist(self):
        """
        5 balancesheet objects should have been created when the assessment was created
        """
        assessment = AssessmentFactory.create(build_response_tree_completed=False)
        build_response_tree_task(assessment.pk)
        self.assertEqual(6, assessment.balancesheets.count())

    def test_profitlossstatements_should_exist(self):
        """
        5 profitlossstatement objects should have been created when the assessment was created
        """
        assessment = AssessmentFactory.create(build_response_tree_completed=False)
        build_response_tree_task(assessment.pk)
        self.assertEqual(5, assessment.profitlossstatements.count())

    def test_cashflowstatements_should_exist(self):
        """
        5 cashflowstatement objects should have been created when the assessment was created
        """
        assessment = AssessmentFactory.create(build_response_tree_completed=False)
        build_response_tree_task(assessment.pk)
        self.assertEqual(5, assessment.cashflowstatements.count())

    def test_financialratios_should_exist(self):
        """
        5 financialratio objects should have been created when the assessment was created
        """
        assessment = AssessmentFactory.create(build_response_tree_completed=False)
        build_response_tree_task(assessment.pk)
        self.assertEqual(6, assessment.financial_ratios.count())

    def test_financialscores_accepts_full_objects_on_patch(self):
        """
        FinancialScores endpoint should accept full objects for the nested financialscore objects
        PATCH edition
        """
        financial_scores = FinancialScoresFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        data = {
            "assessment": reverse(
                "assessments:assessment-detail", [financial_scores.assessment.pk]
            ),
            "revenue_growth": {"status": "1", "trend": "2", "volatility": "3"},
            "net_profit_growth": {"trend": "2", "volatility": "3"},
            "gross_margin": {"trend": "2"},
        }
        url = reverse("assessments:financialscores-detail", [financial_scores.pk])
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(200, response.status_code)

    def test_can_delete_single_balancesheet(self):
        """
        It should be possible to delete a single balancesheet
        """
        assessment = AssessmentFactory.create()
        BalanceSheetFactory.create_batch(5, assessment=assessment)
        self.assertEqual(5, assessment.balancesheets.count())
        bs_to_delete = assessment.balancesheets.order_by("year")[0]
        bs_to_delete.delete()
        flush()
        self.assertEqual(4, assessment.balancesheets.count())
