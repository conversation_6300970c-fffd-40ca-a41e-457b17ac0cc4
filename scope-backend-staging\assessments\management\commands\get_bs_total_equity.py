import tablib
from django.core.management.base import BaseCommand

from assessments.models import Assessment, BalanceSheet


class Command(BaseCommand):
    help = "Get total equity"

    def handle(self, *args, **options):
        """
        Assessment id
        Assessment tool
        Project id
        Years (ascending)
        """
        filename = "total_equity.xlsx"
        assessments = Assessment.objects.filter(tool__type__requires_accountant=True)

        balance_sheets = BalanceSheet.objects.filter(assessment__in=assessments).filter(
            total_equity__isnull=False
        )
        bs_years = (
            balance_sheets.values_list("year", flat=True).distinct().order_by("-year")
        )

        headers = ["Assessment id", "Assessment tool", "Project id"] + [
            item
            for year in bs_years
            for item in ["{} currency".format(year), "{} amount".format(year)]
        ]

        bs_data = {
            (assessment_id, year): [total_equity_currency, total_equity]
            for (assessment_id, year, total_equity_currency, total_equity) in (
                balance_sheets.values_list(
                    "assessment_id", "year", "total_equity_currency", "total_equity"
                )
            )
        }
        data = [
            [assessment.id, str(assessment.tool), assessment.project_id]
            + [
                item
                for year in bs_years
                for item in bs_data.get((assessment.id, year), ["-", "-"])
            ]
            for assessment in assessments
        ]
        with open(filename, "wb") as f:
            f.write(tablib.Dataset(*data, headers=headers).xlsx)
