from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0106_assessment_progress")]

    operations = [
        migrations.AlterField(
            model_name="bankaccount",
            name="bank_type",
            field=models.TextField(),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="granthistory",
            name="funder_type",
            field=models.TextField(),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="granthistory",
            name="in_cash_in_kind",
            field=models.TextField(),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="loanhistory",
            name="financier",
            field=models.TextField(),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="loanhistory",
            name="purpose",
            field=models.TextField(),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="loanhistory",
            name="repayment_status",
            field=models.TextField(),
            preserve_default=True,
        ),
    ]
