import json

from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import ProductFactory, ProductionSaleFactory
from assessments.models import Product
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class ProductTestCase(DenormMixin, AssessorJWTTestCase):
    def test_product_create_does_get_or_create(self):
        product = ProductFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        data = {
            "name": product.name,
            "unit": product.unit,
            "assessment": reverse(
                "assessments:assessment-detail", [product.assessment_id]
            ),
        }
        url = reverse("assessments:product-list")
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(1, Product.objects.count())
        self.assertEqual(
            status.HTTP_201_CREATED, response.status_code, response.content
        )
        response_dict = json.loads(response.content)
        required_keys = set(["url", "name", "unit"])
        self.assertSetEqual(required_keys, set(response_dict.keys()) & required_keys)

    def test_get_primary_production_in_product(self):
        """
        primary_production field on product object should exist
        """
        product = ProductFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:product-detail", [product.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("primary_production", response_dict)

    def test_patch_primary_production_in_product(self):
        """
        primary_production field on production sales table should be patchable
        """
        product = ProductFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        data = {"primary_production": True}
        url = reverse("assessments:product-detail", [product.pk])
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        product.refresh_from_db()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(product.primary_production)

    def test_get_gross_margins_in_production_sales(self):
        """
        gross_margins field on production sales table should exist
        """
        production_sale = ProductionSaleFactory.create(
            product__assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:productionsale-detail", [production_sale.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertIn("gross_margin", response_dict)

    def test_patch_gross_margins_in_production_sales_with_positive_integer(self):
        """
        gross_margins field on production sales table should be patchable
        """
        production_sale = ProductionSaleFactory.create(
            gross_margin=0,
            product__assessment__assessmentassignments__assessor=self.assessor,
        )
        data = {"gross_margin": 10}
        url = reverse("assessments:productionsale-detail", [production_sale.pk])
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        production_sale.refresh_from_db()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(10, production_sale.gross_margin)

    def test_patch_gross_margins_in_production_sales_with_negative_integer(self):
        """
        gross_margins field on production sales table should be patchable with a negative integer
        """
        production_sale = ProductionSaleFactory.create(
            gross_margin=0,
            product__assessment__assessmentassignments__assessor=self.assessor,
        )
        data = {"gross_margin": -10}
        url = reverse("assessments:productionsale-detail", [production_sale.pk])
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        production_sale.refresh_from_db()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(-10, production_sale.gross_margin)

    def test_no_information_available_fields_default_false(self):
        """
        The *_no_information_available fields should default to False

        productionpurchases_no_information_available
        otherproductionpurchases_no_information_available
        productionsales_no_information_available
        productionfigures_no_information_available
        input_purchases_no_information_available
        """
        product = ProductFactory.create()
        self.assertFalse(product.productionpurchases_no_information_available)
        self.assertFalse(product.otherproductionpurchases_no_information_available)
        self.assertFalse(product.productionsales_no_information_available)
        self.assertFalse(product.productionfigures_no_information_available)
        self.assertFalse(product.input_purchases_no_information_available)

    def test_completed_fields_default_false(self):
        """
        The *_completed fields should default to False

        productionpurchases_completed
        otherproductionpurchases_completed
        productionsales_completed
        productionfigures_completed
        input_purchases_completed
        """
        product = ProductFactory.create()
        self.assertFalse(product.productionpurchases_completed)
        self.assertFalse(product.otherproductionpurchases_completed)
        self.assertFalse(product.productionsales_completed)
        self.assertFalse(product.productionfigures_completed)
        self.assertFalse(product.input_purchases_completed)

    def test_can_patch_no_information_available(self):
        """
        The *_no_information_available fields should be patchable

        productionpurchases_no_information_available
        otherproductionpurchases_no_information_available
        productionsales_no_information_available
        productionfigures_no_information_available
        input_purchases_no_information_available
        """
        product = ProductFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:product-detail", [product.pk])
        data = {
            "productionpurchases_no_information_available": True,
            "otherproductionpurchases_no_information_available": True,
            "productionsales_no_information_available": True,
            "productionfigures_no_information_available": True,
            "input_purchases_no_information_available": True,
        }
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code, response.content)
        product.refresh_from_db()
        self.assertTrue(product.productionpurchases_no_information_available)
        self.assertTrue(product.otherproductionpurchases_no_information_available)
        self.assertTrue(product.productionsales_no_information_available)
        self.assertTrue(product.productionfigures_no_information_available)
        self.assertTrue(product.input_purchases_no_information_available)

    def test_can_patch_completed(self):
        """
        The *_completed fields should be patchable

        productionpurchases_completed
        otherproductionpurchases_completed
        productionsales_completed
        productionfigures_completed
        input_purchases_completed
        """
        product = ProductFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:product-detail", [product.pk])
        data = {
            "productionpurchases_completed": True,
            "otherproductionpurchases_completed": True,
            "productionsales_completed": True,
            "productionfigures_completed": True,
            "input_purchases_completed": True,
        }
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code, response.content)
        product.refresh_from_db()
        self.assertTrue(product.productionpurchases_completed)
        self.assertTrue(product.otherproductionpurchases_completed)
        self.assertTrue(product.productionsales_completed)
        self.assertTrue(product.productionfigures_completed)
        self.assertTrue(product.input_purchases_completed)
