import datetime
import json

import pytz
from denorm import flush
from freezegun import freeze_time
from rest_framework.reverse import reverse

from assessments.factories import FinancialStrategyFactory
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class FinancialStrategyTestCase(DenormMixin, AssessorJWTTestCase):
    login_time = datetime.datetime(2028, 11, 11, tzinfo=pytz.utc)

    def test_patch_triggers_200_on_assessment(self):
        start_time = datetime.datetime(2028, 11, 11, tzinfo=pytz.utc)
        modified_time = datetime.datetime(2028, 11, 12, tzinfo=pytz.utc)
        patch_time = datetime.datetime(2028, 11, 13, tzinfo=pytz.utc)
        modified_since = modified_time.strftime("%a, %d %b %Y %H:%M:%S GMT")
        with freeze_time(start_time):
            financial_strategy = FinancialStrategyFactory.create(
                assessment__assessmentassignments__assessor=self.assessor
            )
            flush()
        with freeze_time(patch_time):
            financial_strategy_url = reverse(
                "assessments:financialstrategy-detail", [financial_strategy.pk]
            )
            response = self.client.patch(
                financial_strategy_url,
                json.dumps({"business_surplus": 12}),
                content_type="application/json",
            )
            self.assertEqual(200, response.status_code)
        url = reverse(
            "assessments:assessment-detail", [financial_strategy.assessment_id]
        )
        response = self.client.get(
            url, content_type="application/json", HTTP_IF_MODIFIED_SINCE=modified_since
        )
        self.assertEqual(200, response.status_code)
