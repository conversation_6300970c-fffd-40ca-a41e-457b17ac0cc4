# Generated by Django 3.1.14 on 2022-02-08 10:57

from django.db import migrations


def update_question_type(apps, schema_editor):
    CanonicalSubQuestion = apps.get_model("canonical", "CanonicalSubQuestion")

    for subquestion in CanonicalSubQuestion.objects.filter(
        title="Does your organization have sufficient staff to achieve its annual plan and/or targets?"
    ):
        CanonicalSubQuestion.objects.filter(id=subquestion.id).update(type="mpc")


class Migration(migrations.Migration):

    dependencies = [
        ("canonical", "0004_auto_20220201_1220"),
    ]

    operations = [migrations.RunPython(update_question_type)]
