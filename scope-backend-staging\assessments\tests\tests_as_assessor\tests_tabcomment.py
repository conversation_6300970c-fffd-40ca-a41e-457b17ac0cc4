import json

from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import (
    AssessmentFactory,
    AssessmentTabCommentFactory,
    BalanceSheetTabCommentFactory,
    CashflowTabCommentFactory,
    DocumentationTabCommentFactory,
    FinanceTabCommentFactory,
    FinancialHistoryTabCommentFactory,
    FinancialOverviewTabCommentFactory,
    FinancialPerformanceTabCommentFactory,
    FinancialProductionTabCommentFactory,
    GovernanceTabCommentFactory,
    MonthlyProductionTabCommentFactory,
    ObservationsTabCommentFactory,
    OrganisationalTabCommentFactory,
    ProductionTabCommentFactory,
    ProfitLossTabCommentFactory,
    TermsAndConditionsTabCommentFactory,
    ValueChainTabCommentFactory,
)
from assessments.models import (
    AssessmentTabComment,
    BalanceSheetTabComment,
    CashflowTabComment,
    DocumentationTabComment,
    FinanceTabComment,
    FinancialHistoryTabComment,
    FinancialOverviewTabComment,
    FinancialPerformanceTabComment,
    FinancialProductionTabComment,
    GovernanceTabComment,
    MonthlyProductionTabComment,
    ObservationsTabComment,
    OrganisationalTabComment,
    ProductionTabComment,
    ProfitLossTabComment,
    TermsAndConditionsTabComment,
    ValueChainTabComment,
)
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class TabCommentTestCase(DenormMixin, AssessorJWTTestCase):
    def create_via_url(self, urlname, klass):
        url = reverse(urlname)
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        data = {
            "assessment": reverse("assessments:assessment-detail", [assessment.pk]),
            "user": reverse("accounts:user-detail", [self.jwt_user.pk]),
            "contents": "Lorem Ipsum",
        }
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_201_CREATED, response.status_code)
        self.assertEqual(1, klass.objects.count())

    def test_can_comment_on_tabs(self):
        cases = [
            ("assessments:assessmenttabcomment-list", AssessmentTabComment),
            ("assessments:organisationaltabcomment-list", OrganisationalTabComment),
            ("assessments:valuechaintabcomment-list", ValueChainTabComment),
            ("assessments:financetabcomment-list", FinanceTabComment),
            ("assessments:financialhistorytabcomment-list", FinancialHistoryTabComment),
            (
                "assessments:financialoverviewtabcomment-list",
                FinancialOverviewTabComment,
            ),
            (
                "assessments:financialproductiontabcomment-list",
                FinancialProductionTabComment,
            ),
            (
                "assessments:financialperformancetabcomment-list",
                FinancialPerformanceTabComment,
            ),
            ("assessments:profitlosstabcomment-list", ProfitLossTabComment),
            ("assessments:balancesheettabcomment-list", BalanceSheetTabComment),
            ("assessments:cashflowtabcomment-list", CashflowTabComment),
            ("assessments:productiontabcomment-list", ProductionTabComment),
            (
                "assessments:monthlyproductiontabcomment-list",
                MonthlyProductionTabComment,
            ),
            ("assessments:governancetabcomment-list", GovernanceTabComment),
            ("assessments:observationstabcomment-list", ObservationsTabComment),
            ("assessments:documentationtabcomment-list", DocumentationTabComment),
            (
                "assessments:termsandconditionstabcomment-list",
                TermsAndConditionsTabComment,
            ),
        ]
        for urlname, klass in cases:
            self.create_via_url(urlname, klass)

    def test_comments_in_assessment(self):
        """
        Comments need to be visible in the assessment object
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        factory_klasses = [
            AssessmentTabCommentFactory,
            OrganisationalTabCommentFactory,
            ValueChainTabCommentFactory,
            FinanceTabCommentFactory,
            FinancialHistoryTabCommentFactory,
            FinancialOverviewTabCommentFactory,
            FinancialProductionTabCommentFactory,
            FinancialPerformanceTabCommentFactory,
            ProfitLossTabCommentFactory,
            BalanceSheetTabCommentFactory,
            CashflowTabCommentFactory,
            ProductionTabCommentFactory,
            MonthlyProductionTabCommentFactory,
            GovernanceTabCommentFactory,
            ObservationsTabCommentFactory,
            DocumentationTabCommentFactory,
            TermsAndConditionsTabCommentFactory,
        ]
        for factory_klass in factory_klasses:
            factory_klass.create_batch(3, assessment=assessment)
        url = reverse("assessments:assessment-detail", [assessment.pk])
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        required_keys = set(
            [
                "assessmenttabcomments",
                "organisationaltabcomments",
                "valuechaintabcomments",
                "financetabcomments",
                "financialhistorytabcomments",
                "financialoverviewtabcomments",
                "financialproductiontabcomments",
                "financialperformancetabcomments",
                "profitlosstabcomments",
                "balancesheettabcomments",
                "cashflowtabcomments",
                "productiontabcomments",
                "monthlyproductiontabcomments",
                "governancetabcomments",
                "observationstabcomments",
                "documentationtabcomments",
                "termsandconditionstabcomments",
            ]
        )
        actual_keys = set(response_dict.keys())
        self.assertSetEqual(required_keys, required_keys & actual_keys)
        for key in required_keys:
            subresponse = response_dict[key]
            self.assertIn("objects", subresponse)
            self.assertEqual(3, len(subresponse["objects"]))
            for item in subresponse["objects"]:
                self.assertIsInstance(item, dict)
                self.assertIn("user", item)
                self.assertIn("contents", item)
                self.assertIn("created_at", item)
