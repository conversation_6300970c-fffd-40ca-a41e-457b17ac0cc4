from django.db import migrations


def move_certifications(apps, schema_editor):
    Product = apps.get_model("assessments", "Product")
    for product in Product.objects.exclude(certification="").exclude(
        certification__isnull=True
    ):
        product.certifications.create(
            name=product.certification,
            status=product.certification_status,
            start_year=product.certification_start_year,
            end_year=product.certification_end_year,
        )


class Migration(migrations.Migration):

    dependencies = [("assessments", "0149_certification")]

    operations = [migrations.RunPython(move_certifications)]
