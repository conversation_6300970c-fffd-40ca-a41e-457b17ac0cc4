# Generated by Django 1.11.12 on 2018-06-20 14:13


from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [("assessments", "0331_auto_20180531_1910")]

    operations = [
        migrations.Remove<PERSON>ield(model_name="enablingplayer", name="contact_email"),
        migrations.RemoveField(model_name="enablingplayer", name="contact_name"),
        migrations.RemoveField(
            model_name="enablingplayer", name="contact_phone_number"
        ),
        migrations.RemoveField(model_name="supplier", name="contact_email"),
        migrations.RemoveField(model_name="supplier", name="contact_name"),
        migrations.RemoveField(model_name="supplier", name="contact_phone_number"),
        migrations.RemoveField(model_name="valuechainplayer", name="contact_email"),
        migrations.RemoveField(model_name="valuechainplayer", name="contact_name"),
        migrations.RemoveField(
            model_name="valuechainplayer", name="contact_phone_number"
        ),
    ]
