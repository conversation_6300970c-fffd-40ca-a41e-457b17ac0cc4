from django.db import migrations, models

import libs.field_helpers


class Migration(migrations.Migration):

    dependencies = [("assessments", "0031_auto_20141228_2126")]

    operations = [
        migrations.CreateModel(
            name="InputPurchase",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("year", models.PositiveSmallIntegerField()),
                ("volume", models.DecimalField(max_digits=8, decimal_places=2)),
                ("price", models.DecimalField(max_digits=8, decimal_places=2)),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="input_purchases",
                        to="assessments.Assessment",
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="InputType",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("name", libs.field_helpers.LowerCaseTextField(unique=True)),
                ("unit", models.TextField()),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Product",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("quality", models.TextField(blank=True)),
                (
                    "land_used_for_product",
                    models.DecimalField(
                        null=True, max_digits=8, decimal_places=2, blank=True
                    ),
                ),
                ("land_unit", models.TextField(blank=True)),
                (
                    "number_of_production_units",
                    models.PositiveIntegerField(null=True, blank=True),
                ),
                (
                    "average_age_of_production_unit",
                    models.DecimalField(
                        null=True, max_digits=8, decimal_places=2, blank=True
                    ),
                ),
                (
                    "average_production_unit_lifetime",
                    models.DecimalField(
                        null=True, max_digits=8, decimal_places=2, blank=True
                    ),
                ),
                (
                    "percent_dead_diseased_production_units",
                    models.DecimalField(
                        null=True, max_digits=5, decimal_places=2, blank=True
                    ),
                ),
                (
                    "number_of_harvest_cycles_per_year",
                    models.PositiveSmallIntegerField(null=True, blank=True),
                ),
                (
                    "percent_irrigated",
                    models.DecimalField(
                        null=True, max_digits=5, decimal_places=2, blank=True
                    ),
                ),
                ("sustainable_production", models.NullBooleanField()),
                ("certification", models.TextField(null=True, blank=True)),
                (
                    "percent_of_members_or_outgrowers_production_sold_to_producing_organization",
                    models.DecimalField(
                        null=True, max_digits=5, decimal_places=2, blank=True
                    ),
                ),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="products",
                        to="assessments.Assessment",
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="ProductionFigure",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("year", models.PositiveSmallIntegerField()),
                ("volume", models.DecimalField(max_digits=8, decimal_places=2)),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="productionfigures",
                        to="assessments.Product",
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="ProductionPurchase",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("year", models.PositiveSmallIntegerField()),
                ("volume", models.DecimalField(max_digits=8, decimal_places=2)),
                ("price", models.DecimalField(max_digits=8, decimal_places=2)),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="productionpurchases",
                        to="assessments.Product",
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="ProductionSale",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("year", models.PositiveSmallIntegerField()),
                ("volume", models.DecimalField(max_digits=8, decimal_places=2)),
                ("price", models.DecimalField(max_digits=8, decimal_places=2)),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="productionsales",
                        to="assessments.Product",
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="ProductType",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("name", models.TextField()),
                ("type", models.TextField()),
                ("production_unit", models.TextField(blank=True)),
                ("unit", models.TextField(blank=True)),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.AlterUniqueTogether(
            name="producttype", unique_together=set([("name", "type")])
        ),
        migrations.AddField(
            model_name="product",
            name="type",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="products",
                to="assessments.ProductType",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="inputpurchase",
            name="type",
            field=models.ForeignKey(
                on_delete=models.CASCADE, to="assessments.InputType"
            ),
            preserve_default=True,
        ),
    ]
