from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0177_auto_20160606_1327")]

    operations = [
        migrations.AddField(
            model_name="product",
            name="operational_month_april",
            field=models.NullBooleanField(),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="product",
            name="operational_month_august",
            field=models.NullBooleanField(),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="product",
            name="operational_month_december",
            field=models.NullBooleanField(),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="product",
            name="operational_month_februari",
            field=models.NullBooleanField(),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="product",
            name="operational_month_january",
            field=models.NullBooleanField(),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="product",
            name="operational_month_july",
            field=models.NullBooleanField(),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="product",
            name="operational_month_june",
            field=models.NullBooleanField(),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="product",
            name="operational_month_march",
            field=models.NullBooleanField(),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="product",
            name="operational_month_may",
            field=models.NullBooleanField(),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="product",
            name="operational_month_november",
            field=models.NullBooleanField(),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="product",
            name="operational_month_october",
            field=models.NullBooleanField(),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="product",
            name="operational_month_september",
            field=models.NullBooleanField(),
            preserve_default=True,
        ),
    ]
