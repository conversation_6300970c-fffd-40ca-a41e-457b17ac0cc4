import datetime
import json
from unittest.mock import patch

import httpretty
from denorm import flush
from denorm.models import DirtyInstance
from django.contrib.auth.hashers import make_password
from django.test import TestCase
from freezegun import freeze_time
from requests.models import Response
from rest_framework.exceptions import PermissionDenied
from rest_framework.reverse import reverse

from accounts.factories import UserFactory
from accounts.models import User
from accounts.serializers import (
    SendToTrainingAssessorSerializer,
    SendToTrainingContactSerializer,
    SendToTrainingEmployeeSerializer,
    SendToTrainingUserSerializer,
)
from customers.factories import ContactFactory, CustomerFactory, RepresentativeFactory
from customers.models import Contact
from hrm.factories import AssessorFactory, EmployeeFactory
from hrm.models import Assessor, Employee
from libs.test_helpers import (
    AssessorJWTTestCase,
    ContactJWTTestCase,
    CustomerAdminJWTTestCase,
    CustomerUserJWTTestCase,
    DenormMixin,
    EmployeeJWTTestCase,
    FinancialSpecialistJWTTestCase,
    QualityReviewerJWTTestCase,
)
from products.factories import ToolTypeFactory


class SendToTrainingTestCase(DenormMixin, EmployeeJWTTestCase):
    fake_api_url = "https://faketraineeapi.scopeinsight.com/users/receive/"

    @classmethod
    def setUpTestData(cls):
        httpretty.register_uri(
            httpretty.POST, cls.fake_api_url, json.dumps({"id": 254})
        )
        httpretty.enable(allow_net_connect=False)
        super().setUpTestData()

    @classmethod
    def tearDownClass(cls):
        super().tearDownClass()
        httpretty.disable()
        httpretty.reset()

    def tearDown(self):
        httpretty.httpretty.latest_requests = []

    def test_url(self):
        url = reverse("accounts:user-send-to-training", [123])
        self.assertEqual(url, "/accounts/users/123/send_to_training/")

    def test_permission_denied_without_remote_url(self):
        user = UserFactory.create()
        with self.assertRaises(PermissionDenied):
            user.send_to_training()

    def test_correct_url_used(self):
        user = UserFactory.create()
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            user.send_to_training()
        self.assertEqual(1, len(httpretty.httpretty.latest_requests))
        request = httpretty.httpretty.latest_requests[0]
        self.assertEqual(
            self.fake_api_url, f"https://{request.headers['Host']}{request.path}"
        )
        self.assertEqual("POST", request.method)

    def test_correct_data_user(self):
        user = UserFactory.create()
        flush()
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            user.send_to_training()
        request = httpretty.httpretty.latest_requests[0]
        expected_data = {
            "training_id": None,
            "username": user.username,
            "password": user.password,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "language": user.language,
            "phone_number": user.phone_number,
            "allowed_frontends": user.allowed_frontends,
            "email_notifications_on": user.email_notifications_on,
            "languages_spoken": user.languages_spoken,
            "street": user.street,
            "street_number": user.street_number,
            "zipcode": user.zipcode,
            "city": user.city,
            "region": user.region,
            "region_iso": user.region_iso,
            "country": user.country,
            "global_region": user.global_region,
            "second_region": user.second_region,
            "display_in_list": user.display_in_list,
        }
        self.assertDictContainsSubset(expected_data, request.parsed_body)

    def test_correct_data_user_previously_sent(self):
        user = UserFactory.create(training_id=789)
        flush()
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            user.send_to_training()
        request = httpretty.httpretty.latest_requests[0]
        expected_data = {"training_id": 789}
        self.assertDictContainsSubset(expected_data, request.parsed_body)

    def test_correct_data_organization(self):
        user = UserFactory.create()
        flush()
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            user.send_to_training()
        request = httpretty.httpretty.latest_requests[0]
        expected_data = {
            "organization": {
                "name": user.organization.name,
                "abbreviation": user.organization.abbreviation,
            }
        }
        self.assertDictContainsSubset(expected_data, request.parsed_body)

    def test_correct_data_employee(self):
        employee = EmployeeFactory.create()
        flush()
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            employee.user.send_to_training()
        request = httpretty.httpretty.latest_requests[0]
        expected_data = {"employee": {"qc_only": employee.qc_only}}
        self.assertDictContainsSubset(expected_data, request.parsed_body)

    def test_correct_data_employee_none(self):
        user = UserFactory.create()
        flush()
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            user.send_to_training()
        request = httpretty.httpretty.latest_requests[0]
        expected_data = {"employee": None}
        self.assertDictContainsSubset(expected_data, request.parsed_body)

    def test_correct_data_contact(self):
        contact = ContactFactory.create()
        flush()
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            contact.user.send_to_training()
        request = httpretty.httpretty.latest_requests[0]
        expected_data = {
            "customer_contact": {
                "position": contact.position,
                "access_to_dashboard": contact.access_to_dashboard,
            }
        }
        self.assertDictContainsSubset(expected_data, request.parsed_body)

    def test_correct_data_contact_none(self):
        user = UserFactory.create()
        flush()
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            user.send_to_training()
        request = httpretty.httpretty.latest_requests[0]
        expected_data = {"customer_contact": None}
        self.assertDictContainsSubset(expected_data, request.parsed_body)

    def test_correct_data_assessor(self):
        assessor = AssessorFactory.create(
            expiration_date_assessor="2019-01-01",
            expiration_date_financial_specialist="2019-01-01",
            is_assessor=True,
            is_active=True,
            tools=ToolTypeFactory.create_batch(2),
        )
        flush()
        assessor.refresh_from_db()
        assessor.user.refresh_from_db()
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            assessor.user.send_to_training()
        request = httpretty.httpretty.latest_requests[0]
        future_date = (datetime.date.today() + datetime.timedelta(days=365)).isoformat()
        expected_data = {
            "assessor": {
                "function": assessor.function,
                "background": assessor.background,
                "comment": assessor.comment,
                "financial_specialist": assessor.financial_specialist,
                "is_assessor": assessor.is_assessor,
                "expiration_date_assessor": future_date,
                "expiration_date_financial_specialist": future_date,
                "tools": [{"name": tooltype.name} for tooltype in assessor.tools.all()],
                "available_to_all_organizations": assessor.available_to_all_organizations,
            }
        }
        self.assertDictContainsSubset(expected_data, request.parsed_body)

    def test_correct_data_financial_specialist(self):
        assessor = AssessorFactory.create(
            expiration_date_assessor="2019-01-01",
            expiration_date_financial_specialist="2019-01-01",
            is_assessor=False,
            financial_specialist=True,
            is_active=True,
            tools=ToolTypeFactory.create_batch(2),
        )
        flush()
        assessor.refresh_from_db()
        assessor.user.refresh_from_db()
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            assessor.user.send_to_training()
        request = httpretty.httpretty.latest_requests[0]
        future_date = (datetime.date.today() + datetime.timedelta(days=365)).isoformat()
        expected_data = {
            "assessor": {
                "function": assessor.function,
                "background": assessor.background,
                "comment": assessor.comment,
                "financial_specialist": assessor.financial_specialist,
                "is_assessor": assessor.is_assessor,
                "expiration_date_assessor": future_date,
                "expiration_date_financial_specialist": future_date,
                "tools": [{"name": tooltype.name} for tooltype in assessor.tools.all()],
                "available_to_all_organizations": assessor.available_to_all_organizations,
            }
        }
        self.assertDictContainsSubset(expected_data, request.parsed_body)

    def test_correct_data_assessor_none(self):
        user = UserFactory.create()
        flush()
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            user.send_to_training()
        request = httpretty.httpretty.latest_requests[0]
        expected_data = {"assessor": None}
        self.assertDictContainsSubset(expected_data, request.parsed_body)

    def test_auth_headers(self):
        user = UserFactory.create()
        flush()
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            with freeze_time("2019-01-01"):
                user.send_to_training()
        request = httpretty.httpretty.latest_requests[0]
        expected_headers = {
            "authorization": 'Signature keyId="default",algorithm="rsa-sha256",signature="TtXyGy67O5v9n0N6S9tWl196ADAm9Dq5Jx8Apft4Y3SCnClSatVtY3rr78lG5uYHe+s9wpM2XYVjYqlCHTJK15Cuu0RbOGepM1ttA5sXJDD/rXirVLaruJClaM+WJRJVU0L/0i00NN7m8ie0l1gT1eg+Lea5JYZkwFWvScNLjfU=",headers="date"',
            "date": "2019-01-01T00:00:00+00:00",
            "content-type": "application/json",
        }
        self.assertDictContainsSubset(expected_headers, dict(request.headers))

    def test_can_access_trainee_tracked(self):
        user = UserFactory.create()
        flush()
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            user.send_to_training()
        user.refresh_from_db()
        self.assertTrue(user.can_access_trainee)

    def test_training_id_tracked(self):
        user = UserFactory.create()
        flush()
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            user.send_to_training()
        user.refresh_from_db()
        self.assertEqual(254, user.training_id)

    def test_update_user_details(self):
        """User fields should sync automatically when changed on prod"""
        organization = CustomerFactory.create()
        user = UserFactory.create(can_access_trainee=True)
        AssessorFactory.create(user=user, is_assessor=True)
        flush()
        user.refresh_from_db()
        with self.settings(
            SEND_TO_TRAINING_URL=self.fake_api_url, ALLOW_SEND_TO_TRAINING=True
        ):
            data = {
                "first_name": "SYNC_first_name",
                "last_name": "SYNC_last_name",
                "language": "es",
                "organization": reverse("customers:customer-detail", [organization.pk]),
                "phone_number": "SYNC_phone_number",
                "allowed_frontends": '[{"app":"smartclient","name":"SCOPE App"},{"app":"dashboard","name":"SCOPE Dashboard"}]',
                "languages_spoken": ["fr"],
                "street": "SYNC_street",
                "street_number": "SYNC_street_number",
                "zipcode": "SYNC_zipcode",
                "city": "SYNC_city",
                "region": "SYNC_region",
                "region_iso": "SYNC_region_iso",
                "country": "SYNC_country",
                "global_region": "SYNC_global_region",
                "second_region": "SYNC_second_region",
                "display_in_list": True,
            }
            url = reverse("accounts:user-detail", [user.pk])
            response = self.client.patch(
                url, json.dumps(data), content_type="application/json"
            )
            response_dict = json.loads(response.content)
            self.assertEqual(200, response.status_code)
        request = httpretty.httpretty.latest_requests[0]
        expected_data = data
        expected_data.pop("organization")
        self.assertDictContainsSubset(expected_data, request.parsed_body)
        self.assertEqual(organization.name, request.parsed_body["organization"]["name"])


class ReceiveFromProdTestCase(DenormMixin, TestCase):
    # notes:

    # return id of user from trainee
    # if sent id is None, use email
    # if email 404s, create
    # if send id is not None, use id
    # if id 404s, create
    #
    #
    # current:
    # id None, email non-existent --> create
    # id None, email exists --> update
    #
    # new:
    # id non-existent, email non-existent --> create done
    # id non-existent, email exists --> error done
    # id null, email exists --> error done
    # id exists, email matches --> update done
    # id exists, email doesn't match, non-existent --> update done
    # id exists, email doesn't match, exists on other user --> error done
    maxDiff = None

    @classmethod
    def setUpTestData(cls):
        cls.url = reverse("accounts:user-receive-from-prod")
        cls.headers = {
            "HTTP_AUTHORIZATION": 'Signature keyId="default",algorithm="rsa-sha256",signature="TtXyGy67O5v9n0N6S9tWl196ADAm9Dq5Jx8Apft4Y3SCnClSatVtY3rr78lG5uYHe+s9wpM2XYVjYqlCHTJK15Cuu0RbOGepM1ttA5sXJDD/rXirVLaruJClaM+WJRJVU0L/0i00NN7m8ie0l1gT1eg+Lea5JYZkwFWvScNLjfU=",headers="date"',
            "HTTP_DATE": "2019-01-01T00:00:00+00:00",
        }
        super().setUpTestData()

    def test_url(self):
        self.assertEqual(self.url, "/accounts/users/receive_from_prod/")

    def test_cannot_get(self):
        response = self.client.get(
            self.url, content_type="application/json", **self.headers
        )
        self.assertEqual(405, response.status_code)

    def test_block_unauthorized(self):
        response = self.client.post(
            self.url, json.dumps({}), content_type="application/json"
        )
        self.assertEqual(401, response.status_code)

    def test_block_incorrect_api_key(self):
        broken_headers = {
            **self.headers,
            "HTTP_AUTHORIZATION": 'Signature keyId="test",algorithm="rsa-sha256",signature="refixTtXyGy67O5v9n0N6S9tWl196ADAm9Dq5Jx8Apft4Y3SCnClSatVtY3rr78lG5uYHe+s9wpM2XYVjYqlCHTJK15Cuu0RbOGepM1ttA5sXJDD/rXirVLaruJClaM+WJRJVU0L/0i00NN7m8ie0l1gT1eg+Lea5JYZkwFWvScNLjfU=",headers="date"',
        }
        response = self.client.post(
            self.url, json.dumps({}), content_type="application/json", **broken_headers
        )
        self.assertEqual(401, response.status_code)

    def test_new_id_new_email(self):
        user = UserFactory.create(password="blubblub")
        flush()
        data = SendToTrainingUserSerializer(user).data
        user.delete()
        response = self.client.post(
            self.url, json.dumps(data), content_type="application/json", **self.headers
        )
        self.assertEqual(201, response.status_code)
        self.assertEqual(1, User.objects.count())
        new_user = User.objects.get()
        self.assertDictEqual({"id": new_user.id}, json.loads(response.content))
        self.assertDictEqual(SendToTrainingUserSerializer(new_user).data, data)
        self.assertTrue(new_user.check_password("blubblub"))

    def test_known_id_same_email(self):
        trainee_user = UserFactory.create(password="blubblub")
        user = UserFactory.create(password="blubblub", training_id=trainee_user.id)
        flush()
        data = SendToTrainingUserSerializer(user).data
        for key, value in data.items():
            if key in ("username", "email"):
                data[key] = trainee_user.email
                continue
            if isinstance(value, (dict, list)):
                continue
            if value is None:
                continue
            if key == "language":
                continue
            if key == "training_id":
                continue
            if key == "email_notifications_on":
                data[key] = not value
                continue
            if key == "display_in_list":
                data[key] = not value
                continue
            if key == "password":
                data[key] = make_password("blabla")
                continue
            data[key] = f"{value}updated"
        response = self.client.post(
            self.url, json.dumps(data), content_type="application/json", **self.headers
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(2, User.objects.count())
        new_user = User.objects.get(pk=trainee_user.id)
        self.assertDictEqual({"id": new_user.id}, json.loads(response.content))
        self.assertDictEqual(SendToTrainingUserSerializer(new_user).data, data)
        self.assertTrue(new_user.check_password("blabla"))

    def test_new_id_known_email(self):
        trainee_user = UserFactory.create(password="blubblub")
        user = UserFactory.create(password="blubblub", training_id=99999)
        flush()
        data = SendToTrainingUserSerializer(user).data
        response = self.client.post(
            self.url, json.dumps(data), content_type="application/json", **self.headers
        )
        self.assertEqual(400, response.status_code)
        self.assertDictContainsSubset(
            {"training_id": ["user with id matching training_id doesn't exist"]},
            json.loads(response.content),
        )

    def test_blank_id_known_email(self):
        trainee_user = UserFactory.create(password="blubblub")
        user = UserFactory.create(password="blubblub")
        flush()
        data = SendToTrainingUserSerializer(user).data
        response = self.client.post(
            self.url, json.dumps(data), content_type="application/json", **self.headers
        )
        self.assertEqual(400, response.status_code)
        self.assertDictContainsSubset(
            {"email": ["different user with same email already exists"]},
            json.loads(response.content),
        )

    def test_known_id_new_email(self):
        trainee_user = UserFactory.create(password="blubblub")
        user = UserFactory.create(password="blubblub", training_id=trainee_user.id)
        flush()
        data = SendToTrainingUserSerializer(user).data
        data["email"] = "<EMAIL>"
        response = self.client.post(
            self.url, json.dumps(data), content_type="application/json", **self.headers
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(2, User.objects.count())
        new_user = User.objects.get(pk=trainee_user.id)
        self.assertEqual("<EMAIL>", new_user.email)

    def test_known_id_email_from_other_user(self):
        trainee_user = UserFactory.create(password="blubblub")
        user = UserFactory.create(password="blubblub", training_id=trainee_user.id)
        flush()
        data = SendToTrainingUserSerializer(user).data
        data["email"] = user.email
        response = self.client.post(
            self.url, json.dumps(data), content_type="application/json", **self.headers
        )
        self.assertEqual(400, response.status_code)
        self.assertDictContainsSubset(
            {"email": ["different user with same email already exists"]},
            json.loads(response.content),
        )

    def test_create_employee(self):
        employee = EmployeeFactory.create()
        flush()
        data = SendToTrainingUserSerializer(employee.user).data
        user = employee.user
        employee.delete()
        user.delete()
        response = self.client.post(
            self.url, json.dumps(data), content_type="application/json", **self.headers
        )
        self.assertEqual(201, response.status_code)
        self.assertEqual(1, Employee.objects.count())
        new_employee = Employee.objects.get()
        self.assertDictEqual(
            SendToTrainingEmployeeSerializer(new_employee).data, data["employee"]
        )

    def test_update_employee(self):
        employee = EmployeeFactory.create()
        flush()
        employee.user.training_id = employee.user.id
        data = SendToTrainingUserSerializer(employee.user).data
        data["employee"]["qc_only"] = not data["employee"]["qc_only"]
        response = self.client.post(
            self.url, json.dumps(data), content_type="application/json", **self.headers
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(1, Employee.objects.count())
        new_employee = Employee.objects.get()
        self.assertDictEqual(
            SendToTrainingEmployeeSerializer(new_employee).data, data["employee"]
        )

    def test_update_user_missing_employee(self):
        employee = EmployeeFactory.create()
        flush()
        employee.user.training_id = employee.user.id
        data = SendToTrainingUserSerializer(employee.user).data
        data["employee"]["qc_only"] = not data["employee"]["qc_only"]
        employee.delete()
        response = self.client.post(
            self.url, json.dumps(data), content_type="application/json", **self.headers
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(1, Employee.objects.count())
        new_employee = Employee.objects.get()
        self.assertDictEqual(
            SendToTrainingEmployeeSerializer(new_employee).data, data["employee"]
        )

    def test_create_contact(self):
        contact = ContactFactory.create()
        flush()
        data = SendToTrainingUserSerializer(contact.user).data
        user = contact.user
        contact.delete()
        user.delete()
        response = self.client.post(
            self.url, json.dumps(data), content_type="application/json", **self.headers
        )
        self.assertEqual(201, response.status_code)
        self.assertEqual(1, Contact.objects.count())
        new_contact = Contact.objects.get()
        self.assertDictEqual(
            SendToTrainingContactSerializer(new_contact).data, data["customer_contact"]
        )

    def test_update_contact(self):
        contact = ContactFactory.create()
        flush()
        contact.user.training_id = contact.user.id
        data = SendToTrainingUserSerializer(contact.user).data
        for key, value in data["customer_contact"].items():
            if key == "access_to_dashboard":
                if data["customer_contact"][key] == "customer_admin":
                    data["customer_contact"][key] = "customer_user"
                else:
                    data["customer_contact"][key] = "customer_admin"
                continue
            data["customer_contact"][key] = f"{value}updated"
        response = self.client.post(
            self.url, json.dumps(data), content_type="application/json", **self.headers
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(1, Contact.objects.count())
        new_contact = Contact.objects.get()
        self.assertDictEqual(
            SendToTrainingContactSerializer(new_contact).data, data["customer_contact"]
        )

    def test_update_user_missing_contact(self):
        contact = ContactFactory.create()
        flush()
        contact.user.training_id = contact.user.id
        data = SendToTrainingUserSerializer(contact.user).data
        for key, value in data["customer_contact"].items():
            if key == "access_to_dashboard":
                if data["customer_contact"][key] == "customer_admin":
                    data["customer_contact"][key] = "customer_user"
                else:
                    data["customer_contact"][key] = "customer_admin"
                continue
            data["customer_contact"][key] = f"{value}updated"
        contact.delete()
        response = self.client.post(
            self.url, json.dumps(data), content_type="application/json", **self.headers
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(1, Contact.objects.count())
        new_contact = Contact.objects.get()
        self.assertDictEqual(
            SendToTrainingContactSerializer(new_contact).data, data["customer_contact"]
        )

    def test_create_assessor(self):
        assessor = AssessorFactory.create(tools=ToolTypeFactory.create_batch(2))
        flush()
        data = SendToTrainingUserSerializer(assessor.user).data
        user = assessor.user
        assessor.delete()
        user.delete()
        response = self.client.post(
            self.url, json.dumps(data), content_type="application/json", **self.headers
        )
        self.assertEqual(201, response.status_code)
        self.assertEqual(1, Assessor.objects.count())
        new_assessor = Assessor.objects.get()
        self.assertDictEqual(
            SendToTrainingAssessorSerializer(new_assessor).data, data["assessor"]
        )

    def test_update_assessor(self):
        assessor = AssessorFactory.create(tools=ToolTypeFactory.create_batch(2))
        flush()
        assessor.user.training_id = assessor.user.id
        data = SendToTrainingUserSerializer(assessor.user).data
        for key, value in data["assessor"].items():
            if isinstance(data["assessor"][key], bool):
                data["assessor"][key] = not data["assessor"][key]
                continue
            if key in (
                "expiration_date_assessor",
                "expiration_date_financial_specialist",
            ):
                data["assessor"][key] = "2025-01-01"
                continue
            if key == "tools":
                data["assessor"][key] = [
                    {"name": tooltype.name}
                    for tooltype in ToolTypeFactory.create_batch(2)
                ]
                continue
            data["assessor"][key] = f"{value}updated"
        response = self.client.post(
            self.url, json.dumps(data), content_type="application/json", **self.headers
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(1, Assessor.objects.count())
        new_assessor = Assessor.objects.get()
        self.assertDictEqual(
            SendToTrainingAssessorSerializer(new_assessor).data, data["assessor"]
        )

    def test_update_user_missing_assessor(self):
        assessor = AssessorFactory.create(tools=ToolTypeFactory.create_batch(2))
        flush()
        assessor.user.training_id = assessor.user.id
        data = SendToTrainingUserSerializer(assessor.user).data
        for key, value in data["assessor"].items():
            if isinstance(data["assessor"][key], bool):
                data["assessor"][key] = not data["assessor"][key]
                continue
            if key in (
                "expiration_date_assessor",
                "expiration_date_financial_specialist",
            ):
                data["assessor"][key] = "2025-01-01"
                continue
            if key == "tools":
                data["assessor"][key] = [
                    {"name": tooltype.name}
                    for tooltype in ToolTypeFactory.create_batch(2)
                ]
                continue
            data["assessor"][key] = f"{value}updated"
        assessor.delete()
        response = self.client.post(
            self.url, json.dumps(data), content_type="application/json", **self.headers
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(1, Assessor.objects.count())
        new_assessor = Assessor.objects.get()
        self.assertDictEqual(
            SendToTrainingAssessorSerializer(new_assessor).data, data["assessor"]
        )

    def test_password_only(self):
        user = UserFactory.create()
        orig_email = user.email
        data = {
            "training_id": user.id,
            "password": "pbkdf2_sha256$120000$AtEirgMH8dE0$6SZftegrNd0ltf5Ha5vQagQSG8525m28BeoZWKVVABw=",
        }
        response = self.client.post(
            self.url, json.dumps(data), content_type="application/json", **self.headers
        )
        self.assertEqual(200, response.status_code)
        user.refresh_from_db()
        self.assertEqual(
            "pbkdf2_sha256$120000$AtEirgMH8dE0$6SZftegrNd0ltf5Ha5vQagQSG8525m28BeoZWKVVABw=",
            user.password,
        )
        self.assertTrue(user.check_password("blub"))
        self.assertEqual(orig_email, user.email)


class ReceiveFromProdEmployeeTestCase(DenormMixin, EmployeeJWTTestCase):
    def test_block_regular_user(self):
        url = reverse("accounts:user-receive-from-prod")
        response = self.client.post(
            url, json.dumps({}), content_type="application/json"
        )
        self.assertEqual(401, response.status_code)


class SetupMixin(object):
    @classmethod
    def setUpTestData(cls):
        cls.target_user = UserFactory.create()
        cls.target_assessor = AssessorFactory.create(
            user=cls.target_user, is_assessor=True
        )
        DirtyInstance.objects.create(content_object=cls.target_user)
        flush()
        cls.url = reverse("accounts:user-send-to-training", [cls.target_user.pk])
        super().setUpTestData()


class NeverAllowedMixin(object):
    def test_cannot_get(self):
        response = self.client.get(self.url, content_type="application/json")
        self.assertEqual(405, response.status_code)


class EmployeeTestCase(NeverAllowedMixin, SetupMixin, DenormMixin, EmployeeJWTTestCase):
    fake_api_url = "https://faketraineeapi.scopeinsight.com/users/receive/"

    def tearDown(self):
        super().tearDown()
        httpretty.disable()
        httpretty.reset()

    def test_cannot_send_to_training_non_prod(self):
        response = self.client.post(
            self.url, json.dumps({}), content_type="application/json"
        )
        self.assertEqual(403, response.status_code)
        response_dict = json.loads(response.content)
        self.assertDictEqual(
            {
                "detail": "Not allowed to send to training from this server",
                "status_code": 403,
            },
            response_dict,
        )

    def test_can_send_to_training_prod(self):
        with self.settings(ALLOW_SEND_TO_TRAINING=True):
            with patch.object(User, "send_to_training", autospec=True) as mock_send:
                mock_response = Response()
                mock_response.status_code = 200
                mock_response._content = json.dumps({"a": "b"})
                mock_send.return_value = mock_response
                response = self.client.post(
                    self.url, json.dumps({}), content_type="application/json"
                )
        self.assertEqual(200, response.status_code)
        mock_send.assert_called_once_with(self.target_user)

    def test_pass_200(self):
        httpretty.register_uri(
            httpretty.POST, self.fake_api_url, json.dumps({"id": 255}), status=200
        )
        httpretty.enable(allow_net_connect=False)
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            with self.settings(ALLOW_SEND_TO_TRAINING=True):
                response = self.client.post(
                    self.url, json.dumps({}), content_type="application/json"
                )
        self.assertEqual(200, response.status_code)
        self.assertDictEqual({"id": 255}, json.loads(response.content))

    def test_pass_201(self):
        httpretty.register_uri(
            httpretty.POST, self.fake_api_url, json.dumps({"id": 256}), status=201
        )
        httpretty.enable(allow_net_connect=False)
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            with self.settings(ALLOW_SEND_TO_TRAINING=True):
                response = self.client.post(
                    self.url, json.dumps({}), content_type="application/json"
                )
        self.assertEqual(201, response.status_code)
        self.assertDictEqual({"id": 256}, json.loads(response.content))

    def test_pass_401(self):
        httpretty.register_uri(
            httpretty.POST,
            self.fake_api_url,
            json.dumps({"something": "here"}),
            status=401,
        )
        httpretty.enable(allow_net_connect=False)
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            with self.settings(ALLOW_SEND_TO_TRAINING=True):
                response = self.client.post(
                    self.url, json.dumps({}), content_type="application/json"
                )
        self.assertEqual(401, response.status_code)
        self.assertDictEqual({"something": "here"}, json.loads(response.content))

    def test_pass_403(self):
        httpretty.register_uri(
            httpretty.POST,
            self.fake_api_url,
            json.dumps({"something": "here"}),
            status=403,
        )
        httpretty.enable(allow_net_connect=False)
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            with self.settings(ALLOW_SEND_TO_TRAINING=True):
                response = self.client.post(
                    self.url, json.dumps({}), content_type="application/json"
                )
        self.assertEqual(403, response.status_code)
        self.assertDictEqual({"something": "here"}, json.loads(response.content))

    def test_pass_500(self):
        httpretty.register_uri(
            httpretty.POST, self.fake_api_url, "<html></html>", status=500
        )
        httpretty.enable(allow_net_connect=False)
        with self.settings(SEND_TO_TRAINING_URL=self.fake_api_url):
            with self.settings(ALLOW_SEND_TO_TRAINING=True):
                response = self.client.post(
                    self.url, json.dumps({}), content_type="application/json"
                )
        self.assertEqual(500, response.status_code)
        self.assertEqual("<html></html>", json.loads(response.content))


class NeverSendBase(NeverAllowedMixin, SetupMixin, DenormMixin):
    def test_cannot_send_to_training_non_prod(self):
        response = self.client.post(
            self.url, json.dumps({}), content_type="application/json"
        )
        self.assertEqual(403, response.status_code)
        response_dict = json.loads(response.content)
        self.assertDictEqual(
            {
                "detail": "You do not have permission to perform this action.",
                "status_code": 403,
            },
            response_dict,
        )

    def test_cannot_send_to_training_prod(self):
        response = self.client.post(
            self.url, json.dumps({}), content_type="application/json"
        )
        self.assertEqual(403, response.status_code)
        response_dict = json.loads(response.content)
        self.assertDictEqual(
            {
                "detail": "You do not have permission to perform this action.",
                "status_code": 403,
            },
            response_dict,
        )


class ContactTestCase(NeverSendBase, ContactJWTTestCase):
    pass


class CustomerAdminTestCase(NeverSendBase, DenormMixin, CustomerAdminJWTTestCase):
    pass


class CustomerUserTestCase(NeverSendBase, DenormMixin, CustomerUserJWTTestCase):
    pass


class AssessorTestCase(NeverSendBase, DenormMixin, AssessorJWTTestCase):
    pass


class FinancialSpecialistTestCase(
    NeverSendBase, DenormMixin, FinancialSpecialistJWTTestCase
):
    pass


class QualityReviewerTestCase(NeverSendBase, DenormMixin, QualityReviewerJWTTestCase):
    pass
