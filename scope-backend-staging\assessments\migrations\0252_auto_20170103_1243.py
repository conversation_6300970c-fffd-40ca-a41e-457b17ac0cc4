# Generated by Django 1.10.4 on 2017-01-03 12:43


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0251_auto_20170103_1223")]

    operations = [
        migrations.AddField(
            model_name="assessment",
            name="bank_accounts_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="bank_accounts_no_information_available",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="basic_financial_infos_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="basic_financial_infos_no_information_available",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="collateral_assets_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.Add<PERSON>ield(
            model_name="assessment",
            name="collateral_assets_no_information_available",
            field=models.<PERSON>oleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="grant_histories_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="grant_histories_no_information_available",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="insurances_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="insurances_no_information_available",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="loan_applications_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="loan_applications_no_information_available",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="loan_histories_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="loan_histories_no_information_available",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="loan_requirements_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="loan_requirements_no_information_available",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="shareholders_completed",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="assessment",
            name="shareholders_no_information_available",
            field=models.BooleanField(default=False),
        ),
    ]
