from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0057_tooltype_requires_accountant"),
        ("customers", "0027_auto_20150907_1018"),
        ("assessments", "0148_auto_20160218_1454"),
    ]

    operations = [
        migrations.AddField(
            model_name="draftassessment",
            name="producing_organization",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="draft_assessments",
                blank=True,
                to="customers.ProducingOrganization",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="draftassessment",
            name="tool",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="draft_assessments",
                blank=True,
                to="products.Tool",
                null=True,
            ),
            preserve_default=True,
        ),
    ]
