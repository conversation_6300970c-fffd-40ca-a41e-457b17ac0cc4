# Generated by Django 3.1.14 on 2022-02-01 11:20

from django.db import migrations


def update_canonical_option_tooltype(apps, schema_editor):
    from canonical.models import CanonicalSubQuestionOption

    for option in CanonicalSubQuestionOption.objects.all():
        option.active_in.set(option.subquestion.active_in.all())
        option.save()


class Migration(migrations.Migration):

    dependencies = [
        ("canonical", "0003_auto_20200610_0820"),
    ]

    operations = [migrations.RunPython(update_canonical_option_tooltype)]
