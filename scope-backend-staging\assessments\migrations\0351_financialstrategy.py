# Generated by Django 1.11.16 on 2018-11-04 18:53


import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0350_product_production_potential_estimate")]

    operations = [
        migrations.CreateModel(
            name="FinancialStrategy",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "business_surplus",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "member_fee_capital",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "loan",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "pre_finance",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "grant",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "other",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "business_surplus_no_information_available",
                    models.BooleanField(default=False),
                ),
                (
                    "member_fee_capital_no_information_available",
                    models.BooleanField(default=False),
                ),
                ("loan_no_information_available", models.BooleanField(default=False)),
                (
                    "pre_finance_no_information_available",
                    models.BooleanField(default=False),
                ),
                ("grant_no_information_available", models.BooleanField(default=False)),
                ("other_no_information_available", models.BooleanField(default=False)),
                (
                    "total",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        editable=False,
                        max_digits=5,
                        null=True,
                    ),
                ),
                (
                    "assessment",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="financial_strategy",
                        to="assessments.Assessment",
                    ),
                ),
            ],
            options={"abstract": False},
        )
    ]
