import datetime
import json

from django.conf import settings
from django.core import mail
from django.template.loader import get_template
from freezegun import freeze_time
from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import AssessmentFactory, AssessmentInvitationFactory
from customers.factories import ContactFactory
from libs.test_helpers import AssessorJWTTestCase, DenormMixin
from messaging.factories import MessageTypeFactory
from projects.factories import ProjectFactory


class InvitationAcceptTestCase(DenormMixin, AssessorJWTTestCase):
    def test_double_accept_throws_400(self):
        invitation = AssessmentInvitationFactory.create(assessor=self.assessor)
        url = reverse("assessments:assessmentinvitation-accept", [invitation.pk])
        response = self.client.post(url, content_type="application/json")
        self.assertEqual(status.HTTP_201_CREATED, response.status_code)
        url = reverse("assessments:assessmentinvitation-accept", [invitation.pk])
        response = self.client.post(url, content_type="application/json")
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual("Invitation was already accepted", response_dict["detail"])

    def test_accept_invalidates_invitation_cache(self):
        """
        Accepting an invitation and subsequently doing a list get should
        return a 200, not a 304
        """
        start = settings.LAST_BREAKING_CODE_CHANGE
        create_time = start + datetime.timedelta(seconds=1)
        fetch_time = start + datetime.timedelta(seconds=2)
        accept_time = start + datetime.timedelta(seconds=3)
        refetch_time = start + datetime.timedelta(seconds=4)
        modified_since_str = fetch_time.strftime("%a, %d %b %Y %H:%M:%S GMT")
        with freeze_time(create_time):
            invitation = AssessmentInvitationFactory.create(assessor=self.assessor)
            AssessmentInvitationFactory.create(assessor=self.assessor)
        with freeze_time(fetch_time):
            list_url = reverse("assessments:assessmentinvitation-list") + "?status=new"
            response = self.client.get(list_url, content_type="application/json")
            self.assertEqual(status.HTTP_200_OK, response.status_code)
        with freeze_time(accept_time):
            url = reverse("assessments:assessmentinvitation-accept", [invitation.pk])
            response = self.client.post(url, content_type="application/json")
            self.assertEqual(status.HTTP_201_CREATED, response.status_code)
        with freeze_time(refetch_time):
            response = self.client.get(
                list_url,
                content_type="application/json",
                HTTP_IF_MODIFIED_SINCE=modified_since_str,
            )
            self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_decline_invalidates_invitation_cache(self):
        """
        Accepting an invitation and subsequently doing a list get should
        return a 200, not a 304
        """
        start = settings.LAST_BREAKING_CODE_CHANGE
        create_time = start + datetime.timedelta(seconds=1)
        fetch_time = start + datetime.timedelta(seconds=2)
        decline_time = start + datetime.timedelta(seconds=3)
        refetch_time = start + datetime.timedelta(seconds=4)
        modified_since_str = fetch_time.strftime("%a, %d %b %Y %H:%M:%S GMT")
        with freeze_time(create_time):
            invitation = AssessmentInvitationFactory.create(assessor=self.assessor)
            AssessmentInvitationFactory.create(assessor=self.assessor)
        with freeze_time(fetch_time):
            list_url = reverse("assessments:assessmentinvitation-list") + "?status=new"
            response = self.client.get(list_url, content_type="application/json")
            self.assertEqual(status.HTTP_200_OK, response.status_code)
        with freeze_time(decline_time):
            url = reverse("assessments:assessmentinvitation-detail", [invitation.pk])
            data = {"status": "declined"}
            response = self.client.patch(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(status.HTTP_200_OK, response.status_code)
        with freeze_time(refetch_time):
            response = self.client.get(
                list_url,
                content_type="application/json",
                HTTP_IF_MODIFIED_SINCE=modified_since_str,
            )
            self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_decline_invitation_email_check(self):
        """
        Declining invitation sends correct email
        """
        MessageTypeFactory.create(slug="invite_declined")
        contact = ContactFactory.create(
            access_to_dashboard="customer_admin",
            user__first_name="Project",
            user__last_name="Lead",
            user__email_notifications_on=True,
            user__language="en",
        )
        project = ProjectFactory.create(contact=contact)
        assessment = AssessmentFactory.create(project=project)
        self.assessor.user.email_notifications_on = True
        self.assessor.user.language = "en"
        self.assessor.user.save()
        invitation = AssessmentInvitationFactory.create(
            assessor=self.assessor, assessment=assessment
        )
        url = reverse("assessments:assessmentinvitation-detail", [invitation.pk])
        data = {"status": "declined"}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual(1, len(mail.outbox))
        message = mail.outbox[0]
        self.assertEqual(
            (
                get_template("assessments/email/invite_declined.txt").render(
                    {"invitation": invitation, "ax_type": "assessment"}
                )
            ),
            message.body,
        )

    def test_decline_invitation_without_project(self):
        """
        It should be possible to decline an invitation when the assessment
        does not have a project
        """
        invitation = AssessmentInvitationFactory.create(
            assessor=self.assessor, assessment__project=None
        )
        url = reverse("assessments:assessmentinvitation-detail", [invitation.pk])
        data = {"status": "declined"}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(status.HTTP_200_OK, response.status_code)
