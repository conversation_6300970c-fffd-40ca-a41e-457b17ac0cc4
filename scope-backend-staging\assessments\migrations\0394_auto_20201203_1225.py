# Generated by Django 2.2.17 on 2020-12-03 12:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0393_auto_20201127_1442"),
    ]

    operations = [
        migrations.RenameField(
            model_name="assessment",
            old_name="terms_and_conditions_tab_accepted",
            new_name="data_sharing_consent_tab_accepted",
        ),
        migrations.RenameField(
            model_name="assessment",
            old_name="finance_tab_accepted",
            new_name="finance_history_tab_accepted",
        ),
        migrations.RenameField(
            model_name="assessment",
            old_name="financial_performance_tab_accepted",
            new_name="finance_performance_tab_accepted",
        ),
        migrations.RemoveField(
            model_name="assessment",
            name="governance_tab_accepted",
        ),
        migrations.RemoveField(
            model_name="assessment",
            name="monthly_production_tab_accepted",
        ),
        migrations.RemoveField(
            model_name="assessment",
            name="monthlyproduction_set_completed",
        ),
        migrations.RemoveField(
            model_name="assessment",
            name="monthlyproduction_set_no_information_available",
        ),
        migrations.Add<PERSON>ield(
            model_name="assessment",
            name="finance_overview_tab_accepted",
            field=models.BooleanField(default=True),
        ),
    ]
