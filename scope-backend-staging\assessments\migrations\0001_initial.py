from django.conf import settings
from django.db import migrations, models

import libs.models_helpers


class Migration(migrations.Migration):

    dependencies = [
        ("customers", "__first__"),
        ("products", "__first__"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("hrm", "__first__"),
    ]

    operations = [
        migrations.CreateModel(
            name="Assessment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("date", models.DateField()),
                (
                    "producing_organization",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="assessments",
                        to="customers.ProducingOrganization",
                    ),
                ),
                (
                    "tool",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="assessments",
                        to="products.Tool",
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="AssessmentAssignment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="assignments",
                        to="assessments.Assessment",
                    ),
                ),
                (
                    "assessor",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="assessment_assignments",
                        to="hrm.Assessor",
                    ),
                ),
                (
                    "skill_type",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        to="hrm.AssessmentSkillType",
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="AssessmentComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.Assessment",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE, to=settings.AUTH_USER_MODEL
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="AssessmentDocument",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "file",
                    models.FileField(
                        null=True,
                        upload_to=libs.models_helpers.class_based_upload_to,
                        blank=True,
                    ),
                ),
                ("accepted", models.BooleanField(default=False)),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="documents",
                        to="assessments.Assessment",
                    ),
                ),
                (
                    "quality_controller",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="assessment_documents_quality_controlled",
                        blank=True,
                        to="hrm.Employee",
                        null=True,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="AssessmentDocumentComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "document",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.AssessmentDocument",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE, to=settings.AUTH_USER_MODEL
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="AssessmentEvaluation",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("contents", models.TextField()),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="evaluations",
                        to="assessments.Assessment",
                    ),
                ),
                (
                    "assessor",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="assessment_evaluations",
                        to="hrm.Assessor",
                    ),
                ),
                (
                    "evaluator",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="assessment_evaluations",
                        to="hrm.Employee",
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="AssessmentInvitation",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="invitations",
                        to="assessments.Assessment",
                    ),
                ),
                (
                    "assessor",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="assessment_invitations",
                        to="hrm.Assessor",
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Response",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "score",
                    models.DecimalField(
                        null=True, max_digits=4, decimal_places=3, blank=True
                    ),
                ),
                ("accepted", models.BooleanField(default=False)),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="responses",
                        to="assessments.Assessment",
                    ),
                ),
                (
                    "assessor",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="responses",
                        to="hrm.Assessor",
                    ),
                ),
                (
                    "quality_controller",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="responses_quality_controlled",
                        blank=True,
                        to="hrm.Employee",
                        null=True,
                    ),
                ),
                (
                    "question",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="responses",
                        to="products.Question",
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="ResponseComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "response",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.Response",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE, to=settings.AUTH_USER_MODEL
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="ResponseDocument",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "file",
                    models.FileField(
                        null=True,
                        upload_to=libs.models_helpers.class_based_upload_to,
                        blank=True,
                    ),
                ),
                ("accepted", models.BooleanField(default=False)),
                (
                    "quality_controller",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="response_documents_quality_controlled",
                        blank=True,
                        to="hrm.Employee",
                        null=True,
                    ),
                ),
                (
                    "response",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="documents",
                        to="assessments.Response",
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="ResponseDocumentComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "document",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.ResponseDocument",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE, to=settings.AUTH_USER_MODEL
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="TimesheetEntry",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("start_datetime", models.DateTimeField(null=True, blank=True)),
                ("end_datetime", models.DateTimeField(null=True, blank=True)),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="timesheet_entries",
                        to="assessments.Assessment",
                    ),
                ),
                (
                    "assessor",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="timesheet_entries",
                        to="hrm.Assessor",
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="UnscoredResponse",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("answer", models.TextField()),
                ("accepted", models.BooleanField(default=False)),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="unscored_responses",
                        to="assessments.Assessment",
                    ),
                ),
                (
                    "assessor",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="unscored_responses",
                        to="hrm.Assessor",
                    ),
                ),
                (
                    "quality_controller",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="unscored_responses_quality_controlled",
                        blank=True,
                        to="hrm.Employee",
                        null=True,
                    ),
                ),
                (
                    "question",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="responses",
                        to="products.UnscoredQuestion",
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="UnscoredResponseComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "response",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.UnscoredResponse",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE, to=settings.AUTH_USER_MODEL
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="UnscoredResponseDocument",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "file",
                    models.FileField(
                        null=True,
                        upload_to=libs.models_helpers.class_based_upload_to,
                        blank=True,
                    ),
                ),
                ("accepted", models.BooleanField(default=False)),
                (
                    "quality_controller",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="unscored_response_documents_quality_controlled",
                        blank=True,
                        to="hrm.Employee",
                        null=True,
                    ),
                ),
                (
                    "response",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="documents",
                        to="assessments.UnscoredResponse",
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="UnscoredResponseDocumentComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "document",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.UnscoredResponseDocument",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE, to=settings.AUTH_USER_MODEL
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.AlterUniqueTogether(
            name="response", unique_together=set([("assessment", "question")])
        ),
        migrations.AlterUniqueTogether(
            name="assessmentassignment",
            unique_together=set([("assessment", "skill_type")]),
        ),
    ]
