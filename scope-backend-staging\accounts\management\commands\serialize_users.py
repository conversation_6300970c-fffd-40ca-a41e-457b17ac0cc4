import json

from django.core.management.base import BaseCommand, CommandError

from accounts.models import User
from accounts.serializers import SendToTrainingUserSerializer


class Command(BaseCommand):
    help = "Serialize users for import on a different server"

    def add_arguments(self, parser):
        # Positional arguments
        parser.add_argument("email", nargs="+", type=str)

    def handle(self, *args, **options):
        for email in options["email"]:
            if not User.objects.filter(email=email).exists():
                raise CommandError(f"User with email {email} does not exist")
        users = User.objects.filter(email__in=options["email"])
        data = SendToTrainingUserSerializer(users, many=True).data
        print(json.dumps(data))
