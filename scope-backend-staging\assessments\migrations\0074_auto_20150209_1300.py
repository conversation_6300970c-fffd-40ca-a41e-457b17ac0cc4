from django.db import migrations


def move_product_type_info(apps, schema_editor):
    Product = apps.get_model("assessments", "Product")
    for product in Product.objects.all().select_related("old_type"):
        product.name = product.old_type.name
        product.type = product.old_type.type
        product.production_unit = product.old_type.production_unit
        product.unit = product.old_type.unit
        product.save()


class Migration(migrations.Migration):

    dependencies = [("assessments", "0073_auto_20150209_1259")]

    operations = [migrations.RunPython(move_product_type_info)]
