from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0123_auto_20150605_1158")]

    operations = [
        migrations.AlterField(
            model_name="financialratio",
            name="asset_turnover",
            field=models.DecimalField(
                null=True, editable=False, max_digits=10, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="current_ratio",
            field=models.DecimalField(
                null=True, editable=False, max_digits=10, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="days_inventory_outstanding",
            field=models.DecimalField(
                null=True, editable=False, max_digits=10, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="days_sales_outstanding",
            field=models.DecimalField(
                null=True, editable=False, max_digits=10, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
        migrations.Alter<PERSON>ield(
            model_name="financialratio",
            name="debt_coverage_ratio",
            field=models.DecimalField(
                null=True, editable=False, max_digits=10, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="debt_servicing",
            field=models.DecimalField(
                null=True, editable=False, max_digits=10, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="debt_to_assets_ratio",
            field=models.DecimalField(
                null=True, editable=False, max_digits=10, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="debt_to_equity_ratio",
            field=models.DecimalField(
                null=True, editable=False, max_digits=10, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="gross_margin",
            field=models.DecimalField(
                null=True, editable=False, max_digits=10, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="net_profit_growth",
            field=models.DecimalField(
                null=True, editable=False, max_digits=10, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="net_profit_margin",
            field=models.DecimalField(
                null=True, editable=False, max_digits=10, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="operating_profit_margin",
            field=models.DecimalField(
                null=True, editable=False, max_digits=10, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="quick_ratio",
            field=models.DecimalField(
                null=True, editable=False, max_digits=10, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="return_on_assets",
            field=models.DecimalField(
                null=True, editable=False, max_digits=10, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="return_on_capital",
            field=models.DecimalField(
                null=True, editable=False, max_digits=10, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="return_on_equity",
            field=models.DecimalField(
                null=True, editable=False, max_digits=10, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="revenue_growth",
            field=models.DecimalField(
                null=True, editable=False, max_digits=10, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialratio",
            name="working_capital_turnover",
            field=models.DecimalField(
                null=True, editable=False, max_digits=10, decimal_places=1, blank=True
            ),
            preserve_default=True,
        ),
    ]
