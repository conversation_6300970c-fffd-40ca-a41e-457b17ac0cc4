# Generated by Django 2.2.26 on 2022-01-18 11:19

from django.db import migrations


def remove_document_availability_response_from_rapid(apps, schema_editor):
    from assessments.models import DocumentAvailabilityResponse

    for response in DocumentAvailabilityResponse.objects.filter(
        assessment__tool__type__name="SCOPE Rapid"
    ):
        if response.question_title == "Audit report of last 2 years":
            response.delete()


class Migration(migrations.Migration):

    dependencies = [
        ("assessments", "0405_product_land_size_under_production_unit"),
    ]

    operations = [
        migrations.RunPython(remove_document_availability_response_from_rapid)
    ]
