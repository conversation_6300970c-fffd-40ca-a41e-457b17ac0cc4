from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0037_auto_20150101_2100")]

    operations = [
        migrations.AlterField(
            model_name="enablingplayer",
            name="contact",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="enablingplayers",
                blank=True,
                to="customers.Contact",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="valuechainplayer",
            name="contact",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="valuechainplayers",
                blank=True,
                to="customers.Contact",
                null=True,
            ),
            preserve_default=True,
        ),
    ]
