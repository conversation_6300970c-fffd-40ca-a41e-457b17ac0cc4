# Generated by Django 1.11.17 on 2018-12-06 15:47


import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("assessments", "0359_merge_20181206_1102"),
    ]

    operations = [
        migrations.CreateModel(
            name="AssessmentLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("create", "create"),
                            ("update", "update"),
                            ("delete", "delete"),
                        ],
                        max_length=6,
                    ),
                ),
                ("target_model", models.TextField()),
                ("target_id", models.PositiveIntegerField()),
                ("changed_data", models.TextField()),
                (
                    "actor",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assessment_logs",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="logs",
                        to="assessments.Assessment",
                    ),
                ),
            ],
            options={"ordering": ("-created_at", "-pk")},
        )
    ]
