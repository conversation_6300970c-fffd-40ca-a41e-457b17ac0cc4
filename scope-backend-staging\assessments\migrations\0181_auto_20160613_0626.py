import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0180_auto_20160612_1612")]

    operations = [
        migrations.AddField(
            model_name="enablingplayer",
            name="percent_sold_to_customer",
            field=models.DecimalField(
                blank=True,
                null=True,
                max_digits=5,
                decimal_places=2,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(100),
                ],
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="valuechainplayer",
            name="percent_sold_to_customer",
            field=models.DecimalField(
                blank=True,
                null=True,
                max_digits=5,
                decimal_places=2,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(100),
                ],
            ),
            preserve_default=True,
        ),
    ]
