# Generated by Django 1.11.12 on 2018-05-08 14:49


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0319_remove_assessment_status")]

    operations = [
        migrations.AddField(
            model_name="assessment",
            name="status",
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, "Created"),
                    (1, "In progress"),
                    (2, "QC Quality Review"),
                    (3, "QC Assessor"),
                    (4, "Draft"),
                    (5, "Final"),
                ],
                default=0,
                editable=False,
            ),
        ),
        migrations.AddField(
            model_name="assessmentassignment",
            name="status",
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, "Created"),
                    (1, "In progress"),
                    (2, "QC Quality Review"),
                    (3, "QC Assessor"),
                    (4, "Draft"),
                    (5, "Final"),
                ],
                default=1,
                editable=False,
            ),
        ),
    ]
