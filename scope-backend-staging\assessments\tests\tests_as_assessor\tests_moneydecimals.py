import json

from moneyed import Money
from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import AssessmentFactory, BasicFinancialInfoFactory
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class MoneyDecimalsTestCase(DenormMixin, AssessorJWTTestCase):
    """
    Test whether money fields correctly interpret decimals

    No decimal point allowed
    Thousands can be seperated by comma or space,
    remove those characters before handling
    Return 400 if anything other than numbers,
    spaces or commas are in the string
    Never return 500
    """

    # Invalid data cases
    invalid_amounts = [
        "a",  # Letters
        "@#$",  # Special characters
        "100.000.000",  # Points as thousands seperator
        "100.0",  # String with decimal point
        100.0,  # Number with decimal point
        "-a",  # Letters
        "-@#$",  # Special characters
        "-100.000.000",  # Points as thousands seperator
        "-100.0",  # String with decimal point
        -100.0,  # Number with decimal point
    ]
    # Valid data cases
    valid_amounts = ["100000", "100,000", "100 000", 100000]
    valid_negative_amounts = ["-100000", "-100,000", "-100 000", -100000]

    def test_create(self):
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:basicfinancialinfo-list")
        data = {
            "assessment": reverse("assessments:assessment-detail", [assessment.pk]),
            "year": 2016,
            "turnover": {"currency": "EUR"},
        }
        for amount in self.invalid_amounts:
            data["turnover"]["amount"] = amount
            response = self.client.post(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(
                status.HTTP_400_BAD_REQUEST,
                response.status_code,
                "Wrong statuscode ({}) for amount (expected 400): {}".format(
                    response.status_code, repr(amount)
                ),
            )
            response_dict = json.loads(response.content)
            self.assertEqual(
                ["Please input a valid number for amount"],
                response_dict["turnover"],
                "Wrong error message ({}) for amount: {}".format(
                    response_dict["turnover"], repr(amount)
                ),
            )
        for amount in self.valid_amounts:
            data["turnover"]["amount"] = amount
            response = self.client.post(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(
                status.HTTP_201_CREATED,
                response.status_code,
                "Wrong statuscode ({}) for amount (expected 201): {}".format(
                    response.status_code, repr(amount)
                ),
            )
            response_dict = json.loads(response.content)
            self.assertEqual(
                "100000",
                response_dict["turnover"]["amount"],
                "Wrong response value ({}) for amount: {}".format(
                    repr(response_dict["turnover"]["amount"]), repr(amount)
                ),
            )
        for amount in self.valid_negative_amounts:
            data["turnover"]["amount"] = amount
            response = self.client.post(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(
                status.HTTP_201_CREATED,
                response.status_code,
                "Wrong statuscode ({}) for amount (expected 201): {}".format(
                    response.status_code, repr(amount)
                ),
            )
            response_dict = json.loads(response.content)
            self.assertEqual(
                "-100000",
                response_dict["turnover"]["amount"],
                "Wrong response value ({}) for amount: {}".format(
                    repr(response_dict["turnover"]["amount"]), repr(amount)
                ),
            )

    def test_update(self):
        basic_financial_info = BasicFinancialInfoFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse(
            "assessments:basicfinancialinfo-detail", [basic_financial_info.pk]
        )
        data = {"turnover": {"currency": "EUR"}}
        for amount in self.invalid_amounts:
            data["turnover"]["amount"] = amount
            response = self.client.patch(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(
                status.HTTP_400_BAD_REQUEST,
                response.status_code,
                "Wrong statuscode ({}) for amount: {}".format(
                    response.status_code, repr(amount)
                ),
            )
            response_dict = json.loads(response.content)
            self.assertEqual(
                ["Please input a valid number for amount"],
                response_dict["turnover"],
                "Wrong error message ({}) for amount: {}".format(
                    response_dict["turnover"], repr(amount)
                ),
            )
        for amount in self.valid_amounts:
            data["turnover"]["amount"] = amount
            response = self.client.patch(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(
                status.HTTP_200_OK,
                response.status_code,
                "Wrong statuscode ({}) for amount (expected 200): {}".format(
                    response.status_code, repr(amount)
                ),
            )
            response_dict = json.loads(response.content)
            self.assertEqual(
                "100000",
                response_dict["turnover"]["amount"],
                "Wrong response value ({}) for amount: {}".format(
                    repr(response_dict["turnover"]["amount"]), repr(amount)
                ),
            )
            # reset object for next iteration
            basic_financial_info.turnover_amount = 0
            basic_financial_info.save()
        for amount in self.valid_negative_amounts:
            data["turnover"]["amount"] = amount
            response = self.client.patch(
                url, json.dumps(data), content_type="application/json"
            )
            self.assertEqual(
                status.HTTP_200_OK,
                response.status_code,
                "Wrong statuscode ({}) for amount (expected 200): {}".format(
                    response.status_code, repr(amount)
                ),
            )
            response_dict = json.loads(response.content)
            self.assertEqual(
                "-100000",
                response_dict["turnover"]["amount"],
                "Wrong response value ({}) for amount: {}".format(
                    repr(response_dict["turnover"]["amount"]), repr(amount)
                ),
            )
            # reset object for next iteration
            basic_financial_info.turnover_amount = 0
            basic_financial_info.save()

    def test_rounding_old_data_positive_works(self):
        basic_financial_info = BasicFinancialInfoFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            turnover=Money("100.50", "USD"),
        )
        url = reverse(
            "assessments:basicfinancialinfo-detail", [basic_financial_info.pk]
        )
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual("101", response_dict["turnover"]["amount"])

    def test_rounding_old_data_negative_works(self):
        basic_financial_info = BasicFinancialInfoFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            turnover=Money("-100.50", "USD"),
        )
        url = reverse(
            "assessments:basicfinancialinfo-detail", [basic_financial_info.pk]
        )
        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual("-101", response_dict["turnover"]["amount"])
