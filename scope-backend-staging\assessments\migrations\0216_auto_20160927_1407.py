from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "assessments",
            "0215_assessmenttabcomment_balancesheettabcomment_cashflowtabcomment_documentationtabcomment_financetabcom",
        )
    ]

    operations = [
        migrations.AlterField(
            model_name="assessmenttabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="assessmenttabcomments",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="balancesheettabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="balancesheettabcomments",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="cashflowtabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="cashflowtabcomments",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="documentationtabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="documentationtabcomments",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financetabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="financetabcomments",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="financialperformancetabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="financialperformancetabcomments",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="governancetabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="governancetabcomments",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="monthlyproductiontabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="monthlyproductiontabcomments",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="observationstabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="observationstabcomments",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="organisationaltabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="organisationaltabcomments",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="productiontabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="productiontabcomments",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="profitlosstabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="profitlosstabcomments",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="valuechaintabcomment",
            name="assessment",
            field=models.ForeignKey(
                on_delete=models.CASCADE,
                related_name="valuechaintabcomments",
                to="assessments.Assessment",
            ),
            preserve_default=True,
        ),
    ]
