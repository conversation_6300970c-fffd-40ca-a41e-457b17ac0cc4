import json

from rest_framework import status
from rest_framework.reverse import reverse

from accounts.factories import UserFactory
from assessments.factories import AssessmentFactory, ResponseFactory, SubResponseFactory
from assessments.models import Response, SubResponse
from hrm.factories import EmployeeFactory
from libs.test_helpers import DenormMixin, QualityReviewerJWTTestCase
from products.factories import QuestionFactory, SubQuestionFactory, ToolFactory
from projects.factories import ProjectFactory


class AssessmentTestCase(DenormMixin, QualityReviewerJWTTestCase):
    def test_can_see_only_assessments_where_they_are_qr(self):
        """
        (Financial) Quality reviewers can see assessments that they are assigned to, directly or indirectly.
        """
        p1 = ProjectFactory.create()
        p2 = ProjectFactory.create(financial_quality_reviewer=self.jwt_user)
        p3 = ProjectFactory.create(quality_reviewer=self.jwt_user)
        direct_qr = AssessmentFactory.create(project=p1, quality_reviewer=self.jwt_user)
        direct_fqr = AssessmentFactory.create(
            project=p1,
            financial_quality_reviewer=self.jwt_user,
            tool__type__requires_accountant=True,
        )
        indirect_fqr = AssessmentFactory.create(
            project=p2, tool__type__requires_accountant=True
        )
        indirect_qr = AssessmentFactory.create(project=p3)
        # User should not see any of the assessments below
        AssessmentFactory.create()
        AssessmentFactory.create(project=p1)
        AssessmentFactory.create(
            project=p2, financial_quality_reviewer=UserFactory.create()
        )
        AssessmentFactory.create(project=p2, tool__type__requires_accountant=False)
        AssessmentFactory.create(project=p3, quality_reviewer=UserFactory.create())

        url = reverse("assessments:assessment-list")

        response = self.client.get(url, content_type="application/json")
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual(4, response_dict["count"])
        assert {item["id"] for item in response_dict["results"]} == {
            direct_qr.id,
            direct_fqr.id,
            indirect_fqr.id,
            indirect_qr.id,
        }

    def test_qc_only_cannot_add_quality_reviewer(self):
        """
        QC only employees cannot add quality reviewer
        """
        my_project = ProjectFactory.create(quality_reviewer=self.jwt_user)
        assessment = AssessmentFactory.create(project=my_project)
        employee = EmployeeFactory.create()

        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {"quality_reviewer": reverse("accounts:user-detail", [employee.user.pk])}
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        response_dict = json.loads(response.content)
        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)
        self.assertEqual(
            "Not allowed to add/edit Quality Reviewer.", response_dict["detail"]
        )
        assessment.refresh_from_db()
        self.assertIsNone(assessment.quality_reviewer)

    def test_qc_only_cannot_add_financial_quality_reviewer(self):
        """
        QC only employees cannot add quality reviewer
        """
        my_project = ProjectFactory.create(quality_reviewer=self.jwt_user)
        assessment = AssessmentFactory.create(project=my_project)
        employee = EmployeeFactory.create()

        url = reverse("assessments:assessment-detail", [assessment.pk])
        data = {
            "financial_quality_reviewer": reverse(
                "accounts:user-detail", [employee.user.pk]
            ),
            "quality_reviewer": reverse("accounts:user-detail", [employee.user.pk]),
        }
        response = self.client.patch(
            url, json.dumps(data), content_type="application/json"
        )
        response_dict = json.loads(response.content)
        self.assertEqual(status.HTTP_403_FORBIDDEN, response.status_code)
        self.assertEqual(
            "Not allowed to add/edit Quality Reviewer.", response_dict["detail"]
        )
        assessment.refresh_from_db()
        self.assertIsNone(assessment.quality_reviewer)

    def test_decline_all_responses_works(self):
        """
        decline_all_responses should set all responses to accepted=True
        """
        tool = ToolFactory.create()
        questions = QuestionFactory.create_batch(4, section__tool=tool)
        for question in questions:
            SubQuestionFactory.create_batch(4, question=question)
        assessment = AssessmentFactory.create(tool=tool, quality_reviewer=self.jwt_user)
        for question in questions:
            response = ResponseFactory.create(
                assessment=assessment, question=question, accepted=True
            )
            for subquestion in question.subquestions.all():
                SubResponseFactory.create(
                    assessment=assessment, subquestion=subquestion, _response=response
                )
        url = reverse("assessments:assessment-decline-all-responses", [assessment.pk])
        self.client.post(url, content_type="application/json")
        self.assertListEqual(
            [False],
            list(
                Response.objects.all()
                .order_by()
                .values_list("accepted", flat=True)
                .distinct()
            ),
        )
        self.assertListEqual(
            [False],
            list(
                SubResponse.objects.all()
                .order_by()
                .values_list("accepted", flat=True)
                .distinct()
            ),
        )

    def test_accept_all_responses_works(self):
        """
        accept_all_responses should set all responses to accepted=True
        """
        tool = ToolFactory.create()
        questions = QuestionFactory.create_batch(4, section__tool=tool)
        for question in questions:
            SubQuestionFactory.create_batch(4, question=question)
        assessment = AssessmentFactory.create(tool=tool, quality_reviewer=self.jwt_user)
        for question in questions:
            response = ResponseFactory.create(
                assessment=assessment, question=question, accepted=False
            )
            for subquestion in question.subquestions.all():
                SubResponseFactory.create(
                    assessment=assessment, subquestion=subquestion, _response=response
                )
        url = reverse("assessments:assessment-accept-all-responses", [assessment.pk])
        self.client.post(url, content_type="application/json")
        self.assertListEqual(
            [True],
            list(
                Response.objects.all()
                .order_by()
                .values_list("accepted", flat=True)
                .distinct()
            ),
        )
        self.assertListEqual(
            [True],
            list(
                SubResponse.objects.all()
                .order_by()
                .values_list("accepted", flat=True)
                .distinct()
            ),
        )
