from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("hrm", "0009_auto_20150208_1350"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("assessments", "0091_insurance_insurancecomment"),
    ]

    operations = [
        migrations.CreateModel(
            name="Shareholder",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("accepted", models.BooleanField(default=False)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("name", models.TextField(blank=True)),
                ("relation_to_producing_organization", models.TextField(blank=True)),
                (
                    "percent_of_shares",
                    models.DecimalField(
                        null=True, max_digits=5, decimal_places=2, blank=True
                    ),
                ),
                (
                    "shareholder_since_year",
                    models.PositiveSmallIntegerField(null=True, blank=True),
                ),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="shareholders",
                        to="assessments.Assessment",
                    ),
                ),
                (
                    "quality_controller",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="shareholders_quality_controlled",
                        blank=True,
                        to="hrm.Employee",
                        null=True,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="ShareholderComment",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("contents", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "_assessment",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="+",
                        blank=True,
                        editable=False,
                        to="assessments.Assessment",
                        null=True,
                    ),
                ),
                (
                    "shareholder",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="comments",
                        to="assessments.Shareholder",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=models.CASCADE,
                        related_name="shareholdercomments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={"abstract": False},
            bases=(models.Model,),
        ),
    ]
