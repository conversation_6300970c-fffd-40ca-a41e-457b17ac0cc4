# Generated by Django 1.11.15 on 2018-08-20 13:51


import django.db.models.deletion
from django.db import migrations, models

import libs.models_helpers


class Migration(migrations.Migration):

    dependencies = [("assessments", "0336_auto_20180803_1429")]

    operations = [
        migrations.CreateModel(
            name="Image",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "file",
                    models.FileField(
                        blank=True,
                        max_length=500,
                        null=True,
                        upload_to=libs.models_helpers.class_based_upload_to,
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="images",
                        to="assessments.Assessment",
                    ),
                ),
            ],
            options={"ordering": ("pk",), "abstract": False},
        )
    ]
