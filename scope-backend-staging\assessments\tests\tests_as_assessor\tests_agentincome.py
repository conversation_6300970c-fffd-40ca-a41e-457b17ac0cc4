import json

from rest_framework import status
from rest_framework.reverse import reverse

from assessments.factories import AgentIncomeFactory, AssessmentFactory
from assessments.models import AgentIncome
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class AgentIncomeTestCase(DenormMixin, AssessorJWTTestCase):
    def test_agent_income_visible_in_api(self):
        """
        agent_income should be visible on assessment in api
        """
        agent_income = AgentIncomeFactory.create(
            assessment__assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:assessment-detail", [agent_income.assessment_id])
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertIn("agent_income", response_dict)
        agent_income_dict = response_dict["agent_income"]
        self.assertIn("objects", agent_income_dict)
        agent_income_dict = agent_income_dict["objects"][0]
        self.assertSetEqual(
            set(
                [
                    "id",
                    "url",
                    "modified_at",
                    "assessment",
                    "year",
                    "income",
                    "income_usd",
                ]
            ),
            set(agent_income_dict.keys()),
        )

    def test_agent_income_patchable(self):
        """
        agent_income should be patchable through api
        """
        agent_income = AgentIncomeFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            year=2015,
            income=0,
        )
        url = reverse("assessments:agentincome-detail", [agent_income.pk])
        data = {"year": 2016, "income": {"currency": "KES", "amount": 2000}}
        self.client.patch(url, json.dumps(data), content_type="application/json")
        db_obj = AgentIncome.objects.get(pk=agent_income.pk)
        self.assertEqual(2016, db_obj.year)
        self.assertEqual("KES", str(db_obj.income.currency))
        self.assertEqual(2000, db_obj.income.amount)

    def test_agent_income_postable(self):
        """
        agent_income should be postable through api
        """
        assessment = AssessmentFactory.create(
            assessmentassignments__assessor=self.assessor
        )
        url = reverse("assessments:agentincome-list")
        data = {
            "assessment": reverse("assessments:assessment-detail", [assessment.pk]),
            "year": 2016,
            "income": {"currency": "KES", "amount": 2000},
        }
        self.client.post(url, json.dumps(data), content_type="application/json")
        db_obj = AgentIncome.objects.get()
        self.assertEqual(2016, db_obj.year)
        self.assertEqual("KES", str(db_obj.income.currency))
        self.assertEqual(2000, db_obj.income.amount)

    def test_agent_income_deletable(self):
        """
        agent_income should be deletable through api
        """
        agent_income = AgentIncomeFactory.create(
            assessment__assessmentassignments__assessor=self.assessor,
            year=2015,
            income=0,
        )
        url = reverse("assessments:agentincome-detail", [agent_income.pk])
        response = self.client.delete(url, content_type="application/json")
        self.assertEqual(
            status.HTTP_204_NO_CONTENT, response.status_code, response.content
        )
        self.assertEqual(0, AgentIncome.objects.count())
