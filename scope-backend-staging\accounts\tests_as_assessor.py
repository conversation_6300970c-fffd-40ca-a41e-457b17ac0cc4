import json
from unittest import skip

from denorm import flush
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from django.test import TestCase
from freezegun import freeze_time
from rest_framework import status
from rest_framework.reverse import reverse

from accounts.factories import UserFactory
from customers.factories import ContactFactory, CustomerFactory
from hrm.factories import (
    AssessorFactory,
    AssessorTrainingFactory,
    FinancialSpecialistTrainingFactory,
)
from hrm.models import Assessor
from libs.test_helpers import AssessorJWTTestCase, DenormMixin


class UserTestCase(DenormMixin, AssessorJWTTestCase):
    def test_assessor_can_create_inactive_user(self):
        """
        Assessor can create a user, but said user must be inactive
        """
        User = get_user_model()
        customer = CustomerFactory.create()
        url = reverse("accounts:user-list")
        data = {
            "email": "<EMAIL>",
            "organization": reverse("customers:customer-detail", [customer.pk]),
        }
        response = self.client.post(
            url, json.dumps(data), content_type="application/json"
        )
        self.assertEqual(
            status.HTTP_201_CREATED, response.status_code, response.content
        )
        new_user = User.objects.get(email="<EMAIL>")
        self.assertFalse(new_user.is_active)

    def test_role_booleans_work(self):
        """
        The role booleans should be set correctly
        """
        url = reverse("accounts:user-me")
        response = self.client.get(url, content_type="application/json")
        response_dict = json.loads(response.content)
        self.assertDictContainsSubset(
            {
                "is_assessor": True,
                "is_financial_specialist": False,
                "is_employee": False,
                "is_quality_reviewer": False,
                "is_contact": False,
            },
            response_dict,
        )


class LoginTestCase(DenormMixin, TestCase):
    USER_EMAIL = "<EMAIL>"
    USER_PASSWORD = "jwt"
    HTTP_ORIGIN = "http://testserver"

    def setUp(self):
        self.jwt_user = UserFactory.create(email=self.USER_EMAIL, language="en")
        self.jwt_user.set_password(self.USER_PASSWORD)
        self.jwt_user.is_superuser = False
        self.jwt_user.save()
        Group.objects.get_or_create(name="assessors")
        self.assessor = Assessor.objects.create(user=self.jwt_user, is_assessor=True)
        super().setUp()

    def test_can_login_to_app(self):
        """
        It should be possible to log in to app
        """
        me = self.jwt_user
        assessor = Assessor.objects.get(user=me)
        assessor.is_assessor = True
        AssessorTrainingFactory.create(
            assessor=assessor, certification_valid_until="9999-01-01"
        )
        assessor.save()
        flush()
        me.refresh_from_db()
        assessor.refresh_from_db()
        with freeze_time("2019-01-01"):
            response = self.client.post(
                "/api-token-auth/",
                {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
                HTTP_ORIGIN="http://app.testserver",
            )
            self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_can_not_login_to_dashboard(self):
        """
        It should not be possible to log in to dashboard
        """
        response = self.client.post(
            "/api-token-auth/",
            {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)

    def test_assessor_and_fin_spec_can_not_login_to_app_with_expired_account(self):
        """
        User with expired assessor AND financial specialist accounts can NOT log in
        """
        me = self.jwt_user
        assessor = Assessor.objects.get(user=me)
        assessor.is_assessor = True
        assessor.financial_specialist = True
        assessor.save()
        AssessorTrainingFactory.create(
            assessor=assessor, certification_valid_until="1900-01-01"
        )
        FinancialSpecialistTrainingFactory.create(
            assessor=assessor, certification_valid_until="1900-01-01"
        )

        flush()
        me.refresh_from_db()
        assessor.refresh_from_db()
        with freeze_time("2019-01-01"):
            response = self.client.post(
                "/api-token-auth/",
                {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
                HTTP_ORIGIN="http://app.testserver",
            )
            self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)

    def test_assessor_and_not_fin_spec_can_not_login_to_app_with_expired_account(self):
        """
        User with expired financial specialist and VALID assessor accounts can log in
        """
        me = self.jwt_user
        assessor = Assessor.objects.get(user=me)
        assessor.is_assessor = True
        assessor.financial_specialist = True
        assessor.save()
        AssessorTrainingFactory.create(
            assessor=assessor, certification_valid_until="9999-01-01"
        )
        FinancialSpecialistTrainingFactory.create(
            assessor=assessor, certification_valid_until="1900-01-01"
        )

        flush()
        me.refresh_from_db()
        assessor.refresh_from_db()
        with freeze_time("2019-01-01"):
            response = self.client.post(
                "/api-token-auth/",
                {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
                HTTP_ORIGIN="http://app.testserver",
            )
            self.assertEqual(status.HTTP_200_OK, response.status_code)

    def test_no_login_on_expiration_date(self):
        """
        User with expired assessor AND financial specialist accounts can NOT log in
        """
        me = self.jwt_user
        assessor = Assessor.objects.get(user=me)
        assessor.is_assessor = True
        assessor.save()
        AssessorTrainingFactory.create(
            assessor=assessor, certification_valid_until="2019-04-01"
        )
        flush()
        with freeze_time("2019-04-01"):
            response = self.client.post(
                "/api-token-auth/",
                {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
                HTTP_ORIGIN="http://app.testserver",
            )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        self.assertDictEqual(
            {
                "non_field_errors": [
                    "Oops, it looks like your Certification/License has expired! To renew your Certificate / License, please get in touch with your account manager or reach out to <NAME_EMAIL>. We'll be happy to assist you with the renewal process. Thank you!"
                ]
            },
            json.loads(response.content),
        )

    @skip("No portal anymore")
    def test_can_not_login_to_portal(self):
        """
        It should not be possible to log in to portal
        """
        response = self.client.post(
            "/api-token-auth/",
            {"email": self.USER_EMAIL, "password": self.USER_PASSWORD},
            HTTP_ORIGIN="http://portal.testserver",
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)


class ImpersonationTestCase(DenormMixin, AssessorJWTTestCase):
    def test_only_employee_can_impersonate(self):
        """
        Assessor cannot impersonate, because has no access to dashboard
        """
        user = UserFactory.create(email="<EMAIL>")
        ContactFactory.create(user=user, access_to_dashboard="customer_admin")
        response = self.client.post(
            "/accounts/users/impersonate/",
            {"email": "<EMAIL>"},
            HTTP_ORIGIN="http://dashboard.testserver",
        )
        self.assertEqual(status.HTTP_401_UNAUTHORIZED, response.status_code)
        response_dict = json.loads(response.content)
        self.assertEqual("Invalid payload.", response_dict["detail"])
