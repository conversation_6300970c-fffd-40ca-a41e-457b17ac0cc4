import json

from django.core.management.base import BaseCommand

from accounts.models import User


class Command(BaseCommand):
    help = "Save trainee env user id and email in a json file"

    def handle(self, *args, **options):
        trainee_user_details = {"trainee_users": []}
        for user in User.objects.all():
            user_detail = {"id": user.pk, "email": user.email}
            trainee_user_details["trainee_users"].append(user_detail)
        with open("trainee_user_details.json", "w") as json_file:
            json.dump(trainee_user_details, json_file)
