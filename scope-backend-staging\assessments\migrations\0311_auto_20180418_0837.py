# Generated by Django 1.11.9 on 2018-04-18 08:37


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0127_auto_20180320_1028"),
        ("assessments", "0310_merge_20180327_0959"),
    ]

    operations = [
        migrations.AddField(
            model_name="enablingplayer",
            name="product_types_purchased",
            field=models.ManyToManyField(
                blank=True,
                related_name="_enablingplayer_product_types_purchased_+",
                to="products.ProductTypeOption",
            ),
        ),
        migrations.AddField(
            model_name="supplier",
            name="product_types_purchased",
            field=models.ManyToManyField(
                blank=True,
                related_name="_supplier_product_types_purchased_+",
                to="products.ProductTypeOption",
            ),
        ),
        migrations.AddField(
            model_name="valuechainplayer",
            name="product_types_purchased",
            field=models.ManyToManyField(
                blank=True,
                related_name="_valuechainplayer_product_types_purchased_+",
                to="products.ProductTypeOption",
            ),
        ),
    ]
