# Generated by Django 1.11.16 on 2018-11-29 15:05


import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [("assessments", "0341_subresponse_not_relevant_is_hidden")]

    operations = [
        migrations.AlterField(
            model_name="assessment",
            name="_netmonthlyincomeprojection",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="+",
                to="assessments.NetMonthlyIncomeProjection",
            ),
        ),
        migrations.AlterField(
            model_name="assessment",
            name="_totalmonthlyexpensesprojection",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="+",
                to="assessments.TotalMonthlyExpensesProjection",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="assessment",
            name="_totalmonthlyincomeprojection",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="+",
                to="assessments.TotalMonthlyIncomeProjection",
            ),
        ),
        migrations.AlterField(
            model_name="assessment",
            name="assessor_user",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="assessment",
            name="display_financial_quality_reviewer",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="assessment",
            name="display_quality_reviewer",
            field=models.ForeignKey(
                blank=True,
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="assessmentassignment",
            name="assessor",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="assessmentassignments",
                to="hrm.Assessor",
            ),
        ),
        migrations.AlterField(
            model_name="assessmentassignment",
            name="invitation",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to="assessments.AssessmentInvitation",
            ),
        ),
        migrations.AlterField(
            model_name="assessmentinvitation",
            name="assessor",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="assessmentinvitations",
                to="hrm.Assessor",
            ),
        ),
    ]
