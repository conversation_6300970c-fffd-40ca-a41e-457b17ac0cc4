from django.db import migrations


def setup_standard_groups(apps, schema_editor):
    Group = apps.get_model("auth", "Group")
    Group.objects.all().delete()
    group_names = ["admins", "scope_insight_users", "assessors"]
    Group.objects.bulk_create([Group(name=name) for name in group_names])


class Migration(migrations.Migration):

    dependencies = [("accounts", "0004_user_image")]

    operations = [migrations.RunPython(setup_standard_groups)]
