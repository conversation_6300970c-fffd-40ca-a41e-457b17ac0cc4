# Generated by Django 1.11.7 on 2017-11-07 19:21


from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [("assessments", "0297_auto_20171107_1343")]

    operations = [
        migrations.AlterModelOptions(
            name="additionalforestryinfo", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="agentbankaccountloanhistory", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(name="agentincome", options={"ordering": ("pk",)}),
        migrations.AlterModelOptions(
            name="assessmentevaluation", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="assessmentfunder", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="assessmenttabcomment", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="balancesheettabcomment", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(name="bankaccount", options={"ordering": ("pk",)}),
        migrations.AlterModelOptions(
            name="capitalrequirement", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="cashflowtabcomment", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="certification", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="collateralasset", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(name="costofsale", options={"ordering": ("pk",)}),
        migrations.AlterModelOptions(
            name="documentationtabcomment", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="documentavailabilityresponsedocument", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="enablingplayer", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(name="executive", options={"ordering": ("pk",)}),
        migrations.AlterModelOptions(name="expense", options={"ordering": ("pk",)}),
        migrations.AlterModelOptions(
            name="financetabcomment", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="financialperformancetabcomment", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="financialscore", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="financialscores", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="forestryequipment", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="forestryinventory", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="forestryplan", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="generalcheck", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="generalchecks", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="governancetabcomment", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="granthistory", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="inputpurchase", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(name="insurance", options={"ordering": ("pk",)}),
        migrations.AlterModelOptions(name="landuse", options={"ordering": ("pk",)}),
        migrations.AlterModelOptions(
            name="landuseforestry", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="loanapplication", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(name="loanhistory", options={"ordering": ("pk",)}),
        migrations.AlterModelOptions(
            name="loanrequirement", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="monthlyproductiontabcomment", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="netmonthlyincomeprojection", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="nonexecutive", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="observationstabcomment", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="organisationaltabcomment", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="producingorganizationdetails", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="productionfigure", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="productionmargin", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="productionpurchase", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="productionsale", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="productiontabcomment", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="profitlosstabcomment", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(name="ratioscores", options={"ordering": ("pk",)}),
        migrations.AlterModelOptions(
            name="responsedocument", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(name="shareholder", options={"ordering": ("pk",)}),
        migrations.AlterModelOptions(
            name="timesheetentry", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="totallanduse", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="totallanduseforestry", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="totalmonthlyexpensesprojection", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="totalmonthlyincomeprojection", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="unscoredtoolresponse", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="valuechainplayer", options={"ordering": ("pk",)}
        ),
        migrations.AlterModelOptions(
            name="valuechaintabcomment", options={"ordering": ("pk",)}
        ),
    ]
